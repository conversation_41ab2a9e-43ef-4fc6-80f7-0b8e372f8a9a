#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查数据表和爬虫程序的对应关系
"""

import pymysql
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'dict_romte'))
from dict_romte.config import get_db_config

def check_tables_and_programs():
    """检查数据表和爬虫程序的对应关系"""
    try:
        # 检查数据库中的表
        DB_CONFIG = get_db_config('default')
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        cursor.execute("SHOW TABLES LIKE 'dict_zonghe_%'")
        db_tables = [row[0] for row in cursor.fetchall()]
        
        cursor.close()
        conn.close()
        
        print(f"数据库中有 {len(db_tables)} 个dict_zonghe_*表")
        
        # 检查爬虫程序文件
        py_files = [f for f in os.listdir('.') if f.startswith('dict_zonghe_') and f.endswith('.py')]
        print(f"当前有 {len(py_files)} 个爬虫程序")
        
        # 分析对应关系
        missing_tables = []
        for py_file in py_files:
            table_name = py_file.replace('.py', '').lower()
            if table_name not in [t.lower() for t in db_tables]:
                missing_tables.append(table_name)
        
        print(f"\n缺少 {len(missing_tables)} 个数据表:")
        for i, table in enumerate(missing_tables[:10], 1):  # 只显示前10个
            print(f"  {i}. {table}")
        
        if len(missing_tables) > 10:
            print(f"  ... 还有 {len(missing_tables) - 10} 个")
        
        return missing_tables
        
    except Exception as e:
        print(f"[错误] 检查失败: {e}")
        return []

def main():
    print("=" * 60)
    print("检查数据表和爬虫程序的对应关系")
    print("=" * 60)
    
    missing_tables = check_tables_and_programs()
    
    if missing_tables:
        print(f"\n需要为 {len(missing_tables)} 个爬虫程序创建对应的数据表")
        print("建议运行: python create_missing_tables.py")
    else:
        print("\n✅ 所有爬虫程序都有对应的数据表")

if __name__ == "__main__":
    main()
