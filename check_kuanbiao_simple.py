#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单检查kuanbiao视图的字段结构
"""

import pymysql

DB_CONFIG = {
    'host': '*************',
    'port': 12136,
    'user': 'root',
    'password': '5eb9a11916e3a66d',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

try:
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    print('=== 检查kuanbiao视图的字段 ===')
    cursor.execute('DESCRIBE kuanbiao')
    columns = cursor.fetchall()
    
    target_fields = [
        '签约上报日期', '项目编码', '项目名称', '合同含税金额（万元）', 
        '收入侧客户', '收入侧合同编码', '项目建设内容及方案简介（CT）', 
        '项目建设内容及方案简介（IT）', '一级场景', '二级场景', 
        '后向合同签约时间', '成本侧供应商'
    ]
    
    existing_fields = []
    missing_fields = []
    
    column_names = [col[0] for col in columns]
    
    for field in target_fields:
        if field in column_names:
            existing_fields.append(field)
        else:
            missing_fields.append(field)
    
    print('\n=== 存在的字段 ===')
    for field in existing_fields:
        print(f'✓ {field}')
    
    print('\n=== 缺失的字段 ===')
    for field in missing_fields:
        print(f'✗ {field}')
    
    # 简单检查数据量
    print('\n=== 基本数据统计 ===')
    cursor.execute('SELECT COUNT(*) FROM kuanbiao LIMIT 1')
    total_count = cursor.fetchone()[0]
    print(f'kuanbiao视图总记录数: {total_count}')
    
    # 检查样本数据
    print('\n=== 样本数据 ===')
    cursor.execute('SELECT 项目编码, 收入侧客户, 成本侧供应商, 一级场景, 二级场景 FROM kuanbiao LIMIT 3')
    results = cursor.fetchall()
    for i, row in enumerate(results):
        print(f'样本{i+1}: 项目={row[0]}, 客户={row[1]}, 供应商={row[2]}, 一级场景={row[3]}, 二级场景={row[4]}')
    
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f'错误: {e}')
