import os
import pymysql
import pandas as pd
from sqlalchemy import create_engine
from datetime import datetime
import shutil
import json

# 目标文件夹
TARGET_FOLDER = "shichang"
IMPORTED_FOLDER = os.path.join(TARGET_FOLDER, "inserted2db")

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'cmcc12345',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

def ensure_folder_exists(folder_path):
    """确保目标文件夹存在"""
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)
        print(f"[信息] 创建目标文件夹: {folder_path}")
    return folder_path

def connect_to_database():
    """连接到MySQL数据库"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        print(f"[成功] 已连接到数据库: {DB_CONFIG['database']}")
        return conn
    except Exception as e:
        print(f"[错误] 连接数据库失败: {e}")
        exit(1)

def check_file_already_imported(conn, filename):
    """检查文件是否已经被导入过数据库"""
    cursor = conn.cursor()
    try:
        sql = "SELECT COUNT(*) FROM import_logs WHERE file_name = %s"
        cursor.execute(sql, (filename,))
        count = cursor.fetchone()[0]
        return count > 0
    except Exception as e:
        print(f"[警告] 检查文件导入记录失败: {e}")
        return False
    finally:
        cursor.close()

def import_market_share_data(conn, filepath, filename):
    """导入市场份额数据到数据库"""
    try:
        print(f"[信息] 正在导入市场份额数据: {filepath}")
        
        # 检查文件是否已经导入过
        if check_file_already_imported(conn, filename):
            print(f"[信息] 文件 {filename} 已经导入过数据库，跳过导入")
            # 将文件移动到已导入文件夹
            move_to_imported_folder(filepath, filename)
            return True

        # 创建SQLAlchemy引擎
        engine_url = f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?charset={DB_CONFIG['charset']}"
        engine = create_engine(engine_url)

        # 读取Excel文件中的"日监控"sheet
        print("[信息] 正在读取'日监控'sheet...")
        df_daily = pd.read_excel(filepath, sheet_name="日监控")
        
        # 读取Excel文件中的"号码明细"sheet
        print("[信息] 正在读取'号码明细'sheet...")
        df_detail = pd.read_excel(filepath, sheet_name="号码明细")
        
        # 检查数据库表结构并调整DataFrame列名
        cursor = conn.cursor()
        cursor.execute("DESCRIBE market_share_detail")
        table_columns = [column[0] for column in cursor.fetchall()]
        cursor.close()
        
        # 打印当前DataFrame的列和数据库表的列，帮助调试
        print("[信息] Excel文件中的列:", df_detail.columns.tolist())
        print("[信息] 数据库表中的列:", table_columns)
        
        # 检查是否需要重命名列
        if '证件' in df_detail.columns and '证件' not in table_columns:
            # 查找数据库中可能对应的列名
            possible_columns = ['身份证', '证件号码', '证件号', 'id_card']
            for col in possible_columns:
                if col in table_columns:
                    print(f"[信息] 将Excel中的'证件'列重命名为数据库中的'{col}'列")
                    df_detail = df_detail.rename(columns={'证件': col})
                    break
        
        # 检查是否有其他不匹配的列
        excel_columns = set(df_detail.columns)
        db_columns = set(table_columns)
        
        # 移除Excel中存在但数据库中不存在的列
        columns_to_drop = excel_columns - db_columns
        if columns_to_drop:
            print(f"[警告] 以下列在Excel中存在但在数据库中不存在，将被忽略: {', '.join(columns_to_drop)}")
            df_detail = df_detail.drop(columns=columns_to_drop)

        # 添加导入时间列，用于后续去重
        import_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        df_daily['导入时间'] = import_time
        df_detail['导入时间'] = import_time
        
        # 确保数据从第二行开始（第一行为表头）
        if len(df_daily) > 0:
            # 导入数据到market_share_daily表
            df_daily.to_sql('market_share_daily', engine, if_exists='append', index=False)
            print(f"[成功] 已导入 {len(df_daily)} 条日监控数据")
        else:
            print("[警告] '日监控'sheet中没有数据")
        
        if len(df_detail) > 0:
            # 导入数据到market_share_detail表
            df_detail.to_sql('market_share_detail', engine, if_exists='append', index=False)
            print(f"[成功] 已导入 {len(df_detail)} 条号码明细数据")
        else:
            print("[警告] '号码明细'sheet中没有数据")

        # 记录导入信息
        cursor = conn.cursor()
        
        # 记录日监控数据导入
        sql = """
        INSERT INTO import_logs (file_name, table_name, record_count, import_time)
        VALUES (%s, %s, %s, %s)
        """
        cursor.execute(sql, (filename, 'market_share_daily', len(df_daily), import_time))

        # 记录号码明细数据导入
        cursor.execute(sql, (filename, 'market_share_detail', len(df_detail), import_time))

        conn.commit()
        cursor.close()

        total_records = len(df_daily) + len(df_detail)
        print(f"[成功] 总共导入 {total_records} 条市场份额数据")
        
        # 将已导入的Excel文件移动到inserted2db文件夹
        move_to_imported_folder(filepath, filename)
        
        return True
    except Exception as e:
        print(f"[错误] 导入市场份额数据失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def move_to_imported_folder(filepath, filename):
    """将已导入数据的Excel文件移动到inserted2db文件夹"""
    try:
        # 确保目标文件夹存在
        ensure_folder_exists(IMPORTED_FOLDER)
        
        # 构建目标路径
        target_path = os.path.join(IMPORTED_FOLDER, filename)
        
        # 如果目标文件已存在，添加时间戳避免冲突
        if os.path.exists(target_path):
            file_name, file_ext = os.path.splitext(filename)
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            new_filename = f"{file_name}_{timestamp}{file_ext}"
            target_path = os.path.join(IMPORTED_FOLDER, new_filename)
        
        # 移动文件
        shutil.move(filepath, target_path)
        print(f"[成功] 已将文件 {filename} 移动到 {IMPORTED_FOLDER} 文件夹")
    except Exception as e:
        print(f"[警告] 移动文件 {filename} 失败: {e}")

def create_tables_if_not_exist(conn):
    """创建必要的数据表（如果不存在）"""
    cursor = conn.cursor()
    try:
        # 创建导入日志表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS import_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            file_name VARCHAR(255),
            table_name VARCHAR(50),
            record_count INT,
            import_time DATETIME
        )
        """)

        # 创建市场份额日监控表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS market_share_daily (
            id INT AUTO_INCREMENT PRIMARY KEY,
            日期 DATE,
            集团名称 VARCHAR(100),
            总证件 INT,
            属于拍照 INT,
            属于非拍照新流入 INT,
            拍照时中山移动 INT,
            截止当日在网 INT,
            截止当日流失 INT,
            拍照后新增 INT,
            其中属于XR INT,
            其中客经 INT,
            其中网格经理 INT,
            其中同一网格渠道 INT,
            其他 INT,
            当日到达 INT,
            拍照宽带 INT,
            当日宽带 INT,
            宽带流失 INT,
            宽带新增 INT,
            宽带到达 INT,
            导入时间 DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        """)

        # 创建市场份额号码明细表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS market_share_detail (
            id INT AUTO_INCREMENT PRIMARY KEY,
            更新日期 DATE,
            号码 VARCHAR(20),
            集团名称 VARCHAR(100),
            新入网归属类型 VARCHAR(50),
            工号 VARCHAR(50),
            渠道编码 VARCHAR(50),
            渠道名称 VARCHAR(100),
            分公司 VARCHAR(50),
            网格 VARCHAR(50),
            是否XR VARCHAR(10),
            是否有宽带 VARCHAR(10),
            是否有高质量合约 VARCHAR(10),
            导入时间 DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        """)

        conn.commit()
        print("[信息] 已确保所需数据表存在")
    except Exception as e:
        conn.rollback()
        print(f"[错误] 创建数据表失败: {e}")
    finally:
        cursor.close()

def main():
    print(f"📊 开始导入市场份额数据到数据库 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 确保目标文件夹存在
    ensure_folder_exists(TARGET_FOLDER)
    ensure_folder_exists(IMPORTED_FOLDER)
    
    # 连接数据库
    db_conn = connect_to_database()
    
    # 创建必要的数据表
    create_tables_if_not_exist(db_conn)
    
    # 检查是否有市场份额文件信息
    market_share_files_path = os.path.join(TARGET_FOLDER, "market_share_files.json")
    
    if os.path.exists(market_share_files_path):
        # 从文件中读取市场份额文件信息
        with open(market_share_files_path, "r", encoding="utf-8") as f:
            market_share_files = json.load(f)
        
        print(f"[信息] 找到 {len(market_share_files)} 个待导入的市场份额文件")
        
        # 导入市场份额数据
        imported_count = 0
        for filepath, filename in market_share_files:
            if os.path.exists(filepath):
                if import_market_share_data(db_conn, filepath, filename):
                    imported_count += 1
            else:
                print(f"[警告] 文件不存在: {filepath}")
        
        print(f"[完成] 共导入 {imported_count} 个市场份额文件到数据库")
        
        # 删除市场份额文件信息文件
        os.remove(market_share_files_path)
    else:
        # 如果没有市场份额文件信息，则扫描目标文件夹
        print("[信息] 未找到市场份额文件信息，扫描目标文件夹...")
        
        # 获取目标文件夹中的所有Excel文件
        excel_files = [f for f in os.listdir(TARGET_FOLDER) if f.endswith('.xlsx') and os.path.isfile(os.path.join(TARGET_FOLDER, f))]
        
        # 筛选出市场份额文件
        import re
        market_share_files = [(os.path.join(TARGET_FOLDER, f), f) for f in excel_files if re.match(r'拍照集团份额\d{8}\.xlsx', f)]
        
        print(f"[信息] 找到 {len(market_share_files)} 个市场份额文件")
        
        # 导入市场份额数据
        imported_count = 0
        for filepath, filename in market_share_files:
            if import_market_share_data(db_conn, filepath, filename):
                imported_count += 1
        
        print(f"[完成] 共导入 {imported_count} 个市场份额文件到数据库")
    
    # 关闭连接
    db_conn.close()
    print("✅ 数据导入完成")

if __name__ == "__main__":
    main()