#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BC_groups_rm2local.py - 远程数据库br_groups表到本地数据库同步脚本
功能：将远程数据库的br_groups表全量复制到本地数据库

表结构说明：
- 主键：id (bigint AUTO_INCREMENT)
- 包含集团编码、名称、份额、目标等业务字段
- 字符集：utf8mb4
- 引擎：InnoDB
"""

import pymysql
import pandas as pd
from datetime import datetime
import sys
import traceback
import time

# 远程数据库配置
REMOTE_DB_CONFIG = {
    'host': '************',
    'port': 20029,
    'user': 'root',
    'password': '306d23a56b324b9f',
    'database': 'brch',  # 请根据实际情况修改数据库名
    'charset': 'utf8mb4'
}

# 本地数据库配置 (使用项目现有配置)
LOCAL_DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'cmcc12345',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

# 目标表名
TABLE_NAME = 'br_groups'

# br_groups表的字段列表（排除自增主键id）
BR_GROUPS_COLUMNS = [
    'id','code', 'name', 'dept_id', 'grid_id', 'user_scale', 'current_share',
    'target_share', 'groups_identify_num', 'company_identify_num', 'company_share',
    'broadband_identify_num', 'broadband_penetrate', 'mobile_improve_type', 
    'mobile_improve_target', 'mobile_net_target', 'mobile_add_target', 
    'mobile_opportunity_target', 'broadband_improve_type', 'broadband_improve_target',
    'broadband_net_target', 'broadband_add_target', 'invite_num', 'visit_num',
    'opportunity_num', 'opportunity_convert_num', 'create_by', 'create_time',
    'update_by', 'update_time', 'del_flag'
]

def connect_to_database(config, db_type="数据库"):
    """连接到数据库"""
    try:
        print(f"[信息] 正在连接{db_type} {config['host']}:{config['port']}...")
        conn = pymysql.connect(**config)
        print(f"[成功] 已连接到{db_type}: {config['database']}")
        return conn
    except Exception as e:
        print(f"[错误] 连接{db_type}失败: {e}")
        return None

def test_table_exists(conn, table_name, db_type="数据库"):
    """检查表是否存在"""
    try:
        cursor = conn.cursor()
        cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
        result = cursor.fetchone()
        cursor.close()
        
        if result:
            print(f"[信息] {db_type}中表 {table_name} 存在")
            return True
        else:
            print(f"[警告] {db_type}中表 {table_name} 不存在")
            return False
    except Exception as e:
        print(f"[错误] 检查{db_type}表存在性失败: {e}")
        return False

def get_table_structure(conn, table_name):
    """获取表结构"""
    try:
        cursor = conn.cursor()
        cursor.execute(f"DESCRIBE {table_name}")
        structure = cursor.fetchall()
        cursor.close()
        return structure
    except Exception as e:
        print(f"[错误] 获取表结构失败: {e}")
        return None

def get_record_count(conn, table_name, db_type="数据库"):
    """获取表记录数"""
    try:
        cursor = conn.cursor()
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        cursor.close()
        print(f"[信息] {db_type}表 {table_name} 当前记录数: {count}")
        return count
    except Exception as e:
        print(f"[错误] 获取{db_type}记录数失败: {e}")
        return 0

def validate_data_sample(remote_conn, local_conn, table_name, sample_size=5):
    """验证数据抽样，确保复制准确性"""
    try:
        print(f"[信息] 正在进行数据抽样验证（样本数: {sample_size}）...")
        
        remote_cursor = remote_conn.cursor()
        local_cursor = local_conn.cursor()
        
        # 从远程表随机抽取样本（排除自增ID）
        columns_str = ', '.join([f"`{col}`" for col in BR_GROUPS_COLUMNS])
        remote_cursor.execute(f"SELECT {columns_str} FROM {table_name} ORDER BY RAND() LIMIT {sample_size}")
        remote_samples = remote_cursor.fetchall()
        
        if not remote_samples:
            print("[信息] 远程表无数据，跳过抽样验证")
            return True
        
        # 验证每个样本在本地表中是否存在
        match_count = 0
        for i, sample in enumerate(remote_samples):
            # 构建WHERE条件（使用关键字段进行匹配）
            where_conditions = []
            where_values = []
            
            # 使用code, name, dept_id作为匹配条件
            key_fields = ['code', 'name', 'dept_id']
            for field in key_fields:
                if field in BR_GROUPS_COLUMNS:
                    field_index = BR_GROUPS_COLUMNS.index(field)
                    if field_index < len(sample) and sample[field_index] is not None:
                        where_conditions.append(f"`{field}` = %s")
                        where_values.append(sample[field_index])
            
            if where_conditions:
                where_clause = " AND ".join(where_conditions)
                local_cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE {where_clause}", where_values)
                local_count = local_cursor.fetchone()[0]
                
                if local_count > 0:
                    match_count += 1
                    print(f"[验证] 样本 {i+1}/{sample_size}: ✅ 匹配")
                else:
                    print(f"[验证] 样本 {i+1}/{sample_size}: ❌ 不匹配")
        
        remote_cursor.close()
        local_cursor.close()
        
        success_rate = match_count / len(remote_samples) * 100
        print(f"[验证结果] 抽样验证成功率: {success_rate:.1f}% ({match_count}/{len(remote_samples)})")
        
        return success_rate >= 80  # 80%以上认为验证通过
        
    except Exception as e:
        print(f"[错误] 数据抽样验证失败: {e}")
        return False

def truncate_local_table(local_conn, table_name):
    """清空本地表数据"""
    try:
        print(f"[信息] 正在清空本地表 {table_name}...")
        cursor = local_conn.cursor()
        
        # 先获取清空前的记录数
        before_count = get_record_count(local_conn, table_name, "本地")
        
        # 执行TRUNCATE
        cursor.execute(f"TRUNCATE TABLE {table_name}")
        local_conn.commit()
        cursor.close()
        
        print(f"[成功] 本地表 {table_name} 已清空 (原有 {before_count} 条记录)")
        return True
    except Exception as e:
        print(f"[错误] 清空本地表失败: {e}")
        local_conn.rollback()
        return False

def create_local_table_if_not_exists(local_conn):
    """如果本地表不存在，则创建br_groups表"""
    try:
        cursor = local_conn.cursor()
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS `br_groups` (
          `id` bigint NOT NULL , 
                    # AUTO_INCREMENT,
          `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '集团编码',
          `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '集团名称',
          `dept_id` bigint DEFAULT NULL COMMENT '所属分公司',
          `grid_id` bigint DEFAULT NULL COMMENT '所属网格',
          `user_scale` int DEFAULT NULL COMMENT '用户规模',
          `current_share` decimal(10,2) DEFAULT NULL COMMENT '当前份额',
          `target_share` decimal(10,2) DEFAULT NULL COMMENT '目标份额',
          `groups_identify_num` int DEFAULT '0' COMMENT '拍照份额-集团证件数',
          `company_identify_num` int DEFAULT '0' COMMENT '拍照份额-我司证件数',
          `company_share` decimal(10,2) DEFAULT NULL COMMENT '拍照份额-我司份额',
          `broadband_identify_num` int DEFAULT '0' COMMENT '拍照份额-宽带证件数',
          `broadband_penetrate` decimal(10,2) DEFAULT NULL COMMENT '拍照份额-宽带渗透率',
          `mobile_improve_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机提升目标-手机提升分类',
          `mobile_improve_target` decimal(10,2) DEFAULT NULL COMMENT '手机提升目标-手机提升目标',
          `mobile_net_target` decimal(10,2) DEFAULT NULL COMMENT '手机提升目标-手机净增目标',
          `mobile_add_target` decimal(10,2) DEFAULT NULL COMMENT '手机提升目标-手机新增目标',
          `mobile_opportunity_target` decimal(10,2) DEFAULT NULL COMMENT '手机提升目标-商机目标',
          `broadband_improve_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '宽带提升目标-宽带提升分类',
          `broadband_improve_target` decimal(10,2) DEFAULT NULL COMMENT '宽带提升目标-宽带提升目标',
          `broadband_net_target` decimal(10,2) DEFAULT NULL COMMENT '宽带提升目标-宽带净增目标',
          `broadband_add_target` decimal(10,2) DEFAULT NULL COMMENT '宽带提升目标-宽带新增目标',
          `invite_num` int DEFAULT '0' COMMENT '邀约数量',
          `visit_num` int DEFAULT '0' COMMENT '拜访数量',
          `opportunity_num` int DEFAULT '0' COMMENT '商机数量',
          `opportunity_convert_num` int DEFAULT '0' COMMENT '商机转化数量',
          `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
          `create_time` datetime DEFAULT NULL COMMENT '创建时间',
          `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
          `update_time` datetime DEFAULT NULL COMMENT '更新时间',
          `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
          PRIMARY KEY (`id`) USING BTREE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        """

        cursor.execute(create_table_sql)
        local_conn.commit()
        cursor.close()
        print("[信息] 本地br_groups表已确保存在")
        return True
    except Exception as e:
        print(f"[错误] 创建本地表失败: {e}")
        local_conn.rollback()
        return False

def copy_data_from_remote(remote_conn, local_conn, table_name, batch_size=1000):
    """从远程数据库复制数据到本地数据库 - 针对br_groups表优化"""
    try:
        print(f"[信息] 开始从远程数据库复制 {table_name} 表数据...")
        start_time = time.time()

        # 获取远程表记录数
        remote_count = get_record_count(remote_conn, table_name, "远程")
        if remote_count == 0:
            print("[警告] 远程表没有数据")
            return True

        # 分批读取和插入数据
        remote_cursor = remote_conn.cursor()
        local_cursor = local_conn.cursor()

        # 使用预定义的列名列表（排除自增主键id）
        columns_str = ', '.join([f"`{col}`" for col in BR_GROUPS_COLUMNS])
        placeholders = ', '.join(['%s'] * len(BR_GROUPS_COLUMNS))

        print(f"[信息] 将复制 {len(BR_GROUPS_COLUMNS)} 个字段（排除自增主键id）")
        print(f"[信息] 预计处理 {remote_count} 条记录，批次大小: {batch_size}")

        # 分批处理数据
        offset = 0
        total_inserted = 0

        while True:
            # 从远程数据库读取一批数据
            select_sql = f"SELECT {columns_str} FROM {table_name} LIMIT {batch_size} OFFSET {offset}"
            remote_cursor.execute(select_sql)
            batch_data = remote_cursor.fetchall()

            if not batch_data:
                break

            # 插入到本地数据库
            insert_sql = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"
            local_cursor.executemany(insert_sql, batch_data)
            local_conn.commit()

            total_inserted += len(batch_data)
            offset += batch_size

            # 计算进度和预估剩余时间
            progress = total_inserted / remote_count * 100
            elapsed_time = time.time() - start_time
            if total_inserted > 0:
                estimated_total_time = elapsed_time * remote_count / total_inserted
                remaining_time = estimated_total_time - elapsed_time
                print(f"[进度] {total_inserted}/{remote_count} ({progress:.1f}%) - "
                      f"已用时: {elapsed_time:.1f}s, 预计剩余: {remaining_time:.1f}s")
            else:
                print(f"[进度] {total_inserted}/{remote_count} ({progress:.1f}%)")

        remote_cursor.close()
        local_cursor.close()

        # 验证复制结果
        local_count = get_record_count(local_conn, table_name, "本地")
        total_time = time.time() - start_time

        if local_count == remote_count:
            print(f"[成功] 数据复制完成! 远程: {remote_count} 条, 本地: {local_count} 条")
            print(f"[统计] 总耗时: {total_time:.2f}秒, 平均速度: {remote_count/total_time:.1f} 条/秒")
            return True
        else:
            print(f"[警告] 数据复制可能不完整! 远程: {remote_count} 条, 本地: {local_count} 条")
            return False

    except Exception as e:
        print(f"[错误] 复制数据失败: {e}")
        print(traceback.format_exc())
        local_conn.rollback()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("BC_groups_rm2local.py - 远程br_groups表到本地数据库同步")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

    # 连接远程数据库
    print("\n1. 连接远程数据库...")
    remote_conn = connect_to_database(REMOTE_DB_CONFIG, "远程数据库")
    if not remote_conn:
        print("[错误] 无法连接远程数据库，程序退出")
        sys.exit(1)

    # 连接本地数据库
    print("\n2. 连接本地数据库...")
    local_conn = connect_to_database(LOCAL_DB_CONFIG, "本地数据库")
    if not local_conn:
        print("[错误] 无法连接本地数据库，程序退出")
        remote_conn.close()
        sys.exit(1)

    try:
        # 检查远程表是否存在
        print(f"\n3. 检查远程表 {TABLE_NAME}...")
        if not test_table_exists(remote_conn, TABLE_NAME, "远程数据库"):
            print(f"[错误] 远程数据库中不存在表 {TABLE_NAME}")
            return False

        # 检查本地表是否存在，如果不存在则创建
        print(f"\n4. 检查本地表 {TABLE_NAME}...")
        if not test_table_exists(local_conn, TABLE_NAME, "本地数据库"):
            print(f"[信息] 本地数据库中不存在表 {TABLE_NAME}，正在创建...")
            if not create_local_table_if_not_exists(local_conn):
                print("[错误] 创建本地表失败")
                return False
        else:
            print(f"[信息] 本地表 {TABLE_NAME} 已存在")

        # 清空本地表
        print(f"\n5. 清空本地表 {TABLE_NAME}...")
        if not truncate_local_table(local_conn, TABLE_NAME):
            print("[错误] 清空本地表失败")
            return False

        # 复制数据
        print(f"\n6. 从远程复制数据到本地...")
        if not copy_data_from_remote(remote_conn, local_conn, TABLE_NAME):
            print("[错误] 数据复制失败")
            return False

        # 显示最终统计信息
        print(f"\n7. 最终数据统计...")
        final_remote_count = get_record_count(remote_conn, TABLE_NAME, "远程")
        final_local_count = get_record_count(local_conn, TABLE_NAME, "本地")

        print(f"[统计] 远程表记录数: {final_remote_count}")
        print(f"[统计] 本地表记录数: {final_local_count}")
        print(f"[统计] 数据一致性: {'✅ 一致' if final_remote_count == final_local_count else '❌ 不一致'}")

        # 进行数据抽样验证
        if final_local_count > 0:
            print(f"\n8. 数据抽样验证...")
            validation_passed = validate_data_sample(remote_conn, local_conn, TABLE_NAME)
            if validation_passed:
                print("[验证] ✅ 数据抽样验证通过")
            else:
                print("[验证] ❌ 数据抽样验证未通过，请检查数据完整性")

        print("\n" + "=" * 60)
        print("✅ br_groups表数据同步完成!")
        print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        return True

    except Exception as e:
        print(f"\n[错误] 程序执行过程中出现异常: {e}")
        print(traceback.format_exc())
        return False
    finally:
        # 关闭数据库连接
        if remote_conn:
            remote_conn.close()
            print("[信息] 远程数据库连接已关闭")
        if local_conn:
            local_conn.close()
            print("[信息] 本地数据库连接已关闭")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
