#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
main_dict_local.py - 本地数据采集与处理主流程
功能：按顺序执行本地数据采集、处理和存储过程的完整流程

执行顺序：
1. login2cookie.py - 登录获取Cookie
2. NEW_sign_get_jsessionid.py - 更新JSESSIONID
3. get_sign_detail.py - 获取签约项目明细
4. merge_headers.py - 合并表头
5. import_to_mysql.py - 导入签约数据到MySQL
6. ne76_get_hetong_all.py - 获取合同信息
7. import_hetong_to_mysql.py - 导入合同数据到MySQL
8. LOCAL_run_procedure.py turn2tkuanbiao - 执行本地存储过程
9. 开工日期处理project_start_date_oldcookie.py - 处理开工日期
10. DICT_download_local_kuanbiao_from_db2email.py - 下载宽表并发送邮件
"""

import os
import sys
import time
import subprocess
from datetime import datetime

# 日志文件名
LOG_FILE = "main_bc_local_runlog.txt"

def log_message(message, print_to_console=True):
    """将消息写入日志文件并可选择性地打印到控制台"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    log_entry = f"[{timestamp}] {message}"

    # 写入日志文件
    try:
        with open(LOG_FILE, 'a', encoding='utf-8') as f:
            f.write(log_entry + '\n')
    except Exception as e:
        print(f"写入日志文件失败: {e}")

    # 打印到控制台
    if print_to_console:
        print(message)

def run_script(script_name, description=None, args=None):
    """运行指定的Python脚本并等待其完成"""
    if description:
        log_message(f"\n{'='*60}")
        log_message(f"🚀 {description}")
        log_message(f"{'='*60}")

    # 构建命令
    cmd = [sys.executable, script_name]
    if args:
        cmd.extend(args)
    
    log_message(f"[信息] 正在运行: {' '.join(cmd)}")

    try:
        # 使用subprocess运行脚本，指定encoding为utf-8
        result = subprocess.run(cmd,
                               capture_output=True,
                               text=True,
                               encoding='utf-8',
                               check=True)

        # 记录脚本的输出到日志
        if result.stdout:
            log_message(f"[输出] {script_name} 脚本输出:")
            # 将脚本输出按行记录到日志
            for line in result.stdout.strip().split('\n'):
                if line.strip():  # 只记录非空行
                    log_message(f"  {line}")

        if result.stderr:
            log_message(f"[警告] {script_name} 脚本警告信息:")
            for line in result.stderr.strip().split('\n'):
                if line.strip():
                    log_message(f"  {line}")

        log_message(f"[成功] ✅ {script_name} 执行完成")
        return True

    except subprocess.CalledProcessError as e:
        log_message(f"[错误] ❌ {script_name} 执行失败，返回码: {e.returncode}")
        if e.stdout:
            log_message(f"[输出] 标准输出:")
            for line in e.stdout.strip().split('\n'):
                if line.strip():
                    log_message(f"  {line}")
        if e.stderr:
            log_message(f"[错误] 错误输出:")
            for line in e.stderr.strip().split('\n'):
                if line.strip():
                    log_message(f"  {line}")
        return False

    except FileNotFoundError:
        log_message(f"[错误] ❌ 找不到脚本文件: {script_name}")
        return False

    except Exception as e:
        log_message(f"[错误] ❌ 运行 {script_name} 时发生异常: {e}")
        return False

def check_file_exists(filename):
    """检查文件是否存在"""
    if os.path.exists(filename):
        log_message(f"[检查] ✅ 文件存在: {filename}")
        return True
    else:
        log_message(f"[检查] ❌ 文件不存在: {filename}")
        return False

def find_latest_file(pattern, description="文件"):
    """查找最新的匹配文件"""
    try:
        files = [f for f in os.listdir('.') if pattern in f and f.endswith('.xlsx')]
        if files:
            latest_file = max(files, key=os.path.getmtime)
            log_message(f"[信息] 找到最新的{description}: {latest_file}")
            return latest_file
        else:
            log_message(f"[警告] 未找到匹配的{description}")
            return None
    except Exception as e:
        log_message(f"[错误] 查找{description}时出错: {e}")
        return None

def main():
    """主函数，按顺序执行所有脚本"""
    # 在日志文件末尾添加新的运行记录开始标记
    try:
        with open(LOG_FILE, 'a', encoding='utf-8') as f:
            f.write(f"\n{'='*80}\n")
            f.write(f"=== main_dict_local.py 新运行记录开始 ===\n")
            f.write(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("="*80 + "\n\n")
    except Exception as e:
        print(f"写入日志开始标记失败: {e}")

    start_time = time.time()
    log_message(f"📊 开始执行本地数据采集与处理流程 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 步骤1: 运行login2cookie.py，获取登录cookie
    if not run_script("login2cookie.py", "步骤1: 登录获取Cookie"):
        log_message("[错误] 登录获取Cookie失败，流程终止")
        return

    # 步骤2: 运行NEW_sign_get_jsessionid.py，更新JSESSIONID
    if not run_script("NEW_sign_get_jsessionid.py", "步骤2: 更新JSESSIONID"):
        log_message("[错误] 更新JSESSIONID失败，流程终止")
        return

    # 步骤3: 运行get_sign_detail.py，获取签约项目明细
    if not run_script("get_sign_detail.py", "步骤3: 获取签约项目明细"):
        log_message("[错误] 获取签约项目明细失败，流程终止")
        return

    # 查找最新的签约项目明细文件
    latest_detail_file = find_latest_file('temp_签约项目明细_', '签约项目明细文件')
    if not latest_detail_file:
        log_message("[错误] 未找到签约项目明细文件，流程终止")
        return

    # 步骤4: 运行merge_headers.py，合并表头
    if not run_script("merge_headers.py", "步骤4: 合并表头"):
        log_message("[错误] 合并表头失败，流程终止")
        return

    # 查找最新的合并表头后的文件
    latest_merged_file = find_latest_file('merged_header_', '合并表头文件')
    if not latest_merged_file:
        log_message("[错误] 未找到合并表头后的文件，流程终止")
        return

    # 步骤5: 运行import_to_mysql.py，导入签约数据到MySQL
    if not run_script("import_to_mysql.py", "步骤5: 导入签约数据到MySQL"):
        log_message("[错误] 导入签约数据到MySQL失败，流程终止")
        return

    # 步骤6: 运行ne76_get_hetong_all.py，获取合同信息
    if not run_script("ne76_get_hetong_all.py", "步骤6: 获取合同信息"):
        log_message("[错误] 获取合同信息失败，流程终止")
        return

    # 步骤7: 运行import_hetong_to_mysql.py，导入合同数据到MySQL
    if not run_script("import_hetong_to_mysql.py", "步骤7: 导入合同数据到MySQL"):
        log_message("[错误] 导入合同数据到MySQL失败，流程终止")
        return

    # 步骤8: 运行LOCAL_run_procedure.py turn2tkuanbiao，执行本地存储过程
    if not run_script("LOCAL_run_procedure.py", "步骤8: 执行本地存储过程turn2tkuanbiao", ["turn2tkuanbiao"]):
        log_message("[错误] 执行本地存储过程失败，流程终止")
        return

    # 步骤9: 运行开工日期处理project_start_date_oldcookie.py，处理开工日期
    if not run_script("开工日期处理project_start_date_oldcookie.py", "步骤9: 处理开工日期"):
        log_message("[错误] 处理开工日期失败，流程终止")
        return

    # 步骤10: 运行DICT_download_local_kuanbiao_from_db2email.py，下载宽表并发送邮件
    if not run_script("DICT_download_local_kuanbiao_from_db2email.py", "步骤10: 下载宽表并发送邮件"):
        log_message("[错误] 下载宽表并发送邮件失败，流程终止")
        return

    # 计算总执行时间
    end_time = time.time()
    total_time = end_time - start_time
    hours = int(total_time // 3600)
    minutes = int((total_time % 3600) // 60)
    seconds = int(total_time % 60)

    log_message(f"\n{'='*80}")
    log_message(f"🎉 所有步骤执行完成！")
    log_message(f"📊 总执行时间: {hours}小时 {minutes}分钟 {seconds}秒")
    log_message(f"🕐 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    log_message(f"{'='*80}")

    # 在日志文件末尾添加运行记录结束标记
    try:
        with open(LOG_FILE, 'a', encoding='utf-8') as f:
            f.write(f"\n=== main_dict_local.py 运行记录结束 ===\n")
            f.write(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总执行时间: {hours}小时 {minutes}分钟 {seconds}秒\n")
            f.write("="*80 + "\n\n")
    except Exception as e:
        print(f"写入日志结束标记失败: {e}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        log_message("\n[中断] 用户手动中断程序执行")
        sys.exit(1)
    except Exception as e:
        log_message(f"\n[异常] 程序执行过程中发生异常: {e}")
        sys.exit(1)
