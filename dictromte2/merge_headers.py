#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 自动设置UTF-8编码，解决中文乱码问题
try:
    import utf8_setup
except ImportError:
    # 如果utf8_setup模块不存在，使用内置设置
    import sys, os
    if sys.platform.startswith('win'):
        os.system('chcp 65001 >nul 2>&1')
        os.environ['PYTHONIOENCODING'] = 'utf-8'

import pandas as pd
import numpy as np
import warnings
import sys
import os
from datetime import datetime
from file_manager import move_temp_sign_file

# 忽略 openpyxl 的样式警告
warnings.filterwarnings("ignore", category=UserWarning,
                        module="openpyxl.styles.stylesheet")

def merge_headers(input_file=None):
    # 如果没有提供输入文件，尝试使用命令行参数
    if input_file is None:
        if len(sys.argv) > 1:
            input_file = sys.argv[1]
        else:
            # 查找最新的temp_签约项目明细文件
            files = [f for f in os.listdir('.') if f.startswith('temp_签约项目明细_') and f.endswith('.xlsx')]
            if not files:
                print("[错误] 未找到签约项目明细文件，请先运行get_sign_detail.py")
                return None
            # 按修改时间排序，获取最新的文件
            input_file = max(files, key=os.path.getmtime)
            print(f"[信息] 自动选择最新的文件: {input_file}")

    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"[错误] 文件不存在: {input_file}")
        return None

    print(f"[信息] 正在处理文件: {input_file}")

    # 读取Excel文件，不指定header
    df = pd.read_excel(input_file, header=None)

    # 获取前三行作为表头
    header_rows = df.iloc[:3]

    # 创建新的表头列表
    new_headers = []
    for col in range(header_rows.shape[1]):
        # 获取当前列的前三行
        col_values = header_rows.iloc[:, col].tolist()
        # 去除NaN值
        col_values = [str(val).strip().replace("-", "_") for val in col_values if not pd.isna(val)]

        # 去除重复值，只保留第一次出现的值
        unique_values = []
        for val in col_values:
            if val not in unique_values:
                unique_values.append(val)

        # 用双下划线"__"连接
        header = '__'.join(unique_values)

        # 如果整列都是NaN，使用默认列名
        if header == '':
            header = f'Column_{col}'
        new_headers.append(header)

    # 创建新的DataFrame，使用合并后的表头，数据从第4行开始
    new_df = pd.DataFrame(df.iloc[3:].values, columns=new_headers)

    # 保存到新的Excel文件
    output_file = 'merged_header_' + os.path.basename(input_file)
    new_df.to_excel(output_file, index=False)

    print(f"[成功] 表头合并完成，已保存到{output_file}")

    # 将原始temp文件移动到"已处理"文件夹
    try:
        success, new_path = move_temp_sign_file(os.path.basename(input_file))
        if success:
            print(f"[迁移] 原始文件已迁移到已处理文件夹")
        else:
            print(f"[警告] 原始文件迁移失败")
    except Exception as e:
        print(f"[警告] 迁移原始文件时出错: {e}")

    return output_file

if __name__ == "__main__":
    merge_headers()
