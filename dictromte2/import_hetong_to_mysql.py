#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 自动设置UTF-8编码，解决中文乱码问题
try:
    import utf8_setup
except ImportError:
    # 如果utf8_setup模块不存在，使用内置设置
    import sys, os
    if sys.platform.startswith('win'):
        os.system('chcp 65001 >nul 2>&1')
        os.environ['PYTHONIOENCODING'] = 'utf-8'

import pandas as pd
import pymysql
import warnings
import sys
import os
from sqlalchemy import create_engine
from file_manager import move_contract_file

# 忽略pandas的警告
warnings.filterwarnings('ignore')

# 数据库连接信息
DB_CONFIG = {
    'host': '*************',
    'port': 12136,
    'user': 'root',
    'password': '5eb9a11916e3a66d',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

def import_to_mysql(input_file=None):
    # 如果没有提供输入文件，尝试使用命令行参数
    if input_file is None:
        if len(sys.argv) > 1:
            input_file = sys.argv[1]
        else:
            # 查找最新的合同信息列表_文件
            files = [f for f in os.listdir('.') if f.startswith('合同信息列表_') and f.endswith('.xlsx')]
            if not files:
                print("[错误] 未找到合并表头后的文件，请先运行get_hetong.py")
                return False
            # 按修改时间排序，获取最新的文件
            input_file = max(files, key=os.path.getmtime)
            print(f"[信息] 自动选择最新的文件: {input_file}")

    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"[错误] 文件不存在: {input_file}")
        return False

    # 读取Excel文件
    print(f"[信息] 正在读取Excel文件: {input_file}")
    df = pd.read_excel(input_file)

    # 检查数据
    print(f"读取到 {len(df)} 行数据")
    print("表头列名:", df.columns.tolist())

    # 连接数据库
    print("正在连接到MySQL数据库...")
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()

    try:
        # 检查表是否存在，如果不存在则创建
        print("检查数据表是否存在...")
        cursor.execute("SHOW TABLES LIKE 'hetong'")
        if not cursor.fetchone():
            print("数据表不存在，正在创建...")
            # 使用TEXT类型而不是VARCHAR(255)来避免行大小限制
            columns = []
            for col in df.columns:
                # 替换特殊字符，确保列名有效
                safe_col = col.replace(' ', '_').replace('-', '_').replace('(', '').replace(')', '')
                columns.append(f"`{safe_col}` TEXT")

            create_table_sql = """
            CREATE TABLE `hetong` (
                id INT AUTO_INCREMENT PRIMARY KEY,
                {0},
                import_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """.format(', '.join(columns))

            cursor.execute(create_table_sql)
            print("数据表创建成功")

        # 使用pandas的to_sql方法直接导入数据
        print("正在使用pandas to_sql方法插入数据...")

        # 处理列名，确保有效
        df_copy = df.copy()
        df_copy.columns = [col.replace(' ', '_').replace('-', '_').replace('(', '').replace(')', '') for col in df.columns]

        # 创建SQLAlchemy引擎
        engine_url = f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?charset={DB_CONFIG['charset']}"
        engine = create_engine(engine_url)

        # 使用to_sql方法将数据导入到MySQL
        df_copy.to_sql('hetong', engine, if_exists='append', index=False, chunksize=10)

        print(f"[成功] 成功导入 {len(df_copy)} 行数据到 dict_spider.hetong 表")

        # 导入成功后，将合同信息文件移动到"已处理"文件夹
        try:
            success, new_path = move_contract_file(os.path.basename(input_file))
            if success:
                print(f"[迁移] 合同信息文件已迁移到已处理文件夹")
            else:
                print(f"[警告] 合同信息文件迁移失败")
        except Exception as e:
            print(f"[警告] 迁移合同信息文件时出错: {e}")

        return True

    except Exception as e:
        conn.rollback()
        print(f"[错误] 导入数据时出错: {e}")
        return False
    finally:
        cursor.close()
        conn.close()
        print("[信息] 数据库连接已关闭")

def main():
    import_to_mysql()

if __name__ == "__main__":
    main()