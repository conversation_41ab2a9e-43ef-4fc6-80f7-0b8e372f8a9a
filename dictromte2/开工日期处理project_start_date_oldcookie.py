#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 自动设置UTF-8编码，解决中文乱码问题
try:
    import utf8_setup
except ImportError:
    # 如果utf8_setup模块不存在，使用内置设置
    import sys, os
    if sys.platform.startswith('win'):
        os.system('chcp 65001 >nul 2>&1')
        os.environ['PYTHONIOENCODING'] = 'utf-8'

import requests
import json
import pymysql
import os
from datetime import datetime

# 数据库连接配置
DB_CONFIG = {
    'host': '*************',
    'port': 12136,
    'user': 'root',
    'password': '5eb9a11916e3a66d',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

# API请求配置
url = "http://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp//preparation/queryProjectStart"

def load_cookies(filename="cookies.txt"):
    """从cookies.txt文件中加载cookie，支持JSON格式和简单键值对格式"""
    cookies = {}
    try:
        if os.path.exists(filename):
            with open(filename, "r", encoding="utf-8") as f:
                content = f.read().strip()

                # 判断是否为JSON格式
                if content.startswith('{') and content.endswith('}'):
                    # JSON格式解析
                    data = json.loads(content)
                    if "cookies" in data and isinstance(data["cookies"], list):
                        for cookie in data["cookies"]:
                            if "name" in cookie and "value" in cookie:
                                cookies[cookie["name"]] = cookie["value"]
                        print(f"[信息] 已从{filename}加载Cookie (JSON格式，共{len(cookies)}个)")
                    else:
                        print(f"[错误] JSON格式不正确，缺少cookies数组")
                        exit(1)
                else:
                    # 简单键值对格式解析
                    for line in content.split('\n'):
                        if "=" in line:
                            name, value = line.strip().split("=", 1)
                            cookies[name] = value
                    print(f"[信息] 已从{filename}加载Cookie (键值对格式，共{len(cookies)}个)")

            return cookies
        else:
            print(f"[警告] Cookie文件{filename}不存在，请先运行getcookie.py获取Cookie")
            exit(1)
    except json.JSONDecodeError as e:
        print(f"[错误] JSON格式解析失败: {e}")
        exit(1)
    except Exception as e:
        print(f"[错误] 加载Cookie失败: {e}")
        exit(1)

def get_project_codes():
    """从MySQL获取项目编码列表"""
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    try:
        # 从开工时间视图获取项目编码
        cursor.execute("SELECT 项目编码 FROM 开工时间视图")
        project_codes = [row[0] for row in cursor.fetchall()]
        print(f"获取到 {len(project_codes)} 个项目编码")
        return project_codes
    except Exception as e:
        print(f"获取项目编码失败: {e}")
        return []
    finally:
        cursor.close()
        conn.close()

def get_start_date(project_code, cookies_dict):
    """获取项目的开工日期"""
    # 构建请求头，使用从cookies.txt加载的Cookie
    cookie_str = "; ".join([f"{name}={value}" for name, value in cookies_dict.items()])
    
    headers = {
        'Host': "dict.gmcc.net:30722",
        'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        'Accept': "application/json, text/plain, */*",
        'Accept-Encoding': "gzip, deflate",
        'Content-Type': "application/json",
        'Pragma': "no-cache",
        'Cache-Control': "no-cache",
        'Origin': "http://dict.gmcc.net:30722",
        'Referer': "http://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/",
        'Accept-Language': "zh-CN,zh;q=0.9",
        'Cookie': cookie_str
    }
    
    payload = {
        "ROOT": {
            "HEADER": {
                "OPR_INFO": {
                    "LOGIN_NO": "zhengdewen"   # cookies_dict.get("userCode", "")
                }
            },
            "BODY": {
                "PROJECT_ID": project_code
            }
        }
    }
    
    try:
        response = requests.post(url, data=json.dumps(payload), headers=headers)
        data = response.json()
        print(f"响应数据data: {data}")
        # 从响应中提取开工日期
        start_date = data.get('ROOT', {}).get('BODY', {}).get('OUT_DATA', {}).get('PLAN_START_DATE', '')
        return start_date
    except Exception as e:
        print(f"获取项目 {project_code} 的开工日期失败: {e}")
        return None

def save_start_date(project_code, start_date):
    """保存项目开工日期到数据库"""
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    current_date = datetime.now().strftime("%Y-%m-%d")
    
    try:
        # 使用INSERT INTO语句，按照已有表结构插入数据
        cursor.execute("""
        INSERT INTO project_start_date (项目编码, 开工时间, 查询日期)
        VALUES (%s, %s, %s)
        ON DUPLICATE KEY UPDATE 开工时间 = %s, 查询日期 = %s
        """, (project_code, start_date, current_date, start_date, current_date))
        conn.commit()
    except Exception as e:
        conn.rollback()
        print(f"保存项目 {project_code} 的开工日期失败: {e}")
    finally:
        cursor.close()
        conn.close()

def main():
    # 加载Cookie
    cookies_dict = load_cookies()
    print(f"已加载Cookie: {len(cookies_dict)}个")
    
    # 获取所有项目编码
    project_codes = get_project_codes()
    print(f"获取到 {len(project_codes)} 个项目编码")
    
    # 获取并保存每个项目的开工日期
    for i, project_code in enumerate(project_codes):
        print(f"处理项目 {i+1}/{len(project_codes)}: {project_code}")
        start_date = get_start_date(project_code, cookies_dict)
        if start_date:
            save_start_date(project_code, start_date)
            print(f"项目 {project_code} 的开工日期: {start_date}")
        else:
            print(f"项目 {project_code} 未获取到开工日期")

if __name__ == "__main__":
    main()