#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
test_file_manager.py - 测试文件管理器功能
"""

# 自动设置UTF-8编码，解决中文乱码问题
try:
    import utf8_setup
except ImportError:
    # 如果utf8_setup模块不存在，使用内置设置
    import sys, os
    if sys.platform.startswith('win'):
        os.system('chcp 65001 >nul 2>&1')
        os.environ['PYTHONIOENCODING'] = 'utf-8'

import os
import pandas as pd
from datetime import datetime
from file_manager import FileManager

def create_test_files():
    """创建测试文件"""
    print("[测试] 创建测试文件...")
    
    # 创建测试数据
    test_data = {
        '列1': ['数据1', '数据2', '数据3'],
        '列2': ['值1', '值2', '值3'],
        '列3': [1, 2, 3]
    }
    df = pd.DataFrame(test_data)
    
    # 创建测试文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    files_created = []
    
    # 创建temp_签约项目明细文件
    temp_file = f"temp_签约项目明细_{timestamp}.xlsx"
    df.to_excel(temp_file, index=False)
    files_created.append(temp_file)
    print(f"[创建] {temp_file}")
    
    # 创建merged_header文件
    merged_file = f"merged_header_temp_签约项目明细_{timestamp}.xlsx"
    df.to_excel(merged_file, index=False)
    files_created.append(merged_file)
    print(f"[创建] {merged_file}")
    
    # 创建合同信息文件
    contract_file = f"合同信息列表_全部_{timestamp}.xlsx"
    df.to_excel(contract_file, index=False)
    files_created.append(contract_file)
    print(f"[创建] {contract_file}")
    
    return files_created

def test_file_manager():
    """测试文件管理器功能"""
    print("=" * 60)
    print("文件管理器功能测试")
    print("=" * 60)
    
    # 创建测试文件
    test_files = create_test_files()
    
    # 初始化文件管理器
    fm = FileManager()
    
    print("\n[测试] 测试文件迁移功能...")
    
    # 测试移动temp文件
    print("\n1. 测试移动temp_签约项目明细文件:")
    moved_temp = fm.move_temp_sign_files()
    print(f"   移动了 {len(moved_temp)} 个文件")
    
    # 测试移动merged_header文件
    print("\n2. 测试移动merged_header文件:")
    moved_merged = fm.move_merged_header_files()
    print(f"   移动了 {len(moved_merged)} 个文件")
    
    # 测试移动合同信息文件
    print("\n3. 测试移动合同信息文件:")
    moved_contract = fm.move_contract_files()
    print(f"   移动了 {len(moved_contract)} 个文件")
    
    # 检查"已处理"目录
    print("\n[检查] 已处理目录内容:")
    processed_dir = "已处理"
    if os.path.exists(processed_dir):
        files_in_processed = os.listdir(processed_dir)
        for file in files_in_processed:
            print(f"   - {file}")
        print(f"   总计: {len(files_in_processed)} 个文件")
    else:
        print("   已处理目录不存在")
    
    print("\n=" * 60)
    print("测试完成！")
    print("=" * 60)

if __name__ == "__main__":
    test_file_manager()
