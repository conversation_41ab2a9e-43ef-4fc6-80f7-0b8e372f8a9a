#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
最终版字典网站自动登录脚本
优化验证码识别和登录流程
"""

# 自动设置UTF-8编码，解决中文乱码问题
try:
    import utf8_setup
except ImportError:
    # 如果utf8_setup模块不存在，使用内置设置
    import sys, os
    if sys.platform.startswith('win'):
        os.system('chcp 65001 >nul 2>&1')
        os.environ['PYTHONIOENCODING'] = 'utf-8'

import asyncio
import time
import os
import re
import json
from datetime import datetime
from playwright.async_api import async_playwright
import ddddocr


class FinalDictLoginBot:
    def __init__(self):
        self.url = "https://dict.gmcc.net:30722/dictWeb/login"
        # self.username = "liaochulin"
        # self.password = "Liaochulin147!"
        self.username = "zhen<PERSON><PERSON><PERSON>"
        self.password = "<PERSON>wen@428"

        self.ocr = ddddocr.DdddOcr(show_ad=False)

        # Cookie文件路径
        self.cookie_file = "cookies.txt"

        # 创建验证码图片保存目录
        self.captcha_dir = "captcha_images"
        if not os.path.exists(self.captcha_dir):
            os.makedirs(self.captcha_dir)

    async def login(self):
        """执行登录流程"""
        async with async_playwright() as p:
            # 启动浏览器
            browser = await p.chromium.launch(
                headless=True,
                slow_mo=500,  # 减少延迟
                args=['--disable-blink-features=AutomationControlled']  # 避免被检测为自动化
            )
            context = await browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                ignore_https_errors=True  # 忽略HTTPS证书错误
            )
            page = await context.new_page()

            try:
                print("[访问] 正在访问登录页面...")
                await page.goto(self.url, wait_until='networkidle')

                # 等待页面完全加载
                await asyncio.sleep(2)

                # 执行登录流程
                success = await self.perform_login(page)

                if success:
                    print("[成功] 登录流程完成！")

                    # 保存Cookie
                    print("[保存] 正在保存Cookie...")
                    await self.save_cookies(context)

                    # 保持浏览器打开以便查看结果
                    print("[等待] 保持浏览器打开600秒...")
                    await asyncio.sleep(6)
                else:
                    print("[失败] 登录失败")
                    await asyncio.sleep(10)

            except Exception as e:
                print(f"[错误] 登录过程中出现错误: {e}")
                await asyncio.sleep(10)

            finally:
                await browser.close()

    async def perform_login(self, page):
        """执行登录操作"""
        max_login_attempts = 3

        for login_attempt in range(max_login_attempts):
            print(f"\n[尝试] 第 {login_attempt + 1} 次登录尝试...")

            try:
                # 输入用户名
                print("[用户名] 输入用户名...")
                username_input = await page.wait_for_selector('input[name="username"]', timeout=10000)
                await username_input.fill('')  # 清空
                await username_input.fill(self.username)
                print(f"[成功] 用户名已输入: {self.username}")

                # 输入密码
                print("[密码] 输入密码...")
                password_input = await page.wait_for_selector('input[name="password"]', timeout=10000)
                await password_input.fill('')  # 清空
                await password_input.fill(self.password)
                print("[成功] 密码已输入")

                # 处理验证码
                captcha_success = await self.handle_captcha_smart(page)
                if not captcha_success:
                    print("[失败] 验证码处理失败")
                    continue

                # 点击登录按钮
                print("[点击] 点击登录按钮...")
                login_button = await page.wait_for_selector('button:has-text("登录")', timeout=10000)
                await login_button.click()
                print("[成功] 登录按钮已点击")

                # 等待登录结果
                print("[等待] 等待登录结果...")
                await asyncio.sleep(3)

                # 检查登录结果
                login_success = await self.check_login_success(page)
                if login_success:
                    return True
                else:
                    print(f"[失败] 第 {login_attempt + 1} 次登录失败")
                    if login_attempt < max_login_attempts - 1:
                        print("[重试] 准备重试...")
                        await asyncio.sleep(2)

            except Exception as e:
                print(f"[错误] 登录尝试 {login_attempt + 1} 出错: {e}")
                if login_attempt < max_login_attempts - 1:
                    await asyncio.sleep(2)

        return False

    async def handle_captcha_smart(self, page):
        """智能验证码处理"""
        max_captcha_attempts = 8

        for attempt in range(max_captcha_attempts):
            try:
                print(f"[识别] 验证码识别尝试 {attempt + 1}/{max_captcha_attempts}")

                # 查找验证码图片
                captcha_img = await page.wait_for_selector('img[id="getCodeOfPicture"]', timeout=10000)

                # 截取验证码图片
                captcha_bytes = await captcha_img.screenshot()

                # 保存验证码图片
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                captcha_filename = f"{self.captcha_dir}/captcha_{timestamp}_{attempt + 1}.png"
                with open(captcha_filename, 'wb') as f:
                    f.write(captcha_bytes)

                # 使用ddddocr识别验证码
                captcha_text = self.ocr.classification(captcha_bytes)

                # 清理识别结果
                captcha_text = self.clean_captcha_text(captcha_text)

                print(f"[结果] 识别结果: '{captcha_text}' (长度: {len(captcha_text)})")

                # 验证码合理性检查
                if not self.is_valid_captcha(captcha_text):
                    print(f"[警告] 验证码格式不合理，重新识别...")
                    await self.refresh_captcha(page)
                    continue

                # 输入验证码
                captcha_input = await page.wait_for_selector('input[name="code"]', timeout=10000)
                await captcha_input.fill('')  # 清空
                await asyncio.sleep(0.3)
                await captcha_input.fill(captcha_text)

                # 验证输入
                input_value = await captcha_input.input_value()
                if input_value == captcha_text:
                    print(f"[成功] 验证码输入成功: '{input_value}'")
                    return True
                else:
                    print(f"[警告] 输入验证失败: '{input_value}' != '{captcha_text}'")

            except Exception as e:
                print(f"[错误] 验证码处理失败: {e}")

            # 刷新验证码准备重试
            if attempt < max_captcha_attempts - 1:
                await self.refresh_captcha(page)
                await asyncio.sleep(1)

        return False

    def clean_captcha_text(self, text):
        """清理验证码识别结果"""
        if not text:
            return ""

        # 移除空格和特殊字符
        text = re.sub(r'[^a-zA-Z0-9]', '', text)

        # 常见字符替换
        replacements = {
            'O': '0',  # 字母O替换为数字0
            'I': '1',  # 字母I替换为数字1
            'l': '1',  # 小写l替换为数字1
            'S': '5',  # 字母S有时会被误识别
            'G': '6',  # 字母G有时会被误识别
        }

        for old, new in replacements.items():
            text = text.replace(old, new)

        return text

    def is_valid_captcha(self, text):
        """验证码合理性检查"""
        if not text:
            return False

        # 长度检查
        if len(text) < 3 or len(text) > 6:
            return False

        # 字符检查 - 只允许字母和数字
        if not re.match(r'^[a-zA-Z0-9]+$', text):
            return False

        return True

    async def refresh_captcha(self, page):
        """刷新验证码"""
        try:
            print("[刷新] 刷新验证码...")
            captcha_img = await page.query_selector('img[id="getCodeOfPicture"]')
            if captcha_img:
                await captcha_img.click()
                await asyncio.sleep(1.5)  # 等待新验证码加载
        except Exception as e:
            print(f"[警告] 刷新验证码失败: {e}")

    async def check_login_success(self, page):
        """检查登录是否成功"""
        try:
            # 等待页面响应
            await asyncio.sleep(2)

            current_url = page.url
            print(f"[URL] 当前URL: {current_url}")

            # 检查URL变化
            if "login" not in current_url.lower():
                print("[成功] 登录成功！URL已跳转")
                return True

            # 检查错误消息
            error_found = await self.check_error_messages(page)
            if error_found:
                return False

            # 如果没有明确的错误，可能需要等待更长时间
            print("[等待] 等待更长时间检查登录状态...")
            await asyncio.sleep(3)

            current_url = page.url
            if "login" not in current_url.lower():
                print("[成功] 登录成功！")
                return True

            return False

        except Exception as e:
            print(f"[错误] 检查登录状态时出错: {e}")
            return False

    async def check_error_messages(self, page):
        """检查错误消息"""
        try:
            # 等待可能的错误消息出现
            await asyncio.sleep(1)

            error_selectors = [
                '.el-message--error',
                '.el-message',
                '.error-message',
                '[class*="error"]'
            ]

            for selector in error_selectors:
                elements = await page.query_selector_all(selector)
                for element in elements:
                    text = await element.inner_text()
                    if text and text.strip():
                        print(f"[错误] 发现错误信息: {text}")
                        return True

            return False

        except Exception as e:
            print(f"[警告] 检查错误消息时出错: {e}")
            return False

    async def save_cookies(self, context):
        """保存Cookie到文件"""
        try:
            # 获取所有Cookie
            cookies = await context.cookies()

            # 准备保存的数据
            cookie_data = {
                "timestamp": datetime.now().isoformat(),
                "url": self.url,
                "cookies": cookies
            }

            # 保存到JSON格式的txt文件
            with open(self.cookie_file, 'w', encoding='utf-8') as f:
                json.dump(cookie_data, f, ensure_ascii=False, indent=2)

            print(f"[成功] Cookie已保存到: {self.cookie_file}")
            print(f"[统计] 保存了 {len(cookies)} 个Cookie")

            # 显示主要Cookie信息
            for cookie in cookies:
                if cookie['name'] in ['JSESSIONID', 'token', 'session', 'auth']:
                    print(f"[重要] 重要Cookie: {cookie['name']} = {cookie['value'][:20]}...")

        except Exception as e:
            print(f"[错误] 保存Cookie失败: {e}")

    async def load_cookies(self, context):
        """从文件加载Cookie"""
        try:
            with open(self.cookie_file, 'r', encoding='utf-8') as f:
                cookie_data = json.load(f)

            cookies = cookie_data.get('cookies', [])
            timestamp = cookie_data.get('timestamp', '')

            print(f"[时间] Cookie保存时间: {timestamp}")
            print(f"[统计] 加载了 {len(cookies)} 个Cookie")

            # 添加Cookie到context
            await context.add_cookies(cookies)

            print("[成功] Cookie加载成功")
            return True

        except Exception as e:
            print(f"[错误] 加载Cookie失败: {e}")
            return False


async def main():
    """主函数"""
    bot = FinalDictLoginBot()
    await bot.login()


if __name__ == "__main__":
    print("[启动] 启动最终版字典网站自动登录程序...")
    print("[优化] 优化了验证码识别和登录流程")
    print("[保存] 验证码图片将保存到 captcha_images 目录")
    print("=" * 60)
    asyncio.run(main())
