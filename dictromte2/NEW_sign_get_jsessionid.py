#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 自动设置UTF-8编码，解决中文乱码问题
try:
    import utf8_setup
except ImportError:
    # 如果utf8_setup模块不存在，使用内置设置
    import sys, os
    if sys.platform.startswith('win'):
        os.system('chcp 65001 >nul 2>&1')
        os.environ['PYTHONIOENCODING'] = 'utf-8'

import requests
import os
import hashlib
import time
import json
from datetime import datetime
from urllib.parse import urlencode

def load_cookies(filename="cookies.txt"):
    """从cookies.txt文件中加载cookie，支持JSON格式和简单键值对格式"""
    cookies = {}
    cookie_data = None
    try:
        if os.path.exists(filename):
            with open(filename, "r", encoding="utf-8") as f:
                content = f.read().strip()

                # 判断是否为JSON格式
                if content.startswith('{') and content.endswith('}'):
                    # JSON格式解析
                    cookie_data = json.loads(content)
                    if "cookies" in cookie_data and isinstance(cookie_data["cookies"], list):
                        for cookie in cookie_data["cookies"]:
                            if "name" in cookie and "value" in cookie:
                                cookies[cookie["name"]] = cookie["value"]
                        print(f"[信息] 已从{filename}加载Cookie (JSON格式，共{len(cookies)}个)")
                    else:
                        print(f"[错误] JSON格式不正确，缺少cookies数组")
                        exit(1)
                else:
                    # 简单键值对格式解析
                    for line in content.split('\n'):
                        if "=" in line:
                            name, value = line.strip().split("=", 1)
                            cookies[name] = value
                    print(f"[信息] 已从{filename}加载Cookie (键值对格式，共{len(cookies)}个)")

            return cookies, cookie_data
        else:
            print(f"[警告] Cookie文件{filename}不存在，请先运行getcookie.py获取Cookie")
            exit(1)
    except json.JSONDecodeError as e:
        print(f"[错误] JSON格式解析失败: {e}")
        exit(1)
    except Exception as e:
        print(f"[错误] 加载Cookie失败: {e}")
        exit(1)

def save_cookies(cookies_dict, cookie_data, filename="cookies.txt"):
    """保存cookies到文件，保持JSON格式"""
    try:
        if cookie_data is not None:
            # 更新JSON格式的cookie数据
            cookie_data["timestamp"] = datetime.now().isoformat()

            # 更新cookies数组
            for cookie in cookie_data["cookies"]:
                if cookie["name"] in cookies_dict:
                    cookie["value"] = cookies_dict[cookie["name"]]

            # 添加新的cookies（如果有的话）
            existing_names = {cookie["name"] for cookie in cookie_data["cookies"]}
            for name, value in cookies_dict.items():
                if name not in existing_names:
                    new_cookie = {
                        "name": name,
                        "value": value,
                        "domain": "dict.gmcc.net",
                        "path": "/",
                        "expires": -1,
                        "httpOnly": False,
                        "secure": False,
                        "sameSite": "Lax"
                    }
                    cookie_data["cookies"].append(new_cookie)

            # 保存JSON格式
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(cookie_data, f, indent=2, ensure_ascii=False)
            print(f"[信息] Cookie已更新并保存到 {filename} (JSON格式)")
        else:
            # 如果没有原始JSON数据，创建新的JSON格式
            cookie_data = {
                "timestamp": datetime.now().isoformat(),
                "url": "http://dict.gmcc.net:30722",
                "cookies": []
            }

            for name, value in cookies_dict.items():
                cookie_data["cookies"].append({
                    "name": name,
                    "value": value,
                    "domain": "dict.gmcc.net",
                    "path": "/",
                    "expires": -1,
                    "httpOnly": False,
                    "secure": False,
                    "sameSite": "Lax"
                })

            with open(filename, "w", encoding="utf-8") as f:
                json.dump(cookie_data, f, indent=2, ensure_ascii=False)
            print(f"[信息] Cookie已保存到 {filename} (新建JSON格式)")

    except Exception as e:
        print(f"[错误] 保存Cookie失败: {e}")
        exit(1)

def get_current_timestamp():
    """
    生成当前时间的时间戳，格式为YYYYMMDDHHmmss
    
    Returns:
        str: 格式化的时间戳
    """
    now = datetime.now()
    return now.strftime("%Y%m%d%H%M%S")

def generate_sign(params, secret_key):
    """
    生成签名
    
    Args:
        params (dict): 请求参数对象
        secret_key (str): 密钥
    
    Returns:
        str: 生成的签名
    """
    # 1. 将参数按键名排序
    sorted_keys = sorted(params.keys())
    
    # 2. 构建参数字符串，排除secretKey
    param_string = ''
    for key in sorted_keys:
        if key != 'secretKey':
            param_string += f"{key}={params[key]}&"
    
    # 注意：保留最后的&字符
    
    print(f"[调试] 参数字符串: {param_string}")
    print(f"[调试] 密钥: {secret_key}")
    
    # 4. 第一次哈希：paramString + secretKey
    first_hash = hashlib.md5((param_string + secret_key).encode('utf-8')).hexdigest()
    print(f"[调试] 第一次哈希结果: {first_hash}")
    
    # 5. 第二次哈希：firstHash + secretKey
    sign = hashlib.md5((first_hash + secret_key).encode('utf-8')).hexdigest()
    print(f"[调试] 第二次哈希结果: {sign}")
    
    return sign

def get_jsessionid():
    """访问SSO接口获取新的JSESSIONID"""
    # 加载现有cookie
    cookies_dict, cookie_data = load_cookies()
    cookie_str = "; ".join([f"{name}={value}" for name, value in cookies_dict.items()])
    
    # 基本参数
    base_params = {
        "cityId": "1000008498",
        "limitSec": "300",
        "modId": "2400015082",
        "oaAcct": "zhengdewen",
        "postId": "10061760",
        "postOrgId": "1000010331",
        "sysId": "gdydDict",
        "sysUserCode": "zhengdewen"
    }
    
    # 添加当前时间戳
    timestamp = get_current_timestamp()
    params = {**base_params, "timestamp": timestamp}
    
    # 生成签名
    secret_key = "gdydDictSecret"
    sign = generate_sign(params, secret_key)
    
    # 添加签名到参数
    params["sign"] = sign
    
    print(f"[信息] 当前时间戳: {timestamp}")
    print(f"[信息] 生成的签名: {sign}")
    
    # 构建完整URL
    url = "http://dict.gmcc.net:30722/analysisService/gdyddict/yyfxSSO"
    
    headers = {
        'Host': "dict.gmcc.net:30722",
        'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        'Accept': "application/json, text/plain, */*",
        'Accept-Encoding': "gzip, deflate",
        'Pragma': "no-cache",
        'Cache-Control': "no-cache",
        'Referer': "http://dict.gmcc.net:30722/dictWeb/iframeTemplate/iframeAnalysis?index=1&title=%E7%AD%BE%E7%BA%A6%E9%A1%B9%E7%9B%AE%E6%98%8E%E7%BB%86",
        'Accept-Language': "zh-CN,zh;q=0.9",
        'Cookie': cookie_str
    }
    
    try:
        print("[信息] 正在请求SSO接口获取新的JSESSIONID...")
        session = requests.Session()
        response = session.get(url, params=params, headers=headers)
        response.raise_for_status()
        
        # 打印响应头中的Set-Cookie值
        if 'Set-Cookie' in response.headers:
            print(f"[信息] 响应头中的Set-Cookie: {response.headers['Set-Cookie']}")
        else:
            print("[信息] 响应头中没有Set-Cookie字段")
        
        # 获取响应中的cookies
        new_cookies = session.cookies.get_dict()
        print(f"[信息] 获取到新的cookies: {new_cookies}")
        
        # 如果没有获取到新的cookie，可能是请求参数有问题
        if not new_cookies:
            print("[警告] 未获取到新的cookie，可能是请求参数过期")
            print(f"[调试] 响应状态码: {response.status_code}")
            print(f"[调试] 响应内容: {response.text[:200]}...")
            # 尝试继续使用旧的cookie
            print("[信息] 将继续使用现有Cookie")
            return cookies_dict
        
        # 更新cookies字典，只添加新的cookie，不覆盖现有的
        for name, value in new_cookies.items():
            if name not in cookies_dict:  # 只在cookie不存在时添加
                cookies_dict[name] = value
                print(f"[信息] 添加新cookie: {name}={value}")
            else:
                print(f"[信息] 更新cookie: {name}")
                cookies_dict[name] = value  # 或者您可以选择不更新
        
        # 保存更新后的cookies
        save_cookies(cookies_dict, cookie_data)
        
        print("[成功] JSESSIONID已更新并添加到现有Cookie")
        return cookies_dict
    except requests.exceptions.RequestException as e:
        print(f"[错误] 网络请求失败: {e}")
        if 'response' in locals():
            print(f"[调试] 响应状态码: {response.status_code}")
            print(f"[调试] 响应头: {response.headers}")
            print(f"[调试] 响应内容: {response.text[:200]}...")  # 只打印前200个字符
        # 尝试继续使用旧的cookie
        print("[信息] 将继续使用现有Cookie")
        return cookies_dict
    except Exception as e:
        print(f"[错误] 获取JSESSIONID失败: {e}")
        if 'response' in locals():
            print(f"[调试] 响应状态码: {response.status_code}")
            print(f"[调试] 响应头: {response.headers}")
            print(f"[调试] 响应内容: {response.text[:200]}...")  # 只打印前200个字符
        # 尝试继续使用旧的cookie
        print("[信息] 将继续使用现有Cookie")
        return cookies_dict

if __name__ == "__main__":
    print("[开始] 开始更新JSESSIONID...")
    try:
        get_jsessionid()
        print("[完成] JSESSIONID更新完成")
    except Exception as e:
        print(f"[错误] 更新JSESSIONID过程中发生未处理的异常: {e}")
        exit(1)
