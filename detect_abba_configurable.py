#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
detect_abba_configurable.py - 可配置相似度阈值的ABBA项目关系检测脚本
功能：根据kuanbiao视图数据，基于项目建设内容相似度识别"疑似ABBA"的项目关系

使用方法：
python detect_abba_configurable.py --ct-threshold 0.7 --it-threshold 0.7

参数说明：
--ct-threshold: CT内容相似度阈值 (默认0.6，即60%)
--it-threshold: IT内容相似度阈值 (默认0.6，即60%)
"""

import pymysql
import pandas as pd
from datetime import datetime
import sys
import traceback
import argparse
from difflib import SequenceMatcher

# 数据库配置
DB_CONFIG = {
    'host': '*************',
    'port': 12136,
    'user': 'root',
    'password': '5eb9a11916e3a66d',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='基于内容相似度的ABBA项目关系检测')
    parser.add_argument('--ct-threshold', type=float, default=0.6, 
                       help='CT内容相似度阈值 (默认: 0.6)')
    parser.add_argument('--it-threshold', type=float, default=0.6,
                       help='IT内容相似度阈值 (默认: 0.6)')
    return parser.parse_args()

def connect_to_database():
    """连接到数据库"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        print(f"[成功] 已连接到数据库: {DB_CONFIG['database']}")
        return conn
    except Exception as e:
        print(f"[错误] 连接数据库失败: {e}")
        return None

def create_abba_table_configurable(conn):
    """创建可配置版dict_prj_abba表"""
    cursor = conn.cursor()
    try:
        # 删除已存在的表
        cursor.execute("DROP TABLE IF EXISTS dict_prj_abba")
        
        # 创建新表
        create_table_sql = """
        CREATE TABLE dict_prj_abba (
            id INT AUTO_INCREMENT PRIMARY KEY,
            场景分类 VARCHAR(100) COMMENT '所属行业/场景分类',
            企业A VARCHAR(500) COMMENT '企业A名称',
            企业B VARCHAR(500) COMMENT '企业B名称',
            A相关项目编码 TEXT COMMENT 'A企业相关项目编码列表',
            A相关项目数量 INT COMMENT 'A企业相关项目数量',
            B相关项目编码 TEXT COMMENT 'B企业相关项目编码列表',
            B相关项目数量 INT COMMENT 'B企业相关项目数量',
            CT内容相似度 DECIMAL(5,4) COMMENT 'CT内容最高相似度',
            IT内容相似度 DECIMAL(5,4) COMMENT 'IT内容最高相似度',
            相似项目对 TEXT COMMENT '相似的项目对信息',
            ABBA风险等级 VARCHAR(20) COMMENT '风险等级：高/中/低',
            CT阈值 DECIMAL(3,2) COMMENT '使用的CT相似度阈值',
            IT阈值 DECIMAL(3,2) COMMENT '使用的IT相似度阈值',
            检测时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '检测时间',
            备注 TEXT COMMENT '备注信息'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='基于内容相似度的疑似ABBA项目关系表'
        """
        
        cursor.execute(create_table_sql)
        conn.commit()
        print("[成功] dict_prj_abba表创建完成")
        return True
        
    except Exception as e:
        print(f"[错误] 创建dict_prj_abba表失败: {e}")
        traceback.print_exc()
        return False
    finally:
        cursor.close()

def get_kuanbiao_data_configurable(conn):
    """获取kuanbiao表数据（可配置版）"""
    try:
        print("[信息] 正在获取kuanbiao表数据...")
        
        query = """
        SELECT DISTINCT
            `签约上报日期`,
            `项目编码`,
            `项目名称`,
            `合同含税金额（万元）`,
            `收入侧客户`,
            `收入侧合同编码`,
            `项目建设内容及方案简介（CT）`,
            `项目建设内容及方案简介（IT）`,
            `所属行业`,
            `后向合同签约时间`,
            `成本侧供应商`
        FROM t_kuanbiao 
        WHERE `收入侧客户` IS NOT NULL 
        AND `收入侧客户` != '' 
        AND `所属行业` IS NOT NULL
        AND `所属行业` != ''
        AND (`项目建设内容及方案简介（CT）` IS NOT NULL AND `项目建设内容及方案简介（CT）` != ''
             OR `项目建设内容及方案简介（IT）` IS NOT NULL AND `项目建设内容及方案简介（IT）` != '')
        """
        
        df = pd.read_sql(query, conn)
        print(f"[信息] 获取到 {len(df)} 条有效数据")
        return df
        
    except Exception as e:
        print(f"[错误] 获取kuanbiao数据失败: {e}")
        traceback.print_exc()
        return None

def calculate_similarity(text1, text2):
    """计算两个文本的相似度"""
    if pd.isna(text1) or pd.isna(text2) or text1 == '' or text2 == '':
        return 0.0
    
    # 清理文本
    text1 = str(text1).strip()
    text2 = str(text2).strip()
    
    if text1 == text2:
        return 1.0
    
    # 使用SequenceMatcher计算相似度
    similarity = SequenceMatcher(None, text1, text2).ratio()
    return similarity

def detect_abba_configurable(df, ct_threshold, it_threshold):
    """基于可配置相似度检测ABBA关系"""
    print("[信息] 开始基于内容相似度检测ABBA关系...")
    print(f"[配置] CT相似度阈值: {ct_threshold*100}%")
    print(f"[配置] IT相似度阈值: {it_threshold*100}%")
    
    abba_results = []
    
    # 按所属行业分组处理
    for industry in df['所属行业'].unique():
        if pd.isna(industry) or industry == '':
            continue
            
        industry_data = df[df['所属行业'] == industry].copy()
        print(f"[信息] 正在分析行业: {industry} ({len(industry_data)}条记录)")
        
        # 按客户分组
        customer_projects = {}
        for _, row in industry_data.iterrows():
            customer = row['收入侧客户']
            if pd.notna(customer) and customer != '':
                if customer not in customer_projects:
                    customer_projects[customer] = []
                customer_projects[customer].append(row)
        
        # 只保留有多个项目的客户
        multi_project_customers = {k: v for k, v in customer_projects.items() if len(v) >= 2}
        
        if len(multi_project_customers) < 2:
            continue
            
        print(f"  - 发现 {len(multi_project_customers)} 个在{industry}行业有多个项目的客户")
        
        # 分析客户间的项目内容相似度
        customers = list(multi_project_customers.keys())
        
        for i in range(len(customers)):
            for j in range(i + 1, len(customers)):
                customer_a = customers[i]
                customer_b = customers[j]
                
                projects_a = multi_project_customers[customer_a]
                projects_b = multi_project_customers[customer_b]
                
                # 计算项目间的最高相似度
                max_ct_similarity = 0.0
                max_it_similarity = 0.0
                similar_pairs = []
                
                for proj_a in projects_a:
                    for proj_b in projects_b:
                        # 计算CT相似度
                        ct_sim = calculate_similarity(
                            proj_a['项目建设内容及方案简介（CT）'],
                            proj_b['项目建设内容及方案简介（CT）']
                        )
                        
                        # 计算IT相似度
                        it_sim = calculate_similarity(
                            proj_a['项目建设内容及方案简介（IT）'],
                            proj_b['项目建设内容及方案简介（IT）']
                        )
                        
                        if ct_sim > max_ct_similarity:
                            max_ct_similarity = ct_sim
                        
                        if it_sim > max_it_similarity:
                            max_it_similarity = it_sim
                        
                        # 如果相似度超过阈值，记录这对项目
                        if ct_sim >= ct_threshold or it_sim >= it_threshold:
                            similar_pairs.append({
                                'proj_a_code': proj_a['项目编码'],
                                'proj_a_name': proj_a['项目名称'],
                                'proj_b_code': proj_b['项目编码'],
                                'proj_b_name': proj_b['项目名称'],
                                'ct_similarity': ct_sim,
                                'it_similarity': it_sim
                            })
                
                # 如果找到相似的项目对，记录ABBA关系
                if similar_pairs:
                    # 计算风险等级
                    total_projects = len(projects_a) + len(projects_b)
                    avg_similarity = (max_ct_similarity + max_it_similarity) / 2
                    
                    if total_projects >= 6 and avg_similarity >= 0.8:
                        risk_level = "高"
                    elif total_projects >= 4 and avg_similarity >= 0.7:
                        risk_level = "中"
                    else:
                        risk_level = "低"
                    
                    # 构建相似项目对信息
                    pair_info = []
                    for pair in similar_pairs:
                        pair_info.append(f"{pair['proj_a_code']}↔{pair['proj_b_code']}(CT:{pair['ct_similarity']:.2f},IT:{pair['it_similarity']:.2f})")
                    
                    abba_result = {
                        '场景分类': industry,
                        '企业A': customer_a,
                        '企业B': customer_b,
                        'A相关项目编码': ','.join([p['项目编码'] for p in projects_a]),
                        'A相关项目数量': len(projects_a),
                        'B相关项目编码': ','.join([p['项目编码'] for p in projects_b]),
                        'B相关项目数量': len(projects_b),
                        'CT内容相似度': max_ct_similarity,
                        'IT内容相似度': max_it_similarity,
                        '相似项目对': ';'.join(pair_info),
                        'ABBA风险等级': risk_level,
                        'CT阈值': ct_threshold,
                        'IT阈值': it_threshold,
                        '备注': f'在{industry}行业中发现基于内容相似度的疑似ABBA关系，共{len(similar_pairs)}对相似项目'
                    }
                    
                    abba_results.append(abba_result)
                    
                    print(f"[发现] 基于相似度的ABBA关系: {customer_a} ↔ {customer_b}")
                    print(f"  - 行业: {industry}, 风险: {risk_level}")
                    print(f"  - 最高CT相似度: {max_ct_similarity:.2f}, 最高IT相似度: {max_it_similarity:.2f}")
                    print(f"  - 相似项目对数: {len(similar_pairs)}")
    
    print(f"[完成] 共检测到 {len(abba_results)} 个基于相似度的疑似ABBA关系")
    return abba_results

def save_abba_results_configurable(conn, abba_results):
    """保存ABBA检测结果到数据库（可配置版）"""
    if not abba_results:
        print("[信息] 没有检测到ABBA关系，无需保存")
        return True
    
    cursor = conn.cursor()
    try:
        print(f"[信息] 正在保存 {len(abba_results)} 条ABBA检测结果...")
        
        insert_sql = """
        INSERT INTO dict_prj_abba (
            场景分类, 企业A, 企业B, 
            A相关项目编码, A相关项目数量,
            B相关项目编码, B相关项目数量,
            CT内容相似度, IT内容相似度, 相似项目对,
            ABBA风险等级, CT阈值, IT阈值, 备注
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        for result in abba_results:
            cursor.execute(insert_sql, (
                result['场景分类'],
                result['企业A'],
                result['企业B'],
                result['A相关项目编码'],
                result['A相关项目数量'],
                result['B相关项目编码'],
                result['B相关项目数量'],
                result['CT内容相似度'],
                result['IT内容相似度'],
                result['相似项目对'],
                result['ABBA风险等级'],
                result['CT阈值'],
                result['IT阈值'],
                result['备注']
            ))
        
        conn.commit()
        print(f"[成功] 已保存 {len(abba_results)} 条ABBA检测结果")
        return True
        
    except Exception as e:
        print(f"[错误] 保存ABBA结果失败: {e}")
        traceback.print_exc()
        conn.rollback()
        return False
    finally:
        cursor.close()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    print("="*80)
    print("可配置相似度阈值的疑似ABBA项目关系检测程序")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"CT相似度阈值: {args.ct_threshold*100}%")
    print(f"IT相似度阈值: {args.it_threshold*100}%")
    print("="*80)
    
    # 连接数据库
    conn = connect_to_database()
    if not conn:
        print("[错误] 无法连接数据库，程序退出")
        sys.exit(1)
    
    try:
        # 创建结果表
        if not create_abba_table_configurable(conn):
            print("[错误] 创建结果表失败，程序退出")
            return
        
        # 获取数据
        df = get_kuanbiao_data_configurable(conn)
        if df is None or len(df) == 0:
            print("[警告] 没有获取到有效数据")
            return
        
        # 检测ABBA关系
        abba_results = detect_abba_configurable(df, args.ct_threshold, args.it_threshold)
        
        # 保存结果
        if save_abba_results_configurable(conn, abba_results):
            print(f"\n[完成] 程序执行完成: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"[结果] 共检测到 {len(abba_results)} 个基于相似度的疑似ABBA关系")
        
    except Exception as e:
        print(f"[错误] 程序执行失败: {e}")
        traceback.print_exc()
    finally:
        conn.close()
        print("[信息] 数据库连接已关闭")

if __name__ == "__main__":
    main()
