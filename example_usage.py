#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
密码加密工具使用示例
演示如何在实际项目中使用密码加密功能
"""

from password_utils import encrypt_password
import requests
import json


def simulate_login_process():
    """模拟完整的登录流程"""
    print("=== 模拟登录流程 ===")
    
    # 步骤1: 用户输入
    username = "testuser"
    password = "123456"
    
    print(f"用户名: {username}")
    print(f"密码: {password}")
    
    # 步骤2: 获取登录密钥（模拟从服务器获取）
    # 在实际应用中，这个密钥应该从服务器的 getloginKey() 接口获取
    login_key = "simulated_login_key_from_server"
    print(f"登录密钥: {login_key}")
    
    # 步骤3: 加密密码
    encrypted_password = encrypt_password(password, login_key)
    print(f"加密后密码: {encrypted_password[:50]}...")
    
    # 步骤4: 构造登录请求数据
    login_data = {
        "sysUserCode": username,
        "password": encrypted_password,
        "smsCode": "",  # 短信验证码
        "key": ""       # 其他验证信息
    }
    
    print(f"登录请求数据: {json.dumps(login_data, indent=2, ensure_ascii=False)}")
    
    return login_data


def get_login_key_from_server():
    """
    从服务器获取登录密钥的示例函数
    在实际应用中，需要调用对应的 API 接口
    """
    # 这里是示例代码，实际需要根据具体的 API 接口调整
    try:
        # 示例 API 调用
        # response = requests.get("https://your-server.com/api/getloginKey")
        # if response.status_code == 200:
        #     data = response.json()
        #     return data.get("loginKey", "")
        
        # 模拟返回
        return "real_login_key_from_server_123456"
    except Exception as e:
        print(f"获取登录密钥失败: {e}")
        return "fallback_key"


def encrypt_password_for_login(username: str, password: str) -> dict:
    """
    为登录准备加密密码的便捷函数
    
    Args:
        username: 用户名
        password: 原始密码
        
    Returns:
        包含加密信息的字典
    """
    # 获取登录密钥
    login_key = get_login_key_from_server()
    
    # 加密密码
    encrypted_password = encrypt_password(password, login_key)
    
    return {
        "username": username,
        "original_password": password,
        "encrypted_password": encrypted_password,
        "login_key": login_key
    }


def batch_encrypt_passwords():
    """批量加密密码示例"""
    print("\n=== 批量密码加密示例 ===")
    
    # 测试用例
    test_users = [
        ("admin", "admin123"),
        ("user1", "password1"),
        ("user2", "mypassword"),
        ("test", "test123"),
    ]
    
    login_key = "batch_test_key"
    
    print(f"使用统一登录密钥: {login_key}")
    print("-" * 60)
    
    for username, password in test_users:
        encrypted = encrypt_password(password, login_key)
        print(f"用户: {username:10} | 原密码: {password:12} | 加密后: {encrypted[:30]}...")


def compare_with_javascript():
    """
    与 JavaScript 版本对比测试
    用于验证加密结果的一致性
    """
    print("\n=== 与 JavaScript 版本对比 ===")
    
    # 使用相同的测试数据
    test_password = "123456"
    test_login_key = "testkey123"
    
    print(f"测试密码: {test_password}")
    print(f"测试密钥: {test_login_key}")
    
    # Python 版本加密
    python_result = encrypt_password(test_password, test_login_key, debug=True)
    
    print(f"\nPython 加密结果:")
    print(f"长度: {len(python_result)}")
    print(f"结果: {python_result}")
    
    print(f"\n💡 提示: 将此结果与 JavaScript 版本的结果进行对比")
    print(f"如果结果一致，说明 Python 实现正确")


if __name__ == "__main__":
    # 运行所有示例
    simulate_login_process()
    batch_encrypt_passwords()
    compare_with_javascript()
