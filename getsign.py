import hashlib
import time
from datetime import datetime
from urllib.parse import urle<PERSON><PERSON>

def generate_sign(params, secret_key):
    """
    生成签名
    
    Args:
        params (dict): 请求参数对象
        secret_key (str): 密钥
    
    Returns:
        str: 生成的签名
    """
    # 1. 将参数按键名排序
    sorted_keys = sorted(params.keys())
    
    # 2. 构建参数字符串，排除secretKey
    param_string = ''
    for key in sorted_keys:
        if key != 'secretKey':
            param_string += f"{key}={params[key]}&"
    
    # 注意：保留最后的&字符
    
    print(f"参数字符串: {param_string}")
    print(f"密钥: {secret_key}")
    
    # 4. 第一次哈希：paramString + secretKey
    first_hash = hashlib.md5((param_string + secret_key).encode('utf-8')).hexdigest()
    print(f"第一次哈希结果: {first_hash}")
    
    # 5. 第二次哈希：firstHash + secretKey
    sign = hashlib.md5((first_hash + secret_key).encode('utf-8')).hexdigest()
    print(f"第二次哈希结果: {sign}")
    
    return sign

def get_current_timestamp():
    """
    生成当前时间的时间戳，格式为YYYYMMDDHHmmss
    
    Returns:
        str: 格式化的时间戳
    """
    now = datetime.now()
    return now.strftime("%Y%m%d%H%M%S")

# 基本参数
base_params = {
    "cityId": "1000008498",
    "limitSec": "300",
    "modId": "2400015082",
    "oaAcct": "zhengdewen",
    "postId": "10061760",
    "postOrgId": "1000010331",
    "sysId": "gdydDict",
    "sysUserCode": "zhengdewen"
}

# 添加当前时间戳
timestamp = get_current_timestamp()
params = {**base_params, "timestamp": timestamp}

secret_key = "gdydDictSecret"

# 生成签名
sign = generate_sign(params, secret_key)

# 输出结果
print("\n最终结果:")
print(f"当前时间戳: {timestamp}")
print(f"生成的签名: {sign}")

# 构建完整URL
base_url = "http://dict.gmcc.net:30722/analysisService/gdyddict/yyfxSSO"
query_params = urlencode(params)
full_url = f"{base_url}?{query_params}&sign={sign}"

print("\n完整URL:")
print(full_url)