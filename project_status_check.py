#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
项目状态检查脚本
检查所有数据表和爬虫程序的状态
"""

import os
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), 'dict_romte'))
from dict_romte.config import get_db_config
import pymysql

def check_database_tables():
    """检查数据库表状态"""
    print("=" * 60)
    print("数据库表状态检查")
    print("=" * 60)
    
    try:
        DB_CONFIG = get_db_config('default')
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 检查所有dict_zonghe_*表
        cursor.execute("SHOW TABLES LIKE 'dict_zonghe_%'")
        tables = cursor.fetchall()
        
        print(f"找到 {len(tables)} 个数据表:")
        
        total_records = 0
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            total_records += count
            print(f"  {table_name}: {count} 条记录")
        
        print(f"\n总记录数: {total_records}")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"[错误] 数据库检查失败: {e}")
        return False

def check_crawler_files():
    """检查爬虫程序文件"""
    print("\n" + "=" * 60)
    print("爬虫程序文件检查")
    print("=" * 60)
    
    expected_files = [
        'dict_zonghe_queryProjectInfo.py',
        'dict_zonghe_queryProjectDemand.py',
        'dict_zonghe_qryContractByProject.py',
        'dict_zonghe_queryProjectPlanWithImplement.py',
        'dict_zonghe_queryProjectAmount.py',
        'dict_zonghe_queryProgram.py',
        'dict_zonghe_qryIncomeProgressByProject.py',
        'dict_zonghe_saleBiddingInfo.py',
        'dict_zonghe_qryWoListByProject.py',
        'dict_zonghe_getTodo.py',
        'dict_zonghe_queryProjectProdprcDict.py',
        'dict_zonghe_batchLoadCodeList.py',
        'dict_zonghe_loadCodeList.py'
    ]
    
    existing_files = []
    missing_files = []
    
    for file_name in expected_files:
        if os.path.exists(file_name):
            existing_files.append(file_name)
        else:
            missing_files.append(file_name)
    
    print(f"已存在的爬虫程序: {len(existing_files)}")
    for file_name in existing_files:
        file_size = os.path.getsize(file_name)
        print(f"  ✅ {file_name} ({file_size} bytes)")
    
    if missing_files:
        print(f"\n缺失的爬虫程序: {len(missing_files)}")
        for file_name in missing_files:
            print(f"  ❌ {file_name}")
    
    return len(missing_files) == 0

def check_sql_files():
    """检查SQL建表文件"""
    print("\n" + "=" * 60)
    print("SQL建表文件检查")
    print("=" * 60)
    
    sql_files = []
    for file in os.listdir('.'):
        if file.startswith('create_dict_zonghe_') and file.endswith('.sql'):
            sql_files.append(file)
    
    print(f"找到 {len(sql_files)} 个SQL文件:")
    for file_name in sql_files:
        file_size = os.path.getsize(file_name)
        print(f"  📄 {file_name} ({file_size} bytes)")
    
    return len(sql_files) >= 13

def check_support_files():
    """检查支持文件"""
    print("\n" + "=" * 60)
    print("支持文件检查")
    print("=" * 60)
    
    support_files = [
        'login2cookie.py',
        'cookies.txt',
        'run_all_crawlers.py',
        'test_single_project.py',
        'create_tables.py',
        'dict_romte/config.py'
    ]
    
    for file_name in support_files:
        if os.path.exists(file_name):
            file_size = os.path.getsize(file_name)
            print(f"  ✅ {file_name} ({file_size} bytes)")
        else:
            print(f"  ❌ {file_name} (缺失)")

def check_project_source():
    """检查项目数据源"""
    print("\n" + "=" * 60)
    print("项目数据源检查")
    print("=" * 60)
    
    try:
        DB_CONFIG = get_db_config('default')
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 检查v_distinct_project_id视图
        try:
            cursor.execute("SELECT COUNT(*) FROM v_distinct_project_id")
            count = cursor.fetchone()[0]
            print(f"  ✅ v_distinct_project_id视图: {count} 个项目ID")
        except:
            print(f"  ❌ v_distinct_project_id视图不存在")
            
            # 检查备用数据源
            try:
                cursor.execute("SELECT COUNT(DISTINCT `项目编码`) FROM sign_data_detail WHERE `项目编码` IS NOT NULL AND `项目编码` != ''")
                count = cursor.fetchone()[0]
                print(f"  ✅ sign_data_detail表备用数据源: {count} 个项目ID")
            except Exception as e:
                print(f"  ❌ 备用数据源也不可用: {e}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"[错误] 数据源检查失败: {e}")

def main():
    """主函数"""
    print("🔍 dict爬虫项目状态检查")
    print("检查时间:", __import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # 执行各项检查
    db_ok = check_database_tables()
    crawler_ok = check_crawler_files()
    sql_ok = check_sql_files()
    check_support_files()
    check_project_source()
    
    # 总结
    print("\n" + "=" * 60)
    print("检查总结")
    print("=" * 60)
    
    if db_ok and crawler_ok and sql_ok:
        print("🎉 项目状态良好，所有核心组件都已就绪！")
        print("\n📋 下一步操作建议:")
        print("1. 运行: python run_all_crawlers.py")
        print("2. 检查数据同步结果")
        print("3. 配置定时任务")
    else:
        print("⚠️ 项目存在一些问题，请检查上述报告")
        if not db_ok:
            print("- 数据库连接或表结构有问题")
        if not crawler_ok:
            print("- 爬虫程序文件不完整")
        if not sql_ok:
            print("- SQL建表文件不完整")

if __name__ == "__main__":
    main()
