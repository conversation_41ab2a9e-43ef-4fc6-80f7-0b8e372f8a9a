import os
import pymysql
import pandas as pd
from sqlalchemy import create_engine
import re

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'cmcc12345',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

# 目标文件夹
TARGET_FOLDER = "shichang"
IMPORTED_FOLDER = os.path.join(TARGET_FOLDER, "inserted2db")

def connect_to_database():
    """连接到MySQL数据库"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        print(f"[成功] 已连接到数据库: {DB_CONFIG['database']}")
        return conn
    except Exception as e:
        print(f"[错误] 连接数据库失败: {e}")
        exit(1)

def check_file_already_imported(conn, filename):
    """检查文件是否已经被导入过数据库"""
    cursor = conn.cursor()
    try:
        sql = "SELECT COUNT(*) FROM import_logs WHERE file_name = %s"
        cursor.execute(sql, (filename,))
        count = cursor.fetchone()[0]
        return count > 0
    except Exception as e:
        print(f"[警告] 检查文件导入记录失败: {e}")
        return False
    finally:
        cursor.close()

def import_market_share_data(conn, filepath, filename):
    """导入市场份额数据到数据库"""
    try:
        print(f"[信息] 正在导入市场份额数据: {filepath}")
        
        # 检查文件是否已经导入过
        if check_file_already_imported(conn, filename):
            print(f"[信息] 文件 {filename} 已经导入过数据库，跳过导入")
            # 将文件移动到已导入文件夹
            move_to_imported_folder(filepath, filename)
            return True

        # 创建SQLAlchemy引擎
        engine_url = f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?charset={DB_CONFIG['charset']}"
        engine = create_engine(engine_url)

        # 读取Excel文件中的"日监控"sheet
        print("[信息] 正在读取'日监控'sheet...")
        df_daily = pd.read_excel(filepath, sheet_name="日监控")
        
        # 读取Excel文件中的"号码明细"sheet
        print("[信息] 正在读取'号码明细'sheet...")
        df_detail = pd.read_excel(filepath, sheet_name="号码明细")
        
        # 检查数据库表结构并调整DataFrame列名
        cursor = conn.cursor()
        cursor.execute("DESCRIBE market_share_detail")
        table_columns = [column[0] for column in cursor.fetchall()]
        cursor.close()
        
        # 打印当前DataFrame的列和数据库表的列，帮助调试
        print("[信息] Excel文件中的列:", df_detail.columns.tolist())
        print("[信息] 数据库表中的列:", table_columns)
        
        # 检查是否需要重命名列
        if '证件' in df_detail.columns and '证件' not in table_columns:
            # 查找数据库中可能对应的列名
            possible_columns = ['身份证', '证件号码', '证件号', 'id_card']
            for col in possible_columns:
                if col in table_columns:
                    print(f"[信息] 将Excel中的'证件'列重命名为数据库中的'{col}'列")
                    df_detail = df_detail.rename(columns={'证件': col})
                    break
        
        # 检查是否有其他不匹配的列
        excel_columns = set(df_detail.columns)
        db_columns = set(table_columns)
        
        # 移除Excel中存在但数据库中不存在的列
        columns_to_drop = excel_columns - db_columns
        if columns_to_drop:
            print(f"[警告] 以下列在Excel中存在但在数据库中不存在，将被忽略: {', '.join(columns_to_drop)}")
            df_detail = df_detail.drop(columns=columns_to_drop)

        # 添加导入时间列，用于后续去重
        import_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        df_daily['导入时间'] = import_time
        df_detail['导入时间'] = import_time
        
        # 确保数据从第二行开始（第一行为表头）
        if len(df_daily) > 0:
            # 导入数据到market_share_daily表
            df_daily.to_sql('market_share_daily', engine, if_exists='append', index=False)
            print(f"[成功] 已导入 {len(df_daily)} 条日监控数据")
        else:
            print("[警告] '日监控'sheet中没有数据")
        
        if len(df_detail) > 0:
            # 导入数据到market_share_detail表
            df_detail.to_sql('market_share_detail', engine, if_exists='append', index=False)
            print(f"[成功] 已导入 {len(df_detail)} 条号码明细数据")
        else:
            print("[警告] '号码明细'sheet中没有数据")

        # 记录导入信息
        cursor = conn.cursor()
        
        # 记录日监控数据导入
        sql = """
        INSERT INTO import_logs (file_name, table_name, record_count, import_time)
        VALUES (%s, %s, %s, %s)
        """
        cursor.execute(sql, (filename, 'market_share_daily', len(df_daily), import_time))

        # 记录号码明细数据导入
        cursor.execute(sql, (filename, 'market_share_detail', len(df_detail), import_time))

        conn.commit()
        cursor.close()

        total_records = len(df_daily) + len(df_detail)
        print(f"[成功] 总共导入 {total_records} 条市场份额数据")
        
        # 将已导入的Excel文件移动到inserted2db文件夹
        move_to_imported_folder(filepath, filename)
        
        return True
    except Exception as e:
        print(f"[错误] 导入市场份额数据失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def move_to_imported_folder(filepath, filename):
    """将已导入数据的Excel文件移动到inserted2db文件夹"""
    try

非常抱歉，因达到了最大的token数，输出被截断。