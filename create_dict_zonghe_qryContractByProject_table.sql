-- 创建综合查询项目合同信息表
-- 对应接口：saleCenterApp/contractManage/qryContractByProject

DROP TABLE IF EXISTS `dict_zonghe_qryContractByProject`;

CREATE TABLE IF NOT EXISTS `dict_zonghe_qryContractByProject` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  
  -- 入参字段（来源于源数据表）
  `INPUT_LOGIN_NO` varchar(100) DEFAULT NULL COMMENT '入参-登录用户名',
  `INPUT_PROJECT_ID` varchar(100) DEFAULT NULL COMMENT '入参-项目ID（来源：v_distinct_project_id视图或sign_data_detail表项目编码字段）',
  `INPUT_PAGE_NUM` int DEFAULT NULL COMMENT '入参-页码',
  `INPUT_PAGE_SIZE` int DEFAULT NULL COMMENT '入参-页大小',
  
  -- 响应数据字段（基于接口返回的JSON结构）
  `CONTRACT_ID` varchar(100) DEFAULT NULL COMMENT '合同ID',
  `PROJECT_ID` varchar(100) DEFAULT NULL COMMENT '项目ID',
  `CONTRACT_NO` varchar(100) DEFAULT NULL COMMENT '合同编号',
  `CONTRACT_NAME` text DEFAULT NULL COMMENT '合同名称',
  `CONTRACT_SERIAL_NO` varchar(100) DEFAULT NULL COMMENT '合同序列号',
  `DRAFT_TYPE` varchar(50) DEFAULT NULL COMMENT '草稿类型',
  `RETROACTIVE` varchar(10) DEFAULT NULL COMMENT '是否追溯',
  `SUPERIOR_SIGNATURE` varchar(10) DEFAULT NULL COMMENT '上级签名',
  `CONTRACT_SUBJECT` varchar(100) DEFAULT NULL COMMENT '合同主体',
  `CON_SIGNED_SUBJECT` varchar(100) DEFAULT NULL COMMENT '合同签署主体',
  `INC_EXP_TYPE` varchar(100) DEFAULT NULL COMMENT '收支类型',
  `CONTACT_TEL` varchar(50) DEFAULT NULL COMMENT '联系电话',
  `SIGN_TIME` varchar(50) DEFAULT NULL COMMENT '签署时间',
  `SIGNED_START_DATE` varchar(50) DEFAULT NULL COMMENT '签署开始日期',
  `SIGNED_END_DATE` varchar(50) DEFAULT NULL COMMENT '签署结束日期',
  `CONTRACT_PERIOD` varchar(50) DEFAULT NULL COMMENT '合同期限',
  `CONTRACT_MONEY` decimal(15,2) DEFAULT NULL COMMENT '合同金额',
  `CONTRACT_MONEY_EX_TAX` decimal(15,2) DEFAULT NULL COMMENT '合同金额（不含税）',
  `AMOUNT_TYPE` varchar(100) DEFAULT NULL COMMENT '金额类型',
  `AMOUNT_INCLUDING_TAX` decimal(15,2) DEFAULT NULL COMMENT '含税金额',
  `AMOUNT_EXCLUDING_TAX` decimal(15,2) DEFAULT NULL COMMENT '不含税金额',
  `TAX_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT '税额',
  `AMOUNT_IMPROVE_INCLUDING_TAX` decimal(15,2) DEFAULT NULL COMMENT '改进含税金额',
  `AMOUNT_IMPROVE_EXCLUDING_TAX` decimal(15,2) DEFAULT NULL COMMENT '改进不含税金额',
  `TAX_AMOUNT_IMPROVE` decimal(15,2) DEFAULT NULL COMMENT '改进税额',
  `CURRENCY` varchar(20) DEFAULT NULL COMMENT '货币',
  `PAYMENT_DEPOSIT` varchar(10) DEFAULT NULL COMMENT '付款保证金',
  `IS_FRAMEWORK` varchar(10) DEFAULT NULL COMMENT '是否框架',
  `CONTRACT_CONTENT` text DEFAULT NULL COMMENT '合同内容',
  `CREATE_COMPANY_NO` varchar(100) DEFAULT NULL COMMENT '创建公司编号',
  `CREATE_COMPANY_NAME` text DEFAULT NULL COMMENT '创建公司名称',
  `CREATE_DEPT_CODE` varchar(100) DEFAULT NULL COMMENT '创建部门编码',
  `CREATE_DEPT_NAME` text DEFAULT NULL COMMENT '创建部门名称',
  `CREATE_STAFF` varchar(100) DEFAULT NULL COMMENT '创建员工',
  `CREATE_DATE` varchar(50) DEFAULT NULL COMMENT '创建日期',
  `UPDATE_STAFF` varchar(100) DEFAULT NULL COMMENT '更新员工',
  `UPDATE_DATE` varchar(50) DEFAULT NULL COMMENT '更新日期',
  `STATUS_CD` varchar(50) DEFAULT NULL COMMENT '状态编码',
  `STATUS_DATE` varchar(50) DEFAULT NULL COMMENT '状态日期',
  `CUST_NAME` text DEFAULT NULL COMMENT '客户名称',
  `CUST_ID` varchar(100) DEFAULT NULL COMMENT '客户ID',
  `REGION_CODE` varchar(50) DEFAULT NULL COMMENT '区域编码',
  `REGION_CODE_DESC` varchar(200) DEFAULT NULL COMMENT '区域描述',
  `TRADE` varchar(50) DEFAULT NULL COMMENT '行业',
  `TRADE_DESC` varchar(200) DEFAULT NULL COMMENT '行业描述',
  `FIRST_SCENE` varchar(100) DEFAULT NULL COMMENT '一级场景',
  `SECOND_SCENE` varchar(100) DEFAULT NULL COMMENT '二级场景',
  `PROJECT_STAGE` varchar(50) DEFAULT NULL COMMENT '项目阶段',
  `PROJECT_STAGE_NAME` varchar(200) DEFAULT NULL COMMENT '项目阶段名称',
  `PROJECT_PROGRESS` varchar(50) DEFAULT NULL COMMENT '项目进度',
  `PROJECT_PROGRESS_NAME` varchar(200) DEFAULT NULL COMMENT '项目进度名称',
  `ESTIMATED_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT '预计金额',
  `PROJECT_SCOPE` varchar(50) DEFAULT NULL COMMENT '项目范围',
  `REQUIREMENTS_TITEL` text DEFAULT NULL COMMENT '需求标题',
  `GROUP_ID` varchar(100) DEFAULT NULL COMMENT '集团ID',
  `BUILD_MODE` varchar(50) DEFAULT NULL COMMENT '建设模式',
  `BUILD_MODE_DESC` varchar(200) DEFAULT NULL COMMENT '建设模式描述',
  `ELECTION_MODE` varchar(50) DEFAULT NULL COMMENT '选举模式',
  `IS_BIG_PROJECT` varchar(10) DEFAULT NULL COMMENT '是否大项目',
  `IS_EXCELLENT_PROJECT` varchar(10) DEFAULT NULL COMMENT '是否优秀项目',
  `PARENT_PROJECT_ID` varchar(100) DEFAULT NULL COMMENT '父项目ID',
  `IS_SPEC_MARKET` varchar(10) DEFAULT NULL COMMENT '是否特殊市场',
  `IS_MAINTENANCE` varchar(50) DEFAULT NULL COMMENT '是否维护',
  `PROJECT_STATUS` varchar(50) DEFAULT NULL COMMENT '项目状态',
  `PROJECT_SOURCE` varchar(50) DEFAULT NULL COMMENT '项目来源',
  `BID_FLAG` varchar(10) DEFAULT NULL COMMENT '投标标志',
  `ORG_DESC` text DEFAULT NULL COMMENT '组织描述',
  `BID_FLAG_NAME` varchar(100) DEFAULT NULL COMMENT '投标标志名称',
  
  -- 系统字段
  `RUN_IP` varchar(50) DEFAULT NULL COMMENT '运行IP',
  `REQUEST_ID` varchar(100) DEFAULT NULL COMMENT '请求ID',
  `RETURN_CODE` varchar(10) DEFAULT NULL COMMENT '返回码',
  `RETURN_MSG` varchar(200) DEFAULT NULL COMMENT '返回消息',
  `USER_MSG` varchar(200) DEFAULT NULL COMMENT '用户消息',
  `DETAIL_MSG` text DEFAULT NULL COMMENT '详细消息',
  
  -- 元数据字段
  `import_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_input_project_id` (`INPUT_PROJECT_ID`),
  KEY `idx_contract_id` (`CONTRACT_ID`),
  KEY `idx_contract_no` (`CONTRACT_NO`),
  KEY `idx_project_id` (`PROJECT_ID`),
  KEY `idx_cust_id` (`CUST_ID`),
  KEY `idx_sign_time` (`SIGN_TIME`),
  KEY `idx_contract_money` (`CONTRACT_MONEY`),
  KEY `idx_status_cd` (`STATUS_CD`),
  KEY `idx_import_time` (`import_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='综合查询项目合同信息表';

-- 创建索引优化查询性能
CREATE INDEX idx_region_code ON dict_zonghe_qryContractByProject(REGION_CODE);
CREATE INDEX idx_trade ON dict_zonghe_qryContractByProject(TRADE);
CREATE INDEX idx_project_stage ON dict_zonghe_qryContractByProject(PROJECT_STAGE);
