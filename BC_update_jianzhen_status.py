#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BC_update_jianzhen_status.py - 更新远程br_opportunity表鉴真状态脚本
功能：根据本地视图v_br_jianzhen的数据，自动更新远程数据库br_opportunity表的鉴真状态和鉴真时间

更新逻辑：
- 从本地视图v_br_jianzhen读取鉴真数据（自动去重处理）
- 根据相同的id值匹配远程br_opportunity表记录
- 自动更新远程表的identify_status = 视图的"鉴真状态"字段值
- 自动更新远程表的identify_date = 视图的"更新日期"字段值
- 无需用户确认，自动执行更新操作
"""

import pymysql
from datetime import datetime
import sys
import traceback
import time

# 本地数据库配置 (视图数据源)
LOCAL_DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'cmcc12345',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

# 远程数据库配置 (目标表)
REMOTE_DB_CONFIG = {
    'host': '************',
    'port': 20029,
    'user': 'root',
    'password': '306d23a56b324b9f',
    'database': 'brch',
    'charset': 'utf8mb4'
}

# 目标表和视图
TARGET_TABLE = 'br_opportunity'  # 远程表
SOURCE_VIEW = 'v_br_jianzhen'    # 本地视图

def connect_to_database(config, db_type="数据库"):
    """连接到数据库"""
    try:
        print(f"[信息] 正在连接{db_type} {config['host']}:{config['port']}...")
        conn = pymysql.connect(**config)
        print(f"[成功] 已连接到{db_type}: {config['database']}")
        return conn
    except Exception as e:
        print(f"[错误] 连接{db_type}失败: {e}")
        return None

def check_view_exists(conn, view_name):
    """检查视图是否存在"""
    try:
        cursor = conn.cursor()
        cursor.execute(f"SHOW TABLES LIKE '{view_name}'")
        result = cursor.fetchone()
        cursor.close()
        
        if result:
            print(f"[信息] 视图 {view_name} 存在")
            return True
        else:
            print(f"[错误] 视图 {view_name} 不存在")
            return False
    except Exception as e:
        print(f"[错误] 检查视图存在性失败: {e}")
        return False

def check_table_exists(conn, table_name):
    """检查表是否存在"""
    try:
        cursor = conn.cursor()
        cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
        result = cursor.fetchone()
        cursor.close()
        
        if result:
            print(f"[信息] 表 {table_name} 存在")
            return True
        else:
            print(f"[错误] 表 {table_name} 不存在")
            return False
    except Exception as e:
        print(f"[错误] 检查表存在性失败: {e}")
        return False

def get_view_record_count(conn, view_name):
    """获取视图记录数"""
    try:
        cursor = conn.cursor()
        cursor.execute(f"SELECT COUNT(*) FROM {view_name}")
        count = cursor.fetchone()[0]
        cursor.close()
        print(f"[信息] 视图 {view_name} 当前记录数: {count}")
        return count
    except Exception as e:
        print(f"[错误] 获取视图记录数失败: {e}")
        return 0

def get_table_record_count(conn, table_name):
    """获取表记录数"""
    try:
        cursor = conn.cursor()
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        cursor.close()
        print(f"[信息] 表 {table_name} 当前记录数: {count}")
        return count
    except Exception as e:
        print(f"[错误] 获取表记录数失败: {e}")
        return 0

def get_view_data(local_conn):
    """从本地视图获取鉴真数据（去重处理）"""
    try:
        cursor = local_conn.cursor()
        # 使用DISTINCT去重，并按更新日期降序排列，确保获取最新的数据
        sql = f"""
        SELECT DISTINCT id, 鉴真状态, 更新日期
        FROM {SOURCE_VIEW}
        WHERE id IS NOT NULL
        ORDER BY id, 更新日期 DESC
        """
        cursor.execute(sql)
        all_data = cursor.fetchall()

        # 进一步去重，保留每个id的最新记录
        unique_data = {}
        for record in all_data:
            record_id, status, date = record
            if record_id not in unique_data:
                unique_data[record_id] = (record_id, status, date)
            else:
                # 如果已存在，比较日期，保留更新的
                existing_date = unique_data[record_id][2]
                if date and existing_date:
                    if date > existing_date:
                        unique_data[record_id] = (record_id, status, date)
                elif date and not existing_date:
                    unique_data[record_id] = (record_id, status, date)

        view_data = list(unique_data.values())
        cursor.close()

        original_count = len(all_data)
        final_count = len(view_data)
        print(f"[信息] 从视图获取到 {original_count} 条记录，去重后 {final_count} 条鉴真数据")

        if original_count != final_count:
            print(f"[警告] 发现 {original_count - final_count} 条重复记录已被去重")

        return view_data
    except Exception as e:
        print(f"[错误] 获取视图数据失败: {e}")
        return []

def diagnose_view_data(local_conn):
    """诊断视图数据，检查重复情况"""
    try:
        cursor = local_conn.cursor()

        # 检查总记录数
        cursor.execute(f"SELECT COUNT(*) FROM {SOURCE_VIEW}")
        total_count = cursor.fetchone()[0]

        # 检查唯一ID数量
        cursor.execute(f"SELECT COUNT(DISTINCT id) FROM {SOURCE_VIEW} WHERE id IS NOT NULL")
        unique_id_count = cursor.fetchone()[0]

        # 查找重复的ID
        cursor.execute(f"""
        SELECT id, COUNT(*) as count
        FROM {SOURCE_VIEW}
        WHERE id IS NOT NULL
        GROUP BY id
        HAVING COUNT(*) > 1
        ORDER BY count DESC
        LIMIT 10
        """)
        duplicates = cursor.fetchall()

        cursor.close()

        print(f"\n[诊断] 视图数据分析:")
        print(f"        总记录数: {total_count}")
        print(f"        唯一ID数: {unique_id_count}")
        print(f"        重复记录数: {total_count - unique_id_count}")

        if duplicates:
            print(f"        重复ID示例 (前10个):")
            for dup_id, count in duplicates:
                print(f"          ID {dup_id}: {count} 条记录")
        else:
            print(f"        ✅ 没有发现重复ID")

        return True

    except Exception as e:
        print(f"[错误] 诊断视图数据失败: {e}")
        return False

def get_update_candidates_count(local_conn, remote_conn):
    """获取需要更新的记录数 - 跨数据库比较"""
    try:
        # 先从本地视图获取数据
        view_data = get_view_data(local_conn)
        if not view_data:
            return 0

        remote_cursor = remote_conn.cursor()

        try:
            # 先删除可能存在的临时表
            remote_cursor.execute("DROP TEMPORARY TABLE IF EXISTS temp_jianzhen_data")

            # 创建临时表
            remote_cursor.execute("""
            CREATE TEMPORARY TABLE temp_jianzhen_data (
                id BIGINT,
                jianzhen_status CHAR(1),
                update_date DATETIME,
                PRIMARY KEY (id)
            )
            """)

            # 插入视图数据到临时表（使用INSERT IGNORE避免重复键错误）
            insert_sql = "INSERT IGNORE INTO temp_jianzhen_data (id, jianzhen_status, update_date) VALUES (%s, %s, %s)"
            inserted_count = 0

            for record in view_data:
                try:
                    remote_cursor.execute(insert_sql, record)
                    inserted_count += 1
                except Exception as insert_error:
                    print(f"[警告] 插入记录失败 ID={record[0]}: {insert_error}")
                    continue

            remote_conn.commit()
            print(f"[信息] 成功插入 {inserted_count} 条记录到临时表")

            # 统计需要更新的记录数
            count_sql = f"""
            SELECT COUNT(*)
            FROM {TARGET_TABLE} t
            INNER JOIN temp_jianzhen_data v ON t.id = v.id
            WHERE t.identify_status != v.jianzhen_status
               OR t.identify_date != v.update_date
               OR t.identify_status IS NULL
               OR t.identify_date IS NULL
            """

            remote_cursor.execute(count_sql)
            count = remote_cursor.fetchone()[0]

            print(f"[信息] 需要更新的记录数: {count}")
            return count

        finally:
            # 确保清理临时表
            try:
                remote_cursor.execute("DROP TEMPORARY TABLE IF EXISTS temp_jianzhen_data")
            except:
                pass
            remote_cursor.close()

    except Exception as e:
        print(f"[错误] 获取待更新记录数失败: {e}")
        print(f"[详细错误] {traceback.format_exc()}")
        return 0

def update_jianzhen_status_batch(local_conn, remote_conn, batch_size=1000):
    """批量更新远程数据库的鉴真状态和鉴真时间"""
    try:
        print(f"[信息] 开始批量更新远程数据库鉴真状态，批次大小: {batch_size}")
        start_time = time.time()

        # 从本地视图获取所有鉴真数据
        view_data = get_view_data(local_conn)
        if not view_data:
            print("[信息] 本地视图没有数据")
            return True

        remote_cursor = remote_conn.cursor()

        try:
            # 先删除可能存在的临时表
            remote_cursor.execute("DROP TEMPORARY TABLE IF EXISTS temp_jianzhen_data")

            # 创建临时表存储视图数据
            remote_cursor.execute("""
            CREATE TEMPORARY TABLE temp_jianzhen_data (
                id BIGINT,
                jianzhen_status CHAR(1),
                update_date DATETIME,
                PRIMARY KEY (id)
            )
            """)

            # 插入视图数据到临时表（使用INSERT IGNORE避免重复键错误）
            insert_sql = "INSERT IGNORE INTO temp_jianzhen_data (id, jianzhen_status, update_date) VALUES (%s, %s, %s)"
            inserted_count = 0

            for record in view_data:
                try:
                    remote_cursor.execute(insert_sql, record)
                    inserted_count += 1
                except Exception as insert_error:
                    print(f"[警告] 插入记录失败 ID={record[0]}: {insert_error}")
                    continue

            remote_conn.commit()
            print(f"[信息] 成功插入 {inserted_count} 条记录到临时表")

            # 获取需要更新的记录
            select_sql = f"""
            SELECT t.id, v.jianzhen_status, v.update_date
            FROM {TARGET_TABLE} t
            INNER JOIN temp_jianzhen_data v ON t.id = v.id
            WHERE t.identify_status != v.jianzhen_status
               OR t.identify_date != v.update_date
               OR t.identify_status IS NULL
               OR t.identify_date IS NULL
            """

            remote_cursor.execute(select_sql)
            update_records = remote_cursor.fetchall()
            total_count = len(update_records)

            if total_count == 0:
                print("[信息] 没有需要更新的记录")
                return True

            print(f"[信息] 找到 {total_count} 条需要更新的记录")

            # 分批更新数据
            total_updated = 0

            for i in range(0, total_count, batch_size):
                batch_records = update_records[i:i + batch_size]

                # 批量更新
                update_sql = f"""
                UPDATE {TARGET_TABLE}
                SET identify_status = %s,
                    identify_date = %s,
                    update_time = NOW()
                WHERE id = %s
                """

                # 准备更新数据
                update_data = []
                for record in batch_records:
                    record_id, jianzhen_status, update_date = record
                    update_data.append((jianzhen_status, update_date, record_id))

                # 执行批量更新
                remote_cursor.executemany(update_sql, update_data)
                remote_conn.commit()

                total_updated += len(batch_records)

                # 计算进度和预估剩余时间
                progress = total_updated / total_count * 100
                elapsed_time = time.time() - start_time
                if total_updated > 0:
                    estimated_total_time = elapsed_time * total_count / total_updated
                    remaining_time = estimated_total_time - elapsed_time
                    print(f"[进度] {total_updated}/{total_count} ({progress:.1f}%) - "
                          f"已用时: {elapsed_time:.1f}s, 预计剩余: {remaining_time:.1f}s")
                else:
                    print(f"[进度] {total_updated}/{total_count} ({progress:.1f}%)")

            total_time = time.time() - start_time

            print(f"[成功] 批量更新完成! 共更新 {total_updated} 条记录")
            print(f"[统计] 总耗时: {total_time:.2f}秒, 平均速度: {total_updated/total_time:.1f} 条/秒")
            return True

        finally:
            # 确保清理临时表
            try:
                remote_cursor.execute("DROP TEMPORARY TABLE IF EXISTS temp_jianzhen_data")
            except:
                pass
            remote_cursor.close()

    except Exception as e:
        print(f"[错误] 批量更新失败: {e}")
        print(traceback.format_exc())
        remote_conn.rollback()
        return False

def validate_update_results(local_conn, remote_conn, sample_size=5):
    """验证更新结果 - 跨数据库验证"""
    try:
        print(f"[信息] 正在验证更新结果（样本数: {sample_size}）...")

        # 从本地视图获取样本数据
        local_cursor = local_conn.cursor()
        local_cursor.execute(f"""
        SELECT id, 鉴真状态, 更新日期
        FROM {SOURCE_VIEW}
        ORDER BY RAND()
        LIMIT {sample_size}
        """)
        view_samples = local_cursor.fetchall()
        local_cursor.close()

        if not view_samples:
            print("[信息] 没有视图数据可验证")
            return True

        # 从远程表获取对应的记录
        remote_cursor = remote_conn.cursor()
        match_count = 0

        for i, view_sample in enumerate(view_samples):
            view_id, view_status, view_date = view_sample

            # 查询远程表对应记录
            remote_cursor.execute(f"""
            SELECT identify_status, identify_date
            FROM {TARGET_TABLE}
            WHERE id = %s
            """, (view_id,))

            remote_result = remote_cursor.fetchone()

            if remote_result:
                table_status, table_date = remote_result

                status_match = str(table_status) == str(view_status)
                date_match = table_date == view_date

                if status_match and date_match:
                    match_count += 1
                    print(f"[验证] 样本 {i+1}/{sample_size}: ✅ 匹配 (ID: {view_id})")
                else:
                    print(f"[验证] 样本 {i+1}/{sample_size}: ❌ 不匹配 (ID: {view_id})")
                    print(f"        状态: 远程表={table_status}, 本地视图={view_status}, 匹配={status_match}")
                    print(f"        日期: 远程表={table_date}, 本地视图={view_date}, 匹配={date_match}")
            else:
                print(f"[验证] 样本 {i+1}/{sample_size}: ❌ 远程表中未找到记录 (ID: {view_id})")

        remote_cursor.close()

        success_rate = match_count / len(view_samples) * 100
        print(f"[验证结果] 抽样验证成功率: {success_rate:.1f}% ({match_count}/{len(view_samples)})")

        return success_rate >= 90  # 90%以上认为验证通过

    except Exception as e:
        print(f"[错误] 验证更新结果失败: {e}")
        return False

def get_update_statistics(remote_conn):
    """获取远程数据库更新统计信息"""
    try:
        cursor = remote_conn.cursor()

        # 统计各种鉴真状态的数量
        status_sql = f"""
        SELECT
            identify_status,
            COUNT(*) as count
        FROM {TARGET_TABLE}
        WHERE identify_status IS NOT NULL
        GROUP BY identify_status
        ORDER BY identify_status
        """

        cursor.execute(status_sql)
        status_stats = cursor.fetchall()

        print("\n[统计] 远程数据库鉴真状态分布:")
        for status, count in status_stats:
            status_desc = "匹配" if status == '1' else "不匹配" if status == '2' else f"状态{status}"
            print(f"        {status_desc} (状态={status}): {count} 条")

        # 统计最近更新的记录
        recent_sql = f"""
        SELECT COUNT(*)
        FROM {TARGET_TABLE}
        WHERE identify_date >= CURDATE()
        """

        cursor.execute(recent_sql)
        recent_count = cursor.fetchone()[0]
        print(f"[统计] 今日更新的记录数: {recent_count}")

        # 统计空值情况
        null_sql = f"""
        SELECT
            SUM(CASE WHEN identify_status IS NULL THEN 1 ELSE 0 END) as null_status_count,
            SUM(CASE WHEN identify_date IS NULL THEN 1 ELSE 0 END) as null_date_count
        FROM {TARGET_TABLE}
        """

        cursor.execute(null_sql)
        null_stats = cursor.fetchone()
        null_status_count, null_date_count = null_stats

        print(f"[统计] 鉴真状态为空的记录数: {null_status_count}")
        print(f"[统计] 鉴真时间为空的记录数: {null_date_count}")

        cursor.close()
        return True

    except Exception as e:
        print(f"[错误] 获取统计信息失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 80)
    print("BC_update_jianzhen_status.py - 更新远程br_opportunity表鉴真状态")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)

    # 连接本地数据库
    print("\n1. 连接本地数据库...")
    local_conn = connect_to_database(LOCAL_DB_CONFIG, "本地数据库")
    if not local_conn:
        print("[错误] 无法连接本地数据库，程序退出")
        sys.exit(1)

    # 连接远程数据库
    print("\n2. 连接远程数据库...")
    remote_conn = connect_to_database(REMOTE_DB_CONFIG, "远程数据库")
    if not remote_conn:
        print("[错误] 无法连接远程数据库，程序退出")
        local_conn.close()
        sys.exit(1)

    try:
        # 检查本地视图是否存在
        print(f"\n3. 检查本地视图 {SOURCE_VIEW}...")
        if not check_view_exists(local_conn, SOURCE_VIEW):
            print(f"[错误] 本地视图 {SOURCE_VIEW} 不存在")
            return False

        # 检查远程目标表是否存在
        print(f"\n4. 检查远程目标表 {TARGET_TABLE}...")
        if not check_table_exists(remote_conn, TARGET_TABLE):
            print(f"[错误] 远程表 {TARGET_TABLE} 不存在")
            return False

        # 显示数据统计和诊断
        print(f"\n5. 数据统计和诊断...")
        view_count = get_view_record_count(local_conn, SOURCE_VIEW)
        table_count = get_table_record_count(remote_conn, TARGET_TABLE)

        # 诊断视图数据
        diagnose_view_data(local_conn)

        update_count = get_update_candidates_count(local_conn, remote_conn)

        print(f"\n[统计] 本地视图记录数: {view_count}")
        print(f"[统计] 远程表记录数: {table_count}")
        print(f"[统计] 需要更新的记录数: {update_count}")

        if update_count == 0:
            print("\n[信息] 没有需要更新的记录，程序结束")
            return True

        # 自动执行更新操作
        print(f"\n6. 开始执行更新操作...")
        print(f"[信息] 将更新远程数据库 {update_count} 条记录的鉴真状态和鉴真时间")
        print(f"[信息] 远程数据库: {REMOTE_DB_CONFIG['host']}:{REMOTE_DB_CONFIG['port']}/{REMOTE_DB_CONFIG['database']}")

        # 执行批量更新
        print(f"\n7. 执行批量更新...")
        if not update_jianzhen_status_batch(local_conn, remote_conn):
            print("[错误] 批量更新失败")
            return False

        # 验证更新结果
        print(f"\n8. 验证更新结果...")
        validation_passed = validate_update_results(local_conn, remote_conn)
        if validation_passed:
            print("[验证] ✅ 更新结果验证通过")
        else:
            print("[验证] ❌ 更新结果验证未通过，请检查数据")

        # 显示最终统计
        print(f"\n9. 最终统计信息...")
        get_update_statistics(remote_conn)

        print("\n" + "=" * 80)
        print("✅ 远程数据库鉴真状态更新完成!")
        print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        return True

    except Exception as e:
        print(f"\n[错误] 程序执行过程中出现异常: {e}")
        print(traceback.format_exc())
        return False
    finally:
        # 关闭数据库连接
        if local_conn:
            local_conn.close()
            print("[信息] 本地数据库连接已关闭")
        if remote_conn:
            remote_conn.close()
            print("[信息] 远程数据库连接已关闭")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)


    #  1、下载邮件的拍照数据
    #  2、拍照数据入库
    #  3、拍照数据去重
    #  3、同步下载bc公网的集团基础表
    #  4、同步下载bc公网的商机数据表
    #  5、形成视图v_br_jianzhe视图
    #  6、更新bc公网的商机数据表（更新鉴真状态与鉴真时间  ）
    #


