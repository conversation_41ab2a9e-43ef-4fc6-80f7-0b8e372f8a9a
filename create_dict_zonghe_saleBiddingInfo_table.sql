-- 创建综合查询查询销售投标信息表
-- 对应接口：saleCenterApp//biddingSupport/saleBiddingInfo

DROP TABLE IF EXISTS `dict_zonghe_saleBiddingInfo`;

CREATE TABLE IF NOT EXISTS `dict_zonghe_saleBiddingInfo` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  
  -- 入参字段（来源于源数据表）
  `INPUT_LOGIN_NO` varchar(100) DEFAULT NULL COMMENT '入参-登录用户名',
  `INPUT_PROJECT_ID` varchar(100) DEFAULT NULL COMMENT '入参-项目ID（来源：v_distinct_project_id视图或sign_data_detail表项目编码字段）',
  
  -- 响应数据字段（基于接口返回的JSON结构）
  `BIDDING_ID` varchar(200) DEFAULT NULL COMMENT 'BIDDING_ID',
  `PROJECT_ID` varchar(200) DEFAULT NULL COMMENT 'PROJECT_ID',
  `BIDDING_TYPE` varchar(200) DEFAULT NULL COMMENT 'BIDDING_TYPE',
  `BIDDING_TYPE_DESC` text DEFAULT NULL COMMENT 'BIDDING_TYPE_DESC',
  `BIDDING_STATUS` varchar(200) DEFAULT NULL COMMENT 'BIDDING_STATUS',
  `BIDDING_STATUS_DESC` text DEFAULT NULL COMMENT 'BIDDING_STATUS_DESC',
  `BIDDING_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'BIDDING_AMOUNT',
  `BIDDING_DATE` varchar(50) DEFAULT NULL COMMENT 'BIDDING_DATE',
  `SUBMIT_DATE` varchar(50) DEFAULT NULL COMMENT 'SUBMIT_DATE',
  `RESULT_DATE` varchar(50) DEFAULT NULL COMMENT 'RESULT_DATE',
  `BIDDING_RESULT` varchar(200) DEFAULT NULL COMMENT 'BIDDING_RESULT',
  `BIDDING_RESULT_DESC` text DEFAULT NULL COMMENT 'BIDDING_RESULT_DESC',
  `WIN_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'WIN_AMOUNT',
  `COMPETITOR_INFO` text DEFAULT NULL COMMENT 'COMPETITOR_INFO',
  `BIDDING_STRATEGY` varchar(200) DEFAULT NULL COMMENT 'BIDDING_STRATEGY',
  `RISK_ASSESSMENT` varchar(200) DEFAULT NULL COMMENT 'RISK_ASSESSMENT',
  `SUCCESS_RATE` varchar(200) DEFAULT NULL COMMENT 'SUCCESS_RATE',
  `CREATE_STAFF` varchar(200) DEFAULT NULL COMMENT 'CREATE_STAFF',
  `CREATE_DATE` varchar(50) DEFAULT NULL COMMENT 'CREATE_DATE',
  `UPDATE_STAFF` varchar(50) DEFAULT NULL COMMENT 'UPDATE_STAFF',
  `UPDATE_DATE` varchar(50) DEFAULT NULL COMMENT 'UPDATE_DATE',
  
  -- 系统字段
  `RUN_IP` varchar(50) DEFAULT NULL COMMENT '运行IP',
  `REQUEST_ID` varchar(100) DEFAULT NULL COMMENT '请求ID',
  `RETURN_CODE` varchar(10) DEFAULT NULL COMMENT '返回码',
  `RETURN_MSG` varchar(200) DEFAULT NULL COMMENT '返回消息',
  `USER_MSG` varchar(200) DEFAULT NULL COMMENT '用户消息',
  `DETAIL_MSG` text DEFAULT NULL COMMENT '详细消息',
  
  -- 元数据字段
  `import_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_import_time` (`import_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='综合查询表';
