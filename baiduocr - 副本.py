import requests
import base64
import ddddocr

from io import BytesIO

# ========== 配置信息 ==========
# USERNAME = "suyuquan"  # TODO: 替换为你的用户名
# PASSWORD = "9CPMpvZdobjzIldZlw4s4jMhCVI%2FB87cKsVkpxgspSAGf4yYWLScRA%3D%3D"  # TODO: 替换为你的密码

# USERNAME = "liaochulin"  # TODO: 替换为你的用户名
# PASSWORD = "YpRi8GJ37tCYW2ULw8wAA01x9/61loLYiY9k3+xgswgGf4yYWLScRA=="  # TODO: 替换为你的密码

USERNAME = "zhengdewen"  # TODO: 替换为你的用户名
PASSWORD = "G5bycftH60tEJAMvIZ3awFqJt9Gzk1qlMu9X5T0MtOgGf4yYWLScRA=="  # TODO: 替换为你的密码



# 百度 OCR 凭证（替换为你的实际值）
BAIDU_API_KEY = "vEKkNsQVoKlNBGrPrycVIqiq"      # TODO
BAIDU_SECRET_KEY = "LIxdVLtDL4zRsttU20KqO1EX31V0aFC6"

# ========== 获取百度 Access Token ==========
def get_baidu_access_token(api_key, secret_key):
    print("[信息] 获取百度 Access Token...")
    url = "https://aip.baidubce.com/oauth/2.0/token"
    params = {
        "grant_type": "client_credentials",
        "client_id": api_key,
        "client_secret": secret_key
    }
    try:
        response = requests.post(url, params=params)
        response.raise_for_status()
        return response.json()["access_token"]
    except Exception as e:
        print(f"[错误] 获取百度 Access Token 失败: {e}")
        exit(1)

# ========== 步骤 1：获取验证码 ==========
def get_captcha():
    url = "http://dict.gmcc.net:30722/dictWeb/gatewayService/bss-base-operation/base/getVerificationImage"
    try:
        response = requests.post(url)
        response.raise_for_status()
        data = response.json()
        body = data["ROOT"]["BODY"]
        if body["RETURN_CODE"] != "0":
            raise Exception(f"验证码获取失败: {body['RETURN_MSG']}")
        base64_str = body["OUT_DATA"]["base64String"]
        key = body["OUT_DATA"]["key"]
        print( base64_str, key)
        return base64_str, key
    except Exception as e:
        print(f"[错误] 获取验证码失败: {e}")
        exit(1)

# ========== 步骤 2：使用百度 OCR 识别验证码 ==========
def recognize_captcha_baidu(base64_str, access_token):
    print("[信息] 使用百度 OCR 识别验证码...")
    url = f"https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic?access_token={access_token}"
    headers = {'Content-Type': 'application/x-www-form-urlencoded'}
    img_data = base64.b64decode(base64_str)
    base64_image = base64.b64encode(img_data).decode()

    data = {"image": base64_image}


    try:
        response = requests.post(url, headers=headers, data=data)
        # response = requests.post(url, headers=headers, data=data, proxies=proxies)
        response.raise_for_status()
        result = response.json()
        if "words_result" in result and result["words_result"]:
            captcha = result["words_result"][0]["words"].strip()
            print(f"[信息] 识别出的验证码: {captcha}")
            return captcha
        else:
            raise Exception("未识别出任何文字")
    except Exception as e:
        print(f"[错误] 百度 OCR 识别失败: {e}")
        print(f"[调试] 响应内容: {response.text if 'response' in locals() else '无响应'}")
        exit(1)

# ========== 步骤 3：使用验证码登录 ==========
def login(username, password, captcha, key):
    #http://dict.gmcc.net:30722/dictWeb/gatewayService/gateway/login?sysUserCode=liaochulin&password=YpRi8GJ37tCYW2ULw8wAA01x9%2F61loLYiY9k3%2BxgswgGf4yYWLScRA%3D%3D&smsCode=95ht7&key=1745333416451440890&
    url = "http://dict.gmcc.net:30722/dictWeb/gatewayService/gateway/login"
    params = {
        "sysUserCode": username,
        "password": password,
        "smsCode": captcha,
        "key": key
    }
    try:
        session = requests.Session()
        response = session.get(url, params=params)
        response.raise_for_status()
        print(f"[信息] 登录成功 {response}")
        return session.cookies
    except Exception as e:
        print(f"[错误] 登录失败: {e}")
        print(f"[调试] 响应内容: {response.text if 'response' in locals() else '无响应'}")
        exit(1)

# ========== 步骤 4：保存 Cookie ==========
def save_cookies(cookies, filename="cookies.txt"):
    try:
        with open(filename, "w") as f:
            for cookie in cookies:
                f.write(f"{cookie.name}={cookie.value}\n")
        print(f"[信息] Cookie 已保存到 {filename}")
    except Exception as e:
        print(f"[错误] 保存 Cookie 失败: {e}")
        exit(1)


def ddocr(base64_str):
    # 初始化 OCR 对象
    ocr = ddddocr.DdddOcr()

    # 你的 Base64 字符串（去掉头部的 data:image/png;base64,）
    # base64_str = "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"
    # 这里填入你的完整 base64 字符串

    # 解码 base64 成 bytes
    img_bytes = base64.b64decode(base64_str)

    # 用 ddddocr 识别
    captcha = ocr.classification(img_bytes)
    print(captcha)
    return captcha

# ========== 主流程 ==========
def main():
    print("🚀 开始执行验证码登录流程（百度 OCR）...")

    # access_token = get_baidu_access_token(BAIDU_API_KEY, BAIDU_SECRET_KEY)
    base64_str, key = get_captcha()
    # captcha_text = recognize_captcha_baidu(base64_str, access_token)

    captcha_text = ddocr(base64_str)
    USERNAME = "zhengdewen"  # TODO: 替换为你的用户名
    PASSWORD = "G5bycftH60tEJAMvIZ3awFqJt9Gzk1qlMu9X5T0MtOgGf4yYWLScRA=="  # TODO: 替换为你的密码

    print(f'{USERNAME}, {captcha_text}, {key}, {PASSWORD}')
    cookies = login(USERNAME, PASSWORD, captcha_text, key)
    print(cookies)
    save_cookies(cookies)


if __name__ == "__main__":
    main()