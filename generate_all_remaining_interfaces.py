#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
批量生成所有剩余接口的数据表和爬虫程序
基于接口分析结果.md自动提取和生成
"""

import re
import os
import json

def extract_interface_details(content, interface_num):
    """提取单个接口的详细信息"""
    # 查找接口开始位置
    pattern = rf'## {interface_num}\. (.+?)\n\*\*中文名称\*\*: (.+?)\n'
    match = re.search(pattern, content)
    
    if not match:
        return None
    
    api_path = match.group(1).strip()
    chinese_name = match.group(2).strip()
    
    # 查找下一个接口的位置，确定当前接口的结束位置
    next_interface_pattern = rf'## {interface_num + 1}\.'
    next_match = re.search(next_interface_pattern, content)
    
    if next_match:
        interface_content = content[match.start():next_match.start()]
    else:
        interface_content = content[match.start():]
    
    # 提取请求参数
    request_params = []
    param_pattern = r'"([A-Z_]+)"\s*:\s*"([^"]*)"'
    param_matches = re.findall(param_pattern, interface_content)
    
    for param_name, param_value in param_matches:
        if param_name not in ['ROOT', 'HEADER', 'BODY', 'OPR_INFO']:
            request_params.append(param_name)
    
    # 判断是否需要PROJECT_ID
    needs_project_id = 'PROJECT_ID' in request_params or 'projectId' in request_params
    
    return {
        'number': interface_num,
        'api_path': api_path,
        'chinese_name': chinese_name,
        'request_params': request_params,
        'needs_project_id': needs_project_id
    }

def load_all_interfaces():
    """加载所有86个接口信息"""
    try:
        with open('dict_romte/接口分析结果.md', 'r', encoding='utf-8') as f:
            content = f.read()
        
        interfaces = []
        for i in range(1, 87):  # 1到86
            interface_info = extract_interface_details(content, i)
            if interface_info:
                interfaces.append(interface_info)
        
        print(f"[成功] 提取到 {len(interfaces)} 个接口信息")
        return interfaces
        
    except Exception as e:
        print(f"[错误] 加载接口失败: {e}")
        return []

def get_completed_interfaces():
    """获取已完成的接口列表"""
    completed = []
    for file in os.listdir('.'):
        if file.startswith('dict_zonghe_') and file.endswith('.py'):
            # 提取接口名称
            interface_name = file.replace('dict_zonghe_', '').replace('.py', '')
            completed.append(interface_name)
    
    return completed

def generate_sql_table(interface):
    """生成数据表SQL"""
    api_name = interface['api_path'].split('/')[-1]
    table_name = f'dict_zonghe_{api_name}'
    
    sql_content = f"""-- 创建综合查询{interface['chinese_name']}表
-- 对应接口：{interface['api_path']}

DROP TABLE IF EXISTS `{table_name}`;

CREATE TABLE IF NOT EXISTS `{table_name}` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  
  -- 入参字段（来源于源数据表）
  `INPUT_LOGIN_NO` varchar(100) DEFAULT NULL COMMENT '入参-登录用户名',
"""
    
    # 添加入参字段
    if interface['needs_project_id']:
        sql_content += "  `INPUT_PROJECT_ID` varchar(100) DEFAULT NULL COMMENT '入参-项目ID（来源：v_distinct_project_id视图或sign_data_detail表项目编码字段）',\n"
    
    # 为其他参数添加字段
    for param in interface['request_params']:
        if param not in ['LOGIN_NO', 'PROJECT_ID', 'projectId']:
            sql_content += f"  `INPUT_{param}` varchar(200) DEFAULT NULL COMMENT '入参-{param}',\n"
    
    sql_content += """  
  -- 响应数据字段（基于接口返回的JSON结构）
  `RESPONSE_DATA` text DEFAULT NULL COMMENT '接口返回的完整JSON数据',
  `RESPONSE_COUNT` int DEFAULT 0 COMMENT '返回数据条数',
  
  -- 系统字段
  `RUN_IP` varchar(50) DEFAULT NULL COMMENT '运行IP',
  `REQUEST_ID` varchar(100) DEFAULT NULL COMMENT '请求ID',
  `RETURN_CODE` varchar(10) DEFAULT NULL COMMENT '返回码',
  `RETURN_MSG` varchar(200) DEFAULT NULL COMMENT '返回消息',
  `USER_MSG` varchar(200) DEFAULT NULL COMMENT '用户消息',
  `DETAIL_MSG` text DEFAULT NULL COMMENT '详细消息',
  
  -- 元数据字段
  `import_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
"""
    
    if interface['needs_project_id']:
        sql_content += "  KEY `idx_input_project_id` (`INPUT_PROJECT_ID`),\n"
    
    sql_content += f"""  KEY `idx_import_time` (`import_time`),
  KEY `idx_return_code` (`RETURN_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='综合查询{interface['chinese_name']}表';
"""
    
    return sql_content

def generate_crawler_program(interface):
    """生成爬虫程序"""
    api_name = interface['api_path'].split('/')[-1]
    table_name = f'dict_zonghe_{api_name}'
    file_name = f'{table_name}.py'
    
    # 构建请求体参数
    body_params = []
    for param in interface['request_params']:
        if param == 'PROJECT_ID' or param == 'projectId':
            body_params.append(f'        body["{param}"] = project_id')
        elif param != 'LOGIN_NO':
            # 为其他参数设置默认值
            if 'PAGE' in param:
                body_params.append(f'        body["{param}"] = 1')
            elif 'SIZE' in param:
                body_params.append(f'        body["{param}"] = 10')
            else:
                body_params.append(f'        body["{param}"] = ""')
    
    body_params_code = '\n'.join(body_params) if body_params else '        pass  # 无额外参数'
    
    crawler_content = f'''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
综合查询{interface['chinese_name']}爬虫程序
对应接口：{interface['api_path']}
功能：从dict系统获取{interface['chinese_name']}并同步到MySQL数据库
"""

import sys
import os
import json
import time
import requests
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
import pymysql
from datetime import datetime

# 添加配置文件路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'dict_romte'))
from dict_romte.config import get_db_config, get_login_credentials

# 数据库配置
DB_CONFIG = get_db_config('default')

# API配置
API_URL = "https://dict.gmcc.net:30722/dictWeb/gatewayService/{interface['api_path']}"

def load_cookies():
    """从cookies.txt文件加载cookie"""
    try:
        with open('cookies.txt', 'r', encoding='utf-8') as f:
            cookie_data = json.load(f)
        
        cookies = {{}}
        for cookie in cookie_data.get('cookies', []):
            cookies[cookie['name']] = cookie['value']
        
        print(f"[信息] 成功加载 {{len(cookies)}} 个Cookie")
        return cookies
    except Exception as e:
        print(f"[错误] 加载Cookie失败: {{e}}")
        return {{}}

def get_project_ids():
    """从数据库获取项目ID列表"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 优先使用v_distinct_project_id视图
        try:
            cursor.execute("SELECT project_id FROM v_distinct_project_id LIMIT 20")
            project_ids = [row[0] for row in cursor.fetchall()]
            print(f"[信息] 从v_distinct_project_id视图获取到 {{len(project_ids)}} 个项目ID")
        except:
            # 备用方案：从sign_data_detail表获取
            cursor.execute("SELECT DISTINCT `项目编码` FROM sign_data_detail WHERE `项目编码` IS NOT NULL AND `项目编码` != '' LIMIT 20")
            project_ids = [row[0] for row in cursor.fetchall()]
            print(f"[信息] 从sign_data_detail表获取到 {{len(project_ids)}} 个项目ID")
        
        return project_ids
        
    except Exception as e:
        print(f"[错误] 获取项目ID失败: {{e}}")
        return []
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def build_request_headers(cookies):
    """构建请求头"""
    cookie_str = "; ".join([f"{{name}}={{value}}" for name, value in cookies.items()])
    
    headers = {{
        'Accept': 'application/json, text/plain, */*',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json',
        'Host': 'dict.gmcc.net:30722',
        'Origin': 'https://dict.gmcc.net:30722',
        'Pragma': 'no-cache',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Cookie': cookie_str
    }}
    return headers

def build_request_data(project_id, login_no):
    """构建请求数据"""
    body = {{}}
    
{body_params_code}
    
    return {{
        "ROOT": {{
            "HEADER": {{
                "OPR_INFO": {{
                    "LOGIN_NO": login_no
                }}
            }},
            "BODY": body
        }}
    }}

def query_data(project_id, cookies, login_no):
    """查询数据"""
    try:
        headers = build_request_headers(cookies)
        data = build_request_data(project_id, login_no)
        
        response = requests.post(API_URL, headers=headers, json=data, timeout=30, verify=False)
        
        if response.status_code == 200:
            result = response.json()
            
            # 检查返回码
            return_code = result.get('ROOT', {{}}).get('BODY', {{}}).get('RETURN_CODE', '')
            if return_code == '0':
                out_data = result.get('ROOT', {{}}).get('BODY', {{}}).get('OUT_DATA', {{}})
                return out_data, result.get('ROOT', {{}}).get('BODY', {{}})
            else:
                error_msg = result.get('ROOT', {{}}).get('BODY', {{}}).get('RETURN_MSG', '未知错误')
                print(f"[警告] 项目 {{project_id}} 查询失败: {{error_msg}}")
                return None, None
        else:
            print(f"[错误] 项目 {{project_id}} HTTP请求失败: {{response.status_code}}")
            return None, None
            
    except Exception as e:
        print(f"[错误] 项目 {{project_id}} 查询异常: {{e}}")
        return None, None

def save_to_database(data_list):
    """保存数据到数据库"""
    if not data_list:
        print("[警告] 没有数据需要保存")
        return
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 清空原有数据
        cursor.execute("DELETE FROM {table_name}")
        print(f"[信息] 已清空原有数据")
        
        # 准备插入SQL
        insert_sql = """
        INSERT INTO {table_name} (
            INPUT_LOGIN_NO, INPUT_PROJECT_ID, RESPONSE_DATA, RESPONSE_COUNT,
            RETURN_CODE, RETURN_MSG, USER_MSG, DETAIL_MSG
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        # 批量插入数据
        for data in data_list:
            response_data = data['response_data']
            system_info = data['system_info']
            
            # 计算返回数据条数
            response_count = 0
            if isinstance(response_data, list):
                response_count = len(response_data)
            elif isinstance(response_data, dict) and response_data:
                response_count = 1
            
            values = (
                data['input_login_no'], 
                data['input_project_id'],
                json.dumps(response_data, ensure_ascii=False),
                response_count,
                system_info.get('RETURN_CODE', '0'),
                system_info.get('RETURN_MSG', 'OK'),
                system_info.get('USER_MSG', ''),
                system_info.get('DETAIL_MSG', '')
            )
            
            cursor.execute(insert_sql, values)
        
        conn.commit()
        print(f"[成功] 成功保存 {{len(data_list)}} 条{interface['chinese_name']}数据")
        
    except Exception as e:
        print(f"[错误] 保存数据失败: {{e}}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def main():
    """主函数"""
    print("=" * 60)
    print("综合查询{interface['chinese_name']}爬虫程序启动")
    print("=" * 60)
    
    # 加载Cookie
    cookies = load_cookies()
    if not cookies:
        print("[错误] Cookie加载失败，请先运行 login2cookie.py 获取Cookie")
        return
    
    # 获取登录用户名
    login_no, _, _ = get_login_credentials()
    
    # 获取项目ID列表
    if {str(interface['needs_project_id']).lower()}:
        project_ids = get_project_ids()
        if not project_ids:
            print("[错误] 未获取到项目ID列表")
            return
    else:
        project_ids = ["SYSTEM"]  # 对于不需要PROJECT_ID的接口
    
    print(f"[信息] 开始处理 {{len(project_ids)}} 个项目")
    
    data_list = []
    success_count = 0
    error_count = 0
    
    for i, project_id in enumerate(project_ids, 1):
        print(f"[进度] {{i}}/{{len(project_ids)}} 处理项目: {{project_id}}")
        
        # 查询数据
        response_data, system_info = query_data(project_id, cookies, login_no)
        
        if response_data is not None:
            data_list.append({{
                'input_login_no': login_no,
                'input_project_id': project_id,
                'response_data': response_data,
                'system_info': system_info
            }})
            success_count += 1
            print(f"  成功获取数据")
        else:
            error_count += 1
        
        # 每处理5个项目休息一下
        if i % 5 == 0:
            time.sleep(1)
    
    print(f"\\n[统计] 成功: {{success_count}}, 失败: {{error_count}}")
    
    # 保存到数据库
    if data_list:
        save_to_database(data_list)
    
    print("=" * 60)
    print("综合查询{interface['chinese_name']}爬虫程序完成")
    print("=" * 60)

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "-all":
        main()
    else:
        print("使用方法: python {file_name} -all")
        print("说明: -all 参数表示轮询所有项目数据同步入库")
'''
    
    return crawler_content

def main():
    """主函数"""
    print("=" * 80)
    print("批量生成所有剩余接口的数据表和爬虫程序")
    print("=" * 80)
    
    # 加载所有接口
    all_interfaces = load_all_interfaces()
    if not all_interfaces:
        return
    
    # 获取已完成的接口
    completed_interfaces = get_completed_interfaces()
    print(f"[信息] 已完成接口: {len(completed_interfaces)} 个")
    
    # 筛选未完成的接口
    remaining_interfaces = []
    for interface in all_interfaces:
        api_name = interface['api_path'].split('/')[-1]
        if api_name not in completed_interfaces:
            remaining_interfaces.append(interface)
    
    print(f"[信息] 待处理接口: {len(remaining_interfaces)} 个")
    
    if not remaining_interfaces:
        print("[完成] 所有接口都已处理完成！")
        return
    
    # 询问用户是否继续
    print(f"\\n将要生成 {len(remaining_interfaces)} 个接口的数据表和爬虫程序")
    print("这可能需要几分钟时间...")
    
    response = input("是否继续？(y/n): ")
    if response.lower() != 'y':
        print("[取消] 用户取消操作")
        return
    
    created_files = []
    
    # 批量生成
    for i, interface in enumerate(remaining_interfaces, 1):
        api_name = interface['api_path'].split('/')[-1]
        table_name = f'dict_zonghe_{api_name}'
        
        print(f"\\n[{i}/{len(remaining_interfaces)}] 正在生成: {interface['chinese_name']}")
        print(f"  接口: {interface['api_path']}")
        
        try:
            # 生成SQL文件
            sql_filename = f"create_{table_name}_table.sql"
            sql_content = generate_sql_table(interface)
            
            with open(sql_filename, 'w', encoding='utf-8') as f:
                f.write(sql_content)
            
            print(f"  ✅ SQL文件: {sql_filename}")
            created_files.append(sql_filename)
            
            # 生成爬虫程序
            py_filename = f"{table_name}.py"
            py_content = generate_crawler_program(interface)
            
            with open(py_filename, 'w', encoding='utf-8') as f:
                f.write(py_content)
            
            print(f"  ✅ 爬虫程序: {py_filename}")
            created_files.append(py_filename)
            
        except Exception as e:
            print(f"  ❌ 生成失败: {e}")
    
    print(f"\\n=" * 80)
    print("批量生成完成")
    print("=" * 80)
    print(f"共生成 {len(created_files)} 个文件")
    
    # 保存生成清单
    with open('generated_files_list.txt', 'w', encoding='utf-8') as f:
        f.write("批量生成的文件清单\\n")
        f.write("=" * 50 + "\\n\\n")
        for file in created_files:
            f.write(f"{file}\\n")
    
    print(f"\\n📄 文件清单已保存到: generated_files_list.txt")
    print(f"\\n💡 下一步操作:")
    print(f"1. 运行: python create_tables.py  # 创建数据表")
    print(f"2. 运行: python project_status_check.py  # 检查状态")
    print(f"3. 运行: python run_all_crawlers.py  # 批量测试")

if __name__ == "__main__":
    main()
