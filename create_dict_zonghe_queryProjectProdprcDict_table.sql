-- 创建综合查询查询项目产品流程字典表
-- 对应接口：saleCenterApp/projectImplement/queryProjectProdprcDict

DROP TABLE IF EXISTS `dict_zonghe_queryProjectProdprcDict`;

CREATE TABLE IF NOT EXISTS `dict_zonghe_queryProjectProdprcDict` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  
  -- 入参字段（来源于源数据表）
  `INPUT_LOGIN_NO` varchar(100) DEFAULT NULL COMMENT '入参-登录用户名',
  `INPUT_PROJECT_ID` varchar(100) DEFAULT NULL COMMENT '入参-项目ID（来源：v_distinct_project_id视图或sign_data_detail表项目编码字段）',
  
  -- 响应数据字段（基于接口返回的JSON结构）
  `DICT_ID` varchar(200) DEFAULT NULL COMMENT 'DICT_ID',
  `DICT_TYPE` varchar(200) DEFAULT NULL COMMENT 'DICT_TYPE',
  `DICT_CODE` varchar(200) DEFAULT NULL COMMENT 'DICT_CODE',
  `DICT_NAME` text DEFAULT NULL COMMENT 'DICT_NAME',
  `DICT_DESC` text DEFAULT NULL COMMENT 'DICT_DESC',
  `PARENT_CODE` varchar(200) DEFAULT NULL COMMENT 'PARENT_CODE',
  `DICT_LEVEL` varchar(200) DEFAULT NULL COMMENT 'DICT_LEVEL',
  `SORT_ORDER` varchar(200) DEFAULT NULL COMMENT 'SORT_ORDER',
  `IS_ACTIVE` varchar(200) DEFAULT NULL COMMENT 'IS_ACTIVE',
  `CREATE_TIME` varchar(50) DEFAULT NULL COMMENT 'CREATE_TIME',
  `FIELD_UPDATE_TIME` varchar(50) DEFAULT NULL COMMENT 'FIELD_UPDATE_TIME',
  `SUBJECT_LABLES` varchar(200) DEFAULT NULL COMMENT 'SUBJECT_LABLES',
  `PROD_TYPE` varchar(200) DEFAULT NULL COMMENT 'PROD_TYPE',
  `PROCESS_TYPE` varchar(200) DEFAULT NULL COMMENT 'PROCESS_TYPE',
  
  -- 系统字段
  `RUN_IP` varchar(50) DEFAULT NULL COMMENT '运行IP',
  `REQUEST_ID` varchar(100) DEFAULT NULL COMMENT '请求ID',
  `RETURN_CODE` varchar(10) DEFAULT NULL COMMENT '返回码',
  `RETURN_MSG` varchar(200) DEFAULT NULL COMMENT '返回消息',
  `USER_MSG` varchar(200) DEFAULT NULL COMMENT '用户消息',
  `DETAIL_MSG` text DEFAULT NULL COMMENT '详细消息',
  
  -- 元数据字段
  `import_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_import_time` (`import_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='综合查询表';
