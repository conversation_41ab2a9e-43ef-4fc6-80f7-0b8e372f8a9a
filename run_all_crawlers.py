#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
运行所有综合查询爬虫程序的主脚本
"""

import os
import sys
import time
import subprocess
from datetime import datetime

# 所有爬虫程序列表
CRAWLER_PROGRAMS = [
    {
        'name': 'dict_zonghe_queryProjectInfo.py',
        'description': '查询项目基本信息',
        'priority': 1,
        'estimated_time': '10分钟'
    },
    {
        'name': 'dict_zonghe_queryProjectDemand.py',
        'description': '查询项目需求信息',
        'priority': 1,
        'estimated_time': '10分钟'
    },
    {
        'name': 'dict_zonghe_qryContractByProject.py',
        'description': '按项目查询合同信息',
        'priority': 1,
        'estimated_time': '10分钟'
    },
    {
        'name': 'dict_zonghe_queryProjectPlanWithImplement.py',
        'description': '查询项目计划及实施情况',
        'priority': 2,
        'estimated_time': '15分钟'
    },
    {
        'name': 'dict_zonghe_queryProjectAmount.py',
        'description': '查询项目金额信息',
        'priority': 2,
        'estimated_time': '15分钟'
    },
    {
        'name': 'dict_zonghe_queryProgram.py',
        'description': '查询项目方案信息',
        'priority': 2,
        'estimated_time': '15分钟'
    },
    {
        'name': 'dict_zonghe_qryIncomeProgressByProject.py',
        'description': '按项目查询收入进度',
        'priority': 2,
        'estimated_time': '15分钟'
    },
    {
        'name': 'dict_zonghe_saleBiddingInfo.py',
        'description': '查询销售投标信息',
        'priority': 3,
        'estimated_time': '20分钟'
    }
]

def check_cookie_validity():
    """检查Cookie是否有效"""
    try:
        # 运行一个简单的测试来检查Cookie
        result = subprocess.run([
            sys.executable, 'test_single_project.py'
        ], capture_output=True, text=True, timeout=60)
        
        if "API测试成功" in result.stdout:
            return True
        else:
            return False
    except Exception as e:
        print(f"[错误] Cookie检查失败: {e}")
        return False

def refresh_cookie():
    """刷新Cookie"""
    print("[信息] 正在刷新Cookie...")
    try:
        result = subprocess.run([
            sys.executable, 'login2cookie.py'
        ], capture_output=True, text=True, timeout=120)
        
        if "登录成功" in result.stdout:
            print("[成功] Cookie刷新成功")
            return True
        else:
            print(f"[错误] Cookie刷新失败: {result.stdout}")
            return False
    except Exception as e:
        print(f"[错误] Cookie刷新异常: {e}")
        return False

def run_crawler(crawler_info):
    """运行单个爬虫程序"""
    program_name = crawler_info['name']
    description = crawler_info['description']
    
    print(f"\n{'='*60}")
    print(f"开始运行: {description}")
    print(f"程序: {program_name}")
    print(f"预计时间: {crawler_info['estimated_time']}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        # 检查程序文件是否存在
        if not os.path.exists(program_name):
            print(f"[错误] 程序文件不存在: {program_name}")
            return False
        
        # 运行爬虫程序
        result = subprocess.run([
            sys.executable, program_name, '-all'
        ], capture_output=True, text=True, timeout=1800)  # 30分钟超时
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n[完成] {description}")
        print(f"[耗时] {duration:.1f}秒")
        
        # 检查执行结果
        if result.returncode == 0:
            if "成功保存" in result.stdout:
                print(f"[成功] 数据同步完成")
                return True
            else:
                print(f"[警告] 程序执行完成但可能没有数据保存")
                print(f"[输出] {result.stdout[-500:]}")  # 显示最后500字符
                return True
        else:
            print(f"[错误] 程序执行失败，返回码: {result.returncode}")
            print(f"[错误输出] {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"[错误] 程序执行超时: {program_name}")
        return False
    except Exception as e:
        print(f"[错误] 程序执行异常: {e}")
        return False

def main():
    """主函数"""
    print("=" * 80)
    print("综合查询爬虫程序批量执行器")
    print("=" * 80)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"计划执行 {len(CRAWLER_PROGRAMS)} 个爬虫程序")
    
    # 检查Cookie有效性
    print("\n[检查] 验证Cookie有效性...")
    if not check_cookie_validity():
        print("[警告] Cookie可能已过期，尝试刷新...")
        if not refresh_cookie():
            print("[错误] Cookie刷新失败，请手动运行 login2cookie.py")
            return
        
        # 再次检查
        if not check_cookie_validity():
            print("[错误] Cookie仍然无效，请检查登录信息")
            return
    
    print("[成功] Cookie验证通过")
    
    # 按优先级排序
    sorted_crawlers = sorted(CRAWLER_PROGRAMS, key=lambda x: x['priority'])
    
    success_count = 0
    failed_count = 0
    
    for i, crawler_info in enumerate(sorted_crawlers, 1):
        print(f"\n[进度] {i}/{len(sorted_crawlers)}")
        
        # 运行爬虫
        if run_crawler(crawler_info):
            success_count += 1
        else:
            failed_count += 1
            
            # 如果是高优先级程序失败，询问是否继续
            if crawler_info['priority'] == 1:
                response = input(f"\n[询问] 高优先级程序失败，是否继续执行其他程序？(y/n): ")
                if response.lower() != 'y':
                    print("[中止] 用户选择停止执行")
                    break
        
        # 程序间休息
        if i < len(sorted_crawlers):
            print(f"\n[休息] 等待5秒后执行下一个程序...")
            time.sleep(5)
    
    # 执行总结
    print("\n" + "=" * 80)
    print("批量执行完成")
    print("=" * 80)
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"成功: {success_count} 个程序")
    print(f"失败: {failed_count} 个程序")
    print(f"总计: {len(sorted_crawlers)} 个程序")
    
    if failed_count > 0:
        print(f"\n[建议] 有 {failed_count} 个程序执行失败，建议检查日志并手动重新运行")

if __name__ == "__main__":
    main()
