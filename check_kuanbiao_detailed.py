#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细检查kuanbiao表的字段结构和数据
"""

import pymysql

DB_CONFIG = {
    'host': '*************',
    'port': 12136,
    'user': 'root',
    'password': '5eb9a11916e3a66d',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

try:
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    print('=== 重新检查kuanbiao表的完整字段结构 ===')
    cursor.execute('SHOW COLUMNS FROM t_kuanbiao')
    columns = cursor.fetchall()
    
    target_fields = [
        '签约上报日期', '项目编码', '项目名称', '合同含税金额（万元）', 
        '收入侧客户', '收入侧合同编码', '项目建设内容及方案简介（CT）', 
        '项目建设内容及方案简介（IT）', '一级场景', '二级场景', 
        '后向合同签约时间', '成本侧供应商'
    ]
    
    print('\n=== 目标字段检查结果 ===')
    column_names = [col[0] for col in columns]
    
    for field in target_fields:
        if field in column_names:
            print(f'✓ {field} - 存在')
        else:
            # 查找相似字段
            similar_fields = []
            for col in column_names:
                if '场景' in field and '场景' in col:
                    similar_fields.append(col)
                elif field.replace('（', '').replace('）', '') in col:
                    similar_fields.append(col)
            
            if similar_fields:
                print(f'? {field} - 可能的相似字段: {similar_fields}')
            else:
                print(f'✗ {field} - 不存在')
    
    print('\n=== 查找包含场景的所有字段 ===')
    scene_fields = []
    for col in columns:
        field_name = col[0]
        if '场景' in field_name:
            scene_fields.append(field_name)
            print(f'找到场景字段: {field_name} ({col[1]})')
    
    print('\n=== 检查成本侧供应商字段的实际数据 ===')
    cursor.execute('SELECT COUNT(*) FROM t_kuanbiao')
    total_count = cursor.fetchone()[0]
    print(f'总记录数: {total_count}')
    
    cursor.execute('SELECT COUNT(*) FROM t_kuanbiao WHERE 成本侧供应商 IS NOT NULL AND 成本侧供应商 != ""')
    supplier_count = cursor.fetchone()[0]
    print(f'有成本侧供应商的记录数: {supplier_count} ({supplier_count/total_count*100:.1f}%)')
    
    if supplier_count > 0:
        print('\n=== 成本侧供应商数据样例 ===')
        cursor.execute('SELECT 项目编码, 收入侧客户, 成本侧供应商, 所属行业 FROM t_kuanbiao WHERE 成本侧供应商 IS NOT NULL AND 成本侧供应商 != "" LIMIT 10')
        results = cursor.fetchall()
        for row in results:
            print(f'项目: {row[0]}, 客户: {row[1]}, 供应商: {row[2]}, 行业: {row[3]}')
    
    # 检查成本侧供应商字段的类型和长度
    print('\n=== 成本侧供应商字段详细信息 ===')
    for col in columns:
        if '成本侧供应商' in col[0]:
            print(f'字段名: {col[0]}')
            print(f'数据类型: {col[1]}')
            print(f'是否允许NULL: {col[2]}')
            print(f'键类型: {col[3]}')
            print(f'默认值: {col[4]}')
            print(f'额外信息: {col[5]}')
    
    # 检查项目建设内容字段
    print('\n=== 项目建设内容字段数据统计 ===')
    cursor.execute('SELECT COUNT(*) FROM t_kuanbiao WHERE `项目建设内容及方案简介（CT）` IS NOT NULL AND `项目建设内容及方案简介（CT）` != ""')
    ct_count = cursor.fetchone()[0]
    print(f'有CT内容的记录数: {ct_count} ({ct_count/total_count*100:.1f}%)')
    
    cursor.execute('SELECT COUNT(*) FROM t_kuanbiao WHERE `项目建设内容及方案简介（IT）` IS NOT NULL AND `项目建设内容及方案简介（IT）` != ""')
    it_count = cursor.fetchone()[0]
    print(f'有IT内容的记录数: {it_count} ({it_count/total_count*100:.1f}%)')
    
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f'错误: {e}')
    import traceback
    traceback.print_exc()
