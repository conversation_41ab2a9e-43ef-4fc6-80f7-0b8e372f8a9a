#!/usr/bin/env python
# -*- coding: utf-8 -*-

#  1、下载邮件的拍照数据
#  2、拍照数据入库
#  3、拍照数据去重
#  3、同步下载bc公网的集团基础表
#  4、同步下载bc公网的商机数据表
#  5、形成视图v_br_jianzhe视图
#  6、更新bc公网的商机数据表（更新鉴真状态与鉴真时间  ）
#


import os
import sys
import time
import subprocess
from datetime import datetime

def run_script(script_name, description=None):
    """运行指定的Python脚本并等待其完成"""
    if description:
        print(f"\n{'='*50}")
        print(f"🚀 {description}")
        print(f"{'='*50}")

    print(f"[信息] 正在运行 {script_name}...")

    try:
        # 使用subprocess运行脚本，指定encoding为utf-8
        result = subprocess.run([sys.executable, script_name],
                               capture_output=True,
                               text=True,
                               encoding='utf-8',  # 明确指定使用UTF-8编码
                               check=True)

        # 打印脚本的输出
        if result.stdout:
            print(result.stdout)

        if result.returncode == 0:
            print(f"[成功] {script_name} 运行完成")
            return True
        else:
            print(f"[错误] {script_name} 运行失败，返回码: {result.returncode}")
            if result.stderr:
                print(f"错误信息: {result.stderr}")
            return False

    except subprocess.CalledProcessError as e:
        print(f"[错误] 运行 {script_name} 时出错: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        return False
    except Exception as e:
        print(f"[错误] 运行 {script_name} 时发生异常: {e}")
        return False

def main():
    """主函数，按顺序执行BC相关脚本"""
    start_time = time.time()
    print(f"📊 开始执行BC数据采集与导入流程 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 步骤1: 运行BC_email_downloader.py，下载邮件附件
    if not run_script("BC_email_downloader.py", "步骤1: 下载邮件附件"):
        print("[错误] 下载邮件附件失败，流程终止")
        return

    # 步骤2: 运行BC_data_importer.py，导入数据
    if not run_script("BC_data_importer.py", "步骤2: 导入市场份额数据"):
        print("[错误] 导入市场份额数据失败，流程终止")
        return

    # 步骤3: 运行客户经理日产能数据入库.py，导入客户经理数据
    if not run_script("客户经理日产能数据入库.py", "步骤3: 导入客户经理日产能数据"):
        print("[错误] 导入客户经理日产能数据失败，流程终止")
        return

    # 步骤4: 运行BC去重remove_market_share_duplicates.py，清理重复数据
    if not run_script("BC去重remove_market_share_duplicates.py", "步骤4: 清理重复数据"):
        print("[错误] 清理重复数据失败，流程终止")
        return

    # 计算总耗时
    end_time = time.time()
    duration = end_time - start_time
    minutes, seconds = divmod(duration, 60)

    print(f"\n{'='*50}")
    print(f"✅ BC数据处理流程执行完成！")
    print(f"总耗时: {int(minutes)}分{int(seconds)}秒")
    print(f"{'='*50}")

if __name__ == "__main__":
    main()