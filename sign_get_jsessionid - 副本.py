import requests
import os
from datetime import datetime

def load_cookies(filename="cookies.txt"):
    """从cookies.txt文件中加载cookie"""
    cookies = {}
    try:
        if os.path.exists(filename):
            with open(filename, "r") as f:
                for line in f:
                    if "=" in line:
                        name, value = line.strip().split("=", 1)
                        cookies[name] = value
            print(f"[信息] 已从{filename}加载Cookie")
            return cookies
        else:
            print(f"[警告] Cookie文件{filename}不存在，请先运行getcookie.py获取Cookie")
            exit(1)
    except Exception as e:
        print(f"[错误] 加载Cookie失败: {e}")
        exit(1)

def save_cookies(cookies, filename="cookies.txt"):
    """保存cookies到文件"""
    try:
        with open(filename, "w") as f:
            for name, value in cookies.items():
                f.write(f"{name}={value}\n")
        print(f"[信息] Cookie已更新并保存到 {filename}")
    except Exception as e:
        print(f"[错误] 保存Cookie失败: {e}")
        exit(1)

def get_jsessionid():
    """访问SSO接口获取新的JSESSIONID"""
    # 加载现有cookie
    cookies_dict = load_cookies()
    cookie_str = "; ".join([f"{name}={value}" for name, value in cookies_dict.items()])
    
    url = "http://dict.gmcc.net:30722/analysisService/gdyddict/yyfxSSOyyfxSSO"

    # params = {
    #     'cityId': "1000008498",
    #     'limitSec': "300",
    #     'modId': "2400015082",
    #     'oaAcct': "zhengdewen",
    #     'postId': "10061760",
    #     'postOrgId': "1000010331",
    #     'sysId': "gdydDict",
    #     'sysUserCode': "zhengdewen",
    #     'timestamp': "20250506011809",       #datetime.now().strftime("%Y%m%d%H%M%S"),
    #     "sign": "9e9d63d5334cdbe166206c45d06d8146"
    # }
    # 使用当前时间戳生成timestamp参数
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")

    # 注意：这里使用了固定的sign值，实际应用中可能需要动态生成
    params = {
        'cityId': "1000008498",
        'limitSec': "300",
        'modId': "2400015082",
        'oaAcct': "zhengdewen",
        'postId': "10061760",
        'postOrgId': "1000010331",
        'sysId': "gdydDict",
        'sysUserCode': "zhengdewen",
        'timestamp': "20250507141702",
        # 'timestamp': timestamp,
        # 实际应用中应该根据其他参数动态生成sign值
        'sign': "9def36c1c3c53e06d927e43f8f81400e"
    }
    
    headers = {
        'Host': "dict.gmcc.net:30722",
        'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        'Accept': "application/json, text/plain, */*",
        'Accept-Encoding': "gzip, deflate",
        'Pragma': "no-cache",
        'Cache-Control': "no-cache",
        'Referer': "http://dict.gmcc.net:30722/dictWeb/iframeTemplate/iframeAnalysis?index=1&title=%E7%AD%BE%E7%BA%A6%E9%A1%B9%E7%9B%AE%E6%98%8E%E7%BB%86",
        'Accept-Language': "zh-CN,zh;q=0.9",
        'Cookie': cookie_str
    }
    
    try:
        print("[信息] 正在请求SSO接口获取新的JSESSIONID...")
        session = requests.Session()
        response = session.get(url, params=params, headers=headers)
        response.raise_for_status()
        
        # 打印响应头中的Set-Cookie值
        if 'Set-Cookie' in response.headers:
            print(f"[信息] 响应头中的Set-Cookie: {response.headers['Set-Cookie']}")
        else:
            print("[信息] 响应头中没有Set-Cookie字段")
        
        # 获取响应中的cookies
        new_cookies = session.cookies.get_dict()
        print(f"[信息] 获取到新的cookies: {new_cookies}")
        
        # 如果没有获取到新的cookie，可能是请求参数有问题
        if not new_cookies:
            print("[警告] 未获取到新的cookie，可能是请求参数过期")
            print(f"[调试] 响应状态码: {response.status_code}")
            print(f"[调试] 响应内容: {response.text[:200]}...")
            # 尝试继续使用旧的cookie
            print("[信息] 将继续使用现有Cookie")
            return cookies_dict
        
        # 更新cookies字典，只添加新的cookie，不覆盖现有的
        for name, value in new_cookies.items():
            if name not in cookies_dict:  # 只在cookie不存在时添加
                cookies_dict[name] = value
                print(f"[信息] 添加新cookie: {name}={value}")
            else:
                print(f"[信息] 更新cookie: {name}")
                cookies_dict[name] = value  # 或者您可以选择不更新
        
        # 保存更新后的cookies
        save_cookies(cookies_dict)
        
        print("[成功] JSESSIONID已更新并添加到现有Cookie")
        return cookies_dict
    except requests.exceptions.RequestException as e:
        print(f"[错误] 网络请求失败: {e}")
        if 'response' in locals():
            print(f"[调试] 响应状态码: {response.status_code}")
            print(f"[调试] 响应头: {response.headers}")
            print(f"[调试] 响应内容: {response.text[:200]}...")  # 只打印前200个字符
        # 尝试继续使用旧的cookie
        print("[信息] 将继续使用现有Cookie")
        return cookies_dict
    except Exception as e:
        print(f"[错误] 获取JSESSIONID失败: {e}")
        if 'response' in locals():
            print(f"[调试] 响应状态码: {response.status_code}")
            print(f"[调试] 响应头: {response.headers}")
            print(f"[调试] 响应内容: {response.text[:200]}...")  # 只打印前200个字符
        # 尝试继续使用旧的cookie
        print("[信息] 将继续使用现有Cookie")
        return cookies_dict

if __name__ == "__main__":
    print("🚀 开始更新JSESSIONID...")
    try:
        get_jsessionid()
        print("✅ JSESSIONID更新完成")
    except Exception as e:
        print(f"[错误] 更新JSESSIONID过程中发生未处理的异常: {e}")
        exit(1)