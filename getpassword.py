import base64
import urllib.parse
from Crypto.Cipher import DES, AES
from Crypto.Util.Padding import pad
import hashlib
from typing import Optional, Callable, Dict, Union


class PasswordEncryptor:
    """
    完整的密码加密工具，支持多种加密方式和预处理函数
    可根据实际前端加密方式灵活配置
    """

    def __init__(self, default_key: str = "gdydDict@sitech"):
        """
        初始化加密器

        参数:
            default_key: 默认加密密钥
        """
        self.default_key = default_key

    def encrypt_password(self, password: str, key: Optional[str] = None, url_encode: bool = False) -> str:
        """
        使用DES加密密码并返回Base64编码的结果 (兼容旧版API)
        
        参数:
            password: 明文密码
            key: 加密密钥，默认使用实例的default_key
            url_encode: 是否进行URL编码
            
        返回:
            加密后的密码
        """
        return self.standard_des_encrypt(password, key, url_encode)

    def standard_des_encrypt(self,
                             password: str,
                             key: Optional[str] = None,
                             url_encode: bool = False) -> str:
        """
        标准DES-ECB加密实现，与CryptoJS库兼容

        参数:
            password: 待加密的密码
            key: 加密密钥，默认使用实例的default_key
            url_encode: 是否对结果进行URL编码

        返回:
            加密后的字符串
        """
        if key is None:
            key = self.default_key

        # 处理密钥 - DES需要8字节密钥
        key_bytes = key.encode('utf-8')
        if len(key_bytes) > 8:
            key_bytes = key_bytes[:8]
        elif len(key_bytes) < 8:
            key_bytes = key_bytes.ljust(8, b'\0')

        # 处理明文
        password_bytes = password.encode('utf-8')

        # 创建DES加密器，使用ECB模式
        cipher = DES.new(key_bytes, DES.MODE_ECB)

        # 使用PKCS7填充并加密
        padded_data = pad(password_bytes, DES.block_size)
        encrypted = cipher.encrypt(padded_data)

        # 转换为Base64编码
        b64_str = base64.b64encode(encrypted).decode('utf-8')

        # 根据需要进行URL编码
        if url_encode:
            return urllib.parse.quote(b64_str)
        else:
            return b64_str

    def des_with_preprocessor(self,
                              password: str,
                              preprocessor: Callable[[str], str],
                              key: Optional[str] = None,
                              url_encode: bool = False) -> str:
        """
        使用预处理函数处理密码，然后DES加密

        参数:
            password: 待加密的密码
            preprocessor: 预处理函数，接收密码字符串，返回处理后的字符串
            key: 加密密钥，默认使用实例的default_key
            url_encode: 是否URL编码结果

        返回:
            加密后的字符串
        """
        processed_password = preprocessor(password)
        return self.standard_des_encrypt(processed_password, key, url_encode)

    def triple_des_encrypt(self,
                           password: str,
                           key: Optional[str] = None,
                           url_encode: bool = False) -> str:
        """
        3DES加密实现

        参数:
            password: 待加密的密码
            key: 加密密钥，默认使用实例的default_key
            url_encode: 是否URL编码结果

        返回:
            加密后的字符串
        """
        if key is None:
            key = self.default_key

        from Crypto.Cipher import DES3

        # 生成24字节密钥
        key_bytes = key.encode('utf-8')
        hash_obj = hashlib.md5(key_bytes)
        key_md5 = hash_obj.digest()
        key_3des = key_md5 + key_md5[:8]  # 拼接到24字节

        # 加密
        cipher = DES3.new(key_3des, DES3.MODE_ECB)
        padded_data = pad(password.encode('utf-8'), DES3.block_size)
        encrypted = cipher.encrypt(padded_data)

        # 转换为Base64编码
        b64_str = base64.b64encode(encrypted).decode('utf-8')

        # 根据需要进行URL编码
        if url_encode:
            return urllib.parse.quote(b64_str)
        else:
            return b64_str

    def multi_encrypt(self,
                      password: str,
                      algorithms: list,
                      key: Optional[str] = None,
                      url_encode: bool = False) -> str:
        """
        多重加密 - 使用多个算法依次加密

        参数:
            password: 待加密的密码
            algorithms: 加密算法列表，包含"des"、"3des"、"aes"等
            key: 加密密钥，默认使用实例的default_key
            url_encode: 是否URL编码最终结果

        返回:
            加密后的字符串
        """
        if key is None:
            key = self.default_key

        result = password
        for algo in algorithms:
            if algo.lower() == "des":
                result = self.standard_des_encrypt(result, key)
            elif algo.lower() in ["3des", "triple_des"]:
                result = self.triple_des_encrypt(result, key)
            elif algo.lower() == "aes":
                # AES加密实现
                key_bytes = key.encode('utf-8')
                if len(key_bytes) > 16:
                    key_bytes = key_bytes[:16]
                else:
                    key_bytes = key_bytes.ljust(16, b'\0')

                cipher = AES.new(key_bytes, AES.MODE_ECB)
                padded_data = pad(result.encode('utf-8'), AES.block_size)
                encrypted = cipher.encrypt(padded_data)
                result = base64.b64encode(encrypted).decode('utf-8')
            elif algo.lower() == "md5":
                result = hashlib.md5(result.encode()).hexdigest()
            elif algo.lower() == "base64":
                result = base64.b64encode(result.encode()).decode()

        # 根据需要进行URL编码
        if url_encode:
            return urllib.parse.quote(result)
        else:
            return result

    def encrypt(self,
                password: str,
                method: str = "standard_des",
                options: Optional[Dict] = None) -> str:
        """
        主加密方法 - 根据指定方法加密密码

        参数:
            password: 待加密的密码
            method: 加密方法名称，如"standard_des"、"triple_des"、"with_md5"等
            options: 加密选项，可包含key、url_encode、preprocessor等

        返回:
            加密后的字符串
        """
        if options is None:
            options = {}

        key = options.get("key", self.default_key)
        url_encode = options.get("url_encode", False)

        # 根据方法选择加密实现
        if method == "standard_des":
            return self.standard_des_encrypt(password, key, url_encode)

        elif method == "triple_des":
            return self.triple_des_encrypt(password, key, url_encode)

        elif method == "with_md5":
            # 先MD5后DES
            def md5_hash(p):
                return hashlib.md5(p.encode()).hexdigest()

            return self.des_with_preprocessor(password, md5_hash, key, url_encode)

        elif method == "with_base64":
            # 先Base64后DES
            def base64_encode(p):
                return base64.b64encode(p.encode()).decode()

            return self.des_with_preprocessor(password, base64_encode, key, url_encode)

        elif method == "double_des":
            # 双重DES
            return self.multi_encrypt(password, ["des", "des"], key, url_encode)

        elif method == "des_aes":
            # DES后AES
            return self.multi_encrypt(password, ["des", "aes"], key, url_encode)

        elif method == "custom":
            # 自定义处理流程
            preprocessor = options.get("preprocessor")
            if preprocessor:
                return self.des_with_preprocessor(password, preprocessor, key, url_encode)

            algorithms = options.get("algorithms")
            if algorithms:
                return self.multi_encrypt(password, algorithms, key, url_encode)

            # 如果没有特定选项，回退到标准DES
            return self.standard_des_encrypt(password, key, url_encode)

        else:
            # 默认使用标准DES
            return self.standard_des_encrypt(password, key, url_encode)


# 便捷函数 - 直接使用
def encrypt_password(password: str,
                     key: str = "gdydDict@sitech",
                     method: str = "standard_des",
                     url_encode: bool = False) -> str:
    """
    便捷加密函数 - 直接使用默认设置

    参数:
        password: 待加密的密码
        key: 加密密钥，默认为"gdydDict@sitech"
        method: 加密方法，默认为"standard_des"
        url_encode: 是否URL编码结果

    返回:
        加密后的字符串
    """
    encryptor = PasswordEncryptor(key)
    options = {"url_encode": url_encode}
    return encryptor.encrypt(password, method, options)


def get_encrypted_password(username, password, method="with_md5", url_encode=True):
    """
    加密指定的密码
    :param username: 用户名（用于日志记录）
    :param password: 明文密码
    :param method: 加密方法，默认为"standard_des"
    :param url_encode: 是否URL编码结果
    :return: 加密后的密码
    """
    try:
        encrypted_password = encrypt_password(password, method=method)
        print(f"[信息] 用户 {username} 的密码加密成功")
        return encrypted_password
    except Exception as e:
        print(f"[错误] 用户 {username} 的密码加密失败: {e}")
        exit(1)

#
# # 测试功能
# def test_encryption():
#     """测试各种加密方法"""
#     print("=== 测试各种加密方法 99999999===")
#     password = "888888"
#     target_result = "ba144J01ZPASe/mXcHUFswl4bRh8nmIPm35FkWi0EHwGf4yYWLScRA=="
#
#     encryptor = PasswordEncryptor()
#
#     # 测试不同方法
#     methods = {
#         "标准DES": "standard_des",
#         "3DES": "triple_des",
#         "MD5+DES": "with_md5",
#         "Base64+DES": "with_base64",
#         "双重DES": "double_des",
#         "DES+AES": "des_aes"
#     }
#
#     print(f"密码: {password}")
#     print(f"目标结果: {target_result}")
#     print(f"目标长度: {len(target_result)}")
#     print('-' * 50)
#
#     for name, method in methods.items():
#         result = encryptor.encrypt(password, method)
#         print(f"\n{name}:")
#         print(f"结果: {result}")
#         print(f"长度: {len(result)}")
#
#         if result == target_result:
#             print("*** 匹配成功! ***")
#
#     # # 尝试自定义方法
#     # print("\n自定义加密:")
#     # custom_result = encryptor.encrypt(password, "custom", {
#     #     "algorithms": ["des", "base64", "des"]
#     # })
#     # print(f"结果: {custom_result}")
#     # print(f"长度: {len(custom_result)}")
#     #
#     # if custom_result == target_result:
#     #     print("*** 匹配成功! ***")


# 运行测试
if __name__ == "__main__":
    # test000_encryption()  # 确保这行已正确缩进并注释

    # 简单使用示例
    print("\n=== 简单使用示例 ===")
    password = "Dewen@428"
    #  G5bycftH60tEJAMvIZ3awFqJt9Gzk1qlMu9X5T0MtOgGf4yYWLScRA==
    encrypted = encrypt_password(password,method="with_md5")
    print(f"标准DES加密: {encrypted}")

    # URL编码版本
    # encrypted_url = encrypt_password(password, url_encode=True)
    # print(f"带URL编码: {encrypted_url}")
    #
    # print("\n此模块提供灵活的密码加密功能，支持多种加密方式和组合。")
    # print("若标准实现与前端不匹配，可尝试其他方法或自定义流程。")