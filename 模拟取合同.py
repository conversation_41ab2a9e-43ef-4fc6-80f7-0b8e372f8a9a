import requests
import json

url = "http://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/common/excelController/export"

payload = {
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "zhen<PERSON><PERSON><PERSON>"
      }
    },
    "BODY": {
      "IS_EXPORT": "20",
      "CONFIG_CODE": "queryContractListExport",
      "MORE_PARAM": "{\"CONTRACT_SERIAL_NO\":\"\",\"PROJECT_NO\":\"\",\"CONTRACT_NAME\":\"\",\"INC_EXP_TYPE\":\"\",\"CONTRACT_STATUS\":\"\",\"START_DATE\":\"2025-05-01 00:00:00\",\"END_DATE\":\"2025-05-06 23:59:59\",\"REGION_CODE\":\"\",\"POST_CODE\":\"10034,10072,10081,20001,10001,10061,10000,10270,10150,10417,10464,10455,10275,10149\",\"CONTRACT_NO\":\"\"}"
    }
  }
}

headers = {
  'Host': "dict.gmcc.net:30722",
  'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  'Accept': "application/json, text/plain, */*",
  'Accept-Encoding': "gzip, deflate",
  'Content-Type': "application/json",
  'Pragma': "no-cache",
  'Cache-Control': "no-cache",
  'x-session-staffname': "dengyong",
  'x-session-regionid': "200",
  'x-session-sysusercode': "dengyong",
  'x-session-staffid': "10000",
  'Origin': "http://dict.gmcc.net:30722",
  'Referer': "http://dict.gmcc.net:30722/dictWeb/gdydFlowPath/contractManage",
  'Accept-Language': "zh-CN,zh;q=0.9",
  'Cookie': "BSS-SESSION=NGE2NDBmZWMtM2FmZS00YTQ5LThmOWEtZDY5MGViNmQ2NGYz; isLogin=ImlzTG9naW4i; requestId=4e86f220-29ed-11f0-854e-a7722f3aadfe; systemUserCode=InpoZW5nZGV3ZW4i; jsession_id_4_boss=nF4FACEFE75B5ED3D50D25C5B16AE90D8-1"
}

response = requests.post(url, data=json.dumps(payload), headers=headers)

print(response.text)