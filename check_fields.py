#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查kuanbiao表中的字段
"""

import pymysql

DB_CONFIG = {
    'host': '*************',
    'port': 12136,
    'user': 'root',
    'password': '5eb9a11916e3a66d',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

try:
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    print('=== 检查kuanbiao表中的字段 ===')
    cursor.execute('SHOW COLUMNS FROM t_kuanbiao')
    columns = cursor.fetchall()
    
    target_fields = [
        '签约上报日期', '项目编码', '项目名称', '合同含税金额（万元）', 
        '收入侧客户', '收入侧合同编码', '项目建设内容及方案简介（CT）', 
        '项目建设内容及方案简介（IT）', '一级场景', '二级场景', 
        '后向合同签约时间', '成本侧供应商'
    ]
    
    existing_fields = []
    missing_fields = []
    
    column_names = [col[0] for col in columns]
    
    for field in target_fields:
        if field in column_names:
            existing_fields.append(field)
        else:
            missing_fields.append(field)
    
    print('\n=== 存在的字段 ===')
    for field in existing_fields:
        print(f'✓ {field}')
    
    print('\n=== 缺失的字段 ===')
    for field in missing_fields:
        print(f'✗ {field}')
    
    # 查看一些样本数据
    if existing_fields:
        print('\n=== 样本数据 ===')
        sample_fields = existing_fields[:5]  # 只看前5个字段
        field_list = ', '.join([f'`{field}`' for field in sample_fields])
        query = f'SELECT {field_list} FROM t_kuanbiao LIMIT 3'
        cursor.execute(query)
        results = cursor.fetchall()
        
        for i, row in enumerate(results):
            print(f'记录 {i+1}:')
            for j, field in enumerate(sample_fields):
                value = row[j] if row[j] is not None else 'NULL'
                print(f'  {field}: {value}')
            print()
    
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f'错误: {e}')
