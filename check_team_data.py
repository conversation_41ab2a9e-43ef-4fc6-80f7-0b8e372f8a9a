#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查团队信息数据
"""

import pymysql

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'cmcc12345',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

def check_data():
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 检查总记录数
        cursor.execute('SELECT COUNT(*) FROM dict_zonghe_queryTeamMember')
        count = cursor.fetchone()[0]
        print(f'当前数据库中已有 {count} 条团队成员记录')
        
        # 检查项目数量
        cursor.execute('SELECT COUNT(DISTINCT PROJECT_ID) FROM dict_zonghe_queryTeamMember')
        project_count = cursor.fetchone()[0]
        print(f'涉及 {project_count} 个项目')
        
        # 显示前5条记录
        cursor.execute('SELECT PROJECT_ID, STAFF_NAME, POST_NAME, DEPT_NAME FROM dict_zonghe_queryTeamMember LIMIT 5')
        rows = cursor.fetchall()
        print('\n前5条记录示例:')
        for row in rows:
            print(f'项目ID: {row[0]}, 姓名: {row[1]}, 岗位: {row[2]}, 部门: {row[3]}')
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f'检查数据失败: {e}')

if __name__ == "__main__":
    check_data()
