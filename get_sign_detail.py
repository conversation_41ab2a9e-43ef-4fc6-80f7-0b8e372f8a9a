import requests
import json
import pandas as pd
from io import BytesIO
from datetime import datetime, timedelta
import os

def load_cookies(filename="cookies.txt"):
    """从cookies.txt文件中加载cookie（支持JSON格式）"""
    cookies = {}
    try:
        if os.path.exists(filename):
            with open(filename, "r", encoding="utf-8") as f:
                content = f.read().strip()

                # 检查是否为JSON格式
                if content.startswith('{'):
                    # JSON格式处理
                    cookie_data = json.loads(content)
                    if 'cookies' in cookie_data:
                        for cookie in cookie_data['cookies']:
                            cookies[cookie['name']] = cookie['value']
                        print(f"[信息] 已从{filename}加载Cookie（JSON格式，共{len(cookies)}个cookie）")
                    else:
                        print(f"[错误] JSON格式的Cookie文件缺少'cookies'字段")
                        exit(1)
                else:
                    # 兼容旧的键值对格式
                    f.seek(0)  # 重置文件指针
                    for line in f:
                        if "=" in line:
                            name, value = line.strip().split("=", 1)
                            cookies[name] = value
                    print(f"[信息] 已从{filename}加载Cookie（键值对格式，共{len(cookies)}个cookie）")

            return cookies
        else:
            print(f"[警告] Cookie文件{filename}不存在，请先运行getcookie.py获取Cookie")
            exit(1)
    except json.JSONDecodeError as e:
        print(f"[错误] Cookie文件JSON格式解析失败: {e}")
        exit(1)
    except Exception as e:
        print(f"[错误] 加载Cookie失败: {e}")
        exit(1)

# 加载cookie
cookies_dict = load_cookies()
cookie_str = "; ".join([f"{name}={value}" for name, value in cookies_dict.items()])
print(cookie_str)

url = "http://dict.gmcc.net:30722/gdyddict/selfReport/exportTableBeta"

# 计算日期
today = datetime.now()
seven_days_ago = today - timedelta(days=8000)

# 格式化日期为yyyymmdd
today_str = today.strftime("%Y%m%d")
seven_days_ago_str = seven_days_ago.strftime("%Y%m%d")

# ##  以下为签约明细的入参   # 取数路径：运营分析--统计报表--业财类报表--签约项目明细
# payload = {
#   "pageNumber": 1,
#   "pageSize": 10,
#   "repCode": "R120221120001",
#   "ids": "v47:v47:v49:v50:v53:v1:v3:v13:v14:v14:v16:v16:v11:v11:",
#   "coltypesize": "151:152:2:8:8:3:3:3:151:152:151:152:151:152:",
#   "os": "Win32",
#   "bv": "5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
#   "v471": seven_days_ago_str,  # 七天前的日期，格式为yyyymmdd
#   "v472": today_str,  # 今天的日期，格式为yyyymmdd
#   "v49": "760",
#   "v50": "",
#   "v53": "",
#   "v1": "",
#   "v3": "",
#   "v13": "",
#   "v141": "",
#   "v142": "",
#   "v161": "",
#   "v162": "",
#   "v111": "",
#   "v112": ""
# }
#
# headers = {
#   'Host': "dict.gmcc.net:30722",
#   'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
#   'Accept': "application/json, text/plain, */*",
#   'Accept-Encoding': "gzip, deflate",
#   'Content-Type': "application/json",
#   'Origin': "http://dict.gmcc.net:30722",
#   'Referer': "http://dict.gmcc.net:30722/analysis/selfReport?repcode=R120221120001",
#   'Accept-Language': "zh-CN,zh;q=0.9",
#   'Cookie': cookie_str
# }
# ##  以上为签约明细的入参



# ###以下为周报的入参，# 取数路径：运营分析--统计报表--业财类报表--签约业务周报

payload = {
  "pageNumber": 1,
  "pageSize": 10,
  "repCode": "R1202305251232",
  "ids": "v47:v47:v49:v50:v53:v1:v3:v13:v14:v14:v16:v16:v11:v11:",
  "coltypesize": "151:152:2:8:8:3:3:3:151:152:151:152:151:152:",
  "os": "Win32",
  "bv": "5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "v471": seven_days_ago_str,  # 七天前的日期，格式为yyyymmdd
  "v472": today_str,  # 今天的日期，格式为yyyymmdd
  "v49": "760",
  "v50": "",
  "v53": "",
  "v1": "",
  "v3": "",
  "v13": "",
  "v141": "",
  "v142": "",
  "v161": "",
  "v162": "",
  "v111": "",
  "v112": ""
}

headers = {
  'Host': "dict.gmcc.net:30722",
  'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  'Accept': "application/json, text/plain, */*",
  'Accept-Encoding': "gzip, deflate",
  'Content-Type': "application/json",
  'Pragma': "no-cache",
  'Cache-Control': "no-cache",
  'Origin': "http://dict.gmcc.net:30722",
  'Referer': "http://dict.gmcc.net:30722/analysis/selfReport?repcode=R1202305251232",
  'Accept-Language': "zh-CN,zh;q=0.9",
  'Cookie': cookie_str
}
# ###以上为周报的入参


try:
    print("[信息] 正在发送请求获取签约项目明细...")
    response = requests.post(url, data=json.dumps(payload), headers=headers)
    response.raise_for_status()  # 检查响应状态

    # 生成带时间戳的文件名
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    filename = f"temp_签约项目明细_{timestamp}.xlsx"

    # 直接将响应内容以二进制方式写入到带时间戳的xlsx文件中
    with open(filename, "wb") as f:
        f.write(response.content)
    print(f"[成功] 响应内容已保存为 {filename}")
    
except Exception as e:
    print(f"[错误] 获取签约项目明细失败: {e}")
    
    # 如果发生错误，尝试保存响应内容以便调试
    if 'response' in locals():
        error_filename = f"error_签约项目明细_{datetime.now().strftime('%Y%m%d%H%M%S')}.txt"
        with open(error_filename, "w", encoding="utf-8") as f:
            f.write(f"状态码: {response.status_code}\n")
            f.write(f"响应头: {response.headers}\n\n")
            f.write(f"响应内容:\n{response.text}")
        print(f"[信息] 错误响应已保存为 {error_filename}")
