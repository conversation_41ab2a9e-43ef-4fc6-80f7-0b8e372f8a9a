import requests
import json
from datetime import datetime
import os

url = "http://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/common/excelController/export"

payload = {
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "zhen<PERSON><PERSON><PERSON>"
      }
    },
    "BODY": {
      "IS_EXPORT": "20",
      "CONFIG_CODE": "queryContractListExport",
      "MORE_PARAM": "{\"CONTRACT_SERIAL_NO\":\"\",\"PROJECT_NO\":\"\",\"CONTRACT_NAME\":\"\",\"INC_EXP_TYPE\":\"\",\"CONTRACT_STATUS\":\"\",\"START_DATE\":\"\",\"END_DATE\":\"\",\"REGION_CODE\":\"\",\"POST_CODE\":\"10034,10072,10081,20001,10001,10061,10000,10270,10150,10417,10464,10455,10275,10149\",\"CONTRACT_NO\":\"\"}"
      # "MORE_PARAM": "{\"CONTRACT_SERIAL_NO\":\"\",\"PROJECT_NO\":\"\",\"CONTRACT_NAME\":\"\",\"INC_EXP_TYPE\":\"\",\"CONTRACT_STATUS\":\"\",\"START_DATE\":\"2025-05-01 00:00:00\",\"END_DATE\":\"2025-05-06 23:59:59\",\"REGION_CODE\":\"\",\"POST_CODE\":\"10034,10072,10081,20001,10001,10061,10000,10270,10150,10417,10464,10455,10275,10149\",\"CONTRACT_NO\":\"\"}"

    }
  }
}

headers = {
  'Host': "dict.gmcc.net:30722",
  'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  'Accept': "application/json, text/plain, */*",
  'Accept-Encoding': "gzip, deflate",
  'Content-Type': "application/json",
  'Pragma': "no-cache",
  'Cache-Control': "no-cache",
  'x-session-staffname': "dengyong",
  'x-session-regionid': "200",
  'x-session-sysusercode': "dengyong",
  'x-session-staffid': "10000",
  'Origin': "http://dict.gmcc.net:30722",
  'Referer': "http://dict.gmcc.net:30722/dictWeb/gdydFlowPath/contractManage",
  'Accept-Language': "zh-CN,zh;q=0.9",
  'Cookie': "BSS-SESSION=NGE2NDBmZWMtM2FmZS00YTQ5LThmOWEtZDY5MGViNmQ2NGYz; isLogin=ImlzTG9naW4i; requestId=4e86f220-29ed-11f0-854e-a7722f3aadfe; systemUserCode=InpoZW5nZGV3ZW4i; jsession_id_4_boss=nF4FACEFE75B5ED3D50D25C5B16AE90D8-1"
}

try:
    print("[信息] 正在发送请求获取合同信息列表...")
    response = requests.post(url, data=json.dumps(payload), headers=headers)
    response.raise_for_status()  # 检查响应状态

    # 生成带时间戳的文件名
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    filename = f"合同信息列表_{timestamp}.xlsx"

    # 直接将响应内容以二进制方式写入到带时间戳的xlsx文件中
    with open(filename, "wb") as f:
        f.write(response.content)
    print(f"[成功] 响应内容已保存为 {filename}")
    
except Exception as e:
    print(f"[错误] 获取合同信息列表失败: {e}")
    
    # 如果发生错误，尝试保存响应内容以便调试
    if 'response' in locals():
        error_filename = f"error_合同信息列表_{datetime.now().strftime('%Y%m%d%H%M%S')}.txt"
        with open(error_filename, "w", encoding="utf-8") as f:
            f.write(f"状态码: {response.status_code}\n")
            f.write(f"响应头: {response.headers}\n\n")
            f.write(f"响应内容:\n{response.text}")
        print(f"[信息] 错误响应已保存为 {error_filename}")
