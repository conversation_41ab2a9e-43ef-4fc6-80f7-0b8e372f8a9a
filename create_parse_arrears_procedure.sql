-- =====================================================
-- MySQL 8 存储过程：解析欠费列表JSON数据
-- 功能：将dict_zonghe_queryarrearslist表的RESPONSE_DATA字段中的OUT_PARAM数据解析到dict_zonghe_qiye_qianfei表
-- 作者：系统自动生成
-- 创建时间：2025-07-31
-- =====================================================

-- 删除存储过程（如果存在）
DROP PROCEDURE IF EXISTS parse_arrears_data;

-- 设置分隔符
DELIMITER $$

-- 创建存储过程
CREATE PROCEDURE parse_arrears_data()
BEGIN
    -- 声明变量
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_id INT;
    DECLARE v_response_data JSON;
    DECLARE v_out_param JSON;
    DECLARE v_array_length INT;
    DECLARE v_index INT DEFAULT 0;
    DECLARE v_item JSON;
    DECLARE v_cust_name VARCHAR(500);
    DECLARE v_cust_id VARCHAR(100);
    DECLARE v_owe_amount DECIMAL(15,2);
    DECLARE v_bill_mon VARCHAR(20);
    DECLARE v_import_time TIMESTAMP;
    
    -- 声明游标
    DECLARE cur_arrears CURSOR FOR
        SELECT id, RESPONSE_DATA, import_time
        FROM dict_zonghe_queryarrearslist
        WHERE RESPONSE_DATA IS NOT NULL;
    
    -- 声明异常处理
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    -- 开始事务
    START TRANSACTION;
    
    -- 创建目标表（如果不存在）
    CREATE TABLE IF NOT EXISTS dict_zonghe_qiye_qianfei (
        id INT AUTO_INCREMENT PRIMARY KEY,
        source_id INT COMMENT '来源记录ID',
        cust_name VARCHAR(500) COMMENT '客户名称',
        cust_id VARCHAR(100) COMMENT '客户ID',
        owe_amount DECIMAL(15,2) COMMENT '欠费金额',
        bill_mon VARCHAR(20) COMMENT '账单月份',
        import_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
        source_import_time TIMESTAMP COMMENT '原始导入时间',
        INDEX idx_cust_id (cust_id),
        INDEX idx_bill_mon (bill_mon),
        INDEX idx_owe_amount (owe_amount)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业欠费明细表';
    
    -- 清空目标表
    TRUNCATE TABLE dict_zonghe_qiye_qianfei;
    
    -- 打开游标
    OPEN cur_arrears;
    
    -- 循环处理每条记录
    read_loop: LOOP
        FETCH cur_arrears INTO v_id, v_response_data, v_import_time;
        
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 提取OUT_PARAM数组（根据实际数据结构调整）
        -- 首先尝试直接从根节点获取OUT_PARAM
        SET v_out_param = JSON_EXTRACT(v_response_data, '$.OUT_PARAM');

        -- 如果没有找到，尝试从ROOT.BODY.OUT_DATA.OUT_PARAM获取
        IF v_out_param IS NULL THEN
            SET v_out_param = JSON_EXTRACT(v_response_data, '$.ROOT.BODY.OUT_DATA.OUT_PARAM');
        END IF;
        
        -- 检查OUT_PARAM是否存在且为数组
        IF v_out_param IS NOT NULL AND JSON_TYPE(v_out_param) = 'ARRAY' THEN
            -- 获取数组长度
            SET v_array_length = JSON_LENGTH(v_out_param);
            SET v_index = 0;
            
            -- 循环处理数组中的每个元素
            WHILE v_index < v_array_length DO
                -- 提取当前元素
                SET v_item = JSON_EXTRACT(v_out_param, CONCAT('$[', v_index, ']'));
                
                -- 提取字段值
                SET v_cust_name = JSON_UNQUOTE(JSON_EXTRACT(v_item, '$.CUST_NAME'));
                SET v_cust_id = JSON_UNQUOTE(JSON_EXTRACT(v_item, '$.CUST_ID'));
                SET v_owe_amount = CAST(JSON_UNQUOTE(JSON_EXTRACT(v_item, '$.OWE_AMOUNT')) AS DECIMAL(15,2));
                SET v_bill_mon = JSON_UNQUOTE(JSON_EXTRACT(v_item, '$.BILL_MON'));
                
                -- 插入到目标表
                INSERT INTO dict_zonghe_qiye_qianfei (
                    source_id,
                    cust_name,
                    cust_id,
                    owe_amount,
                    bill_mon,
                    source_import_time
                ) VALUES (
                    v_id,
                    v_cust_name,
                    v_cust_id,
                    v_owe_amount,
                    v_bill_mon,
                    v_import_time
                );
                
                -- 增加索引
                SET v_index = v_index + 1;
            END WHILE;
        END IF;
    END LOOP;
    
    -- 关闭游标
    CLOSE cur_arrears;
    
    -- 提交事务
    COMMIT;
    
    -- 输出统计信息
    SELECT 
        '数据解析完成' AS status,
        COUNT(*) AS total_records,
        SUM(owe_amount) AS total_owe_amount,
        MIN(bill_mon) AS earliest_bill,
        MAX(bill_mon) AS latest_bill
    FROM dict_zonghe_qiye_qianfei;
    
END$$

-- 恢复分隔符
DELIMITER ;

-- =====================================================
-- 使用说明
-- =====================================================
-- 1. 执行存储过程：CALL parse_arrears_data();
-- 2. 查看结果：SELECT * FROM dict_zonghe_qiye_qianfei LIMIT 10;
-- 3. 统计信息：
--    SELECT 
--        COUNT(*) as 总记录数,
--        COUNT(DISTINCT cust_id) as 客户数量,
--        SUM(owe_amount) as 总欠费金额,
--        AVG(owe_amount) as 平均欠费金额
--    FROM dict_zonghe_qiye_qianfei;

-- =====================================================
-- 示例查询
-- =====================================================
-- 查看欠费金额最高的前10个客户
-- SELECT cust_name, cust_id, owe_amount, bill_mon 
-- FROM dict_zonghe_qiye_qianfei 
-- ORDER BY owe_amount DESC 
-- LIMIT 10;

-- 按账单月份统计欠费情况
-- SELECT 
--     bill_mon,
--     COUNT(*) as 客户数量,
--     SUM(owe_amount) as 总欠费金额
-- FROM dict_zonghe_qiye_qianfei 
-- GROUP BY bill_mon 
-- ORDER BY bill_mon DESC;
