#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
查询欠费列表爬虫程序
接口：saleCenterApp/arrearage/queryArrearsList
"""

import sys
import os
import json
import requests
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
import pymysql

sys.path.append(os.path.join(os.path.dirname(__file__), 'dict_romte'))
from dict_romte.config import get_db_config, get_login_credentials

DB_CONFIG = get_db_config('default')
API_URL = "https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/arrearage/queryArrearsList"

def load_cookies():
    try:
        with open('cookies.txt', 'r', encoding='utf-8') as f:
            cookie_data = json.load(f)
        cookies = {}
        for cookie in cookie_data.get('cookies', []):
            cookies[cookie['name']] = cookie['value']
        return cookies
    except:
        return {}

def get_test_project_ids():
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        cursor.execute("SELECT project_id FROM v_distinct_project_id LIMIT 5")
        return [row[0] for row in cursor.fetchall()]
    except:
        return ["TEST_PROJECT"]
    finally:
        if 'cursor' in locals(): cursor.close()
        if 'conn' in locals(): conn.close()

def query_api(project_id, cookies, login_no):
    try:
        cookie_str = "; ".join([f"{k}={v}" for k, v in cookies.items()])
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Content-Type': 'application/json;charset=UTF-8',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Origin': 'https://dict.gmcc.net:30722',
            'Referer': 'https://dict.gmcc.net:30722/',
            'x-session-regionid': '760',
            'x-session-staffid': '1000032328',
            'x-session-staffname': 'zhengdewen',
            'x-session-sysusercode': 'zhengdewen',
            'Cookie': cookie_str
        }
        
        body = {
            "IN_PARAM": {
                "REGION_CODE": "760"  #  ,  # 可以使用具体的名称或者id来查询具体客户，也可以不加id或名称参数，查询所有
                # "CUST_ID": "2001330585"  # 使用示例中的客户ID"
                #  CUST_NAME": "江苏瑞英达通信工程有限公司",

            },
            "PAGE_INFO": {
                "PAGE_NUM": 1,
                "PAGE_SIZE": 10000   # 一次查出所有数据
            }
        }

        data = {
            "ROOT": {
                "HEADER": {"OPR_INFO": {"LOGIN_NO": login_no}},
                "BODY": body
            }
        }
        
        response = requests.post(API_URL, headers=headers, json=data, timeout=30, verify=False)
        print(f"[调试] 响应状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print(f"[调试] 响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return_code = result.get('ROOT', {}).get('BODY', {}).get('RETURN_CODE', '')
            return_msg = result.get('ROOT', {}).get('BODY', {}).get('RETURN_MSG', '')
            print(f"[调试] 返回码: {return_code}, 返回消息: {return_msg}")
            if return_code == '0':
                return result.get('ROOT', {}).get('BODY', {}).get('OUT_DATA', {})
        else:
            print(f"[调试] 响应内容: {response.text}")
        return None
    except Exception as e:
        print(f"[错误] API调用失败: {e}")
        return None

def save_simple_data(data_list):
    if not data_list:
        return
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 创建简单表（如果不存在）
        create_sql = f"""
        CREATE TABLE IF NOT EXISTS dict_zonghe_queryArrearsList (
            id int AUTO_INCREMENT PRIMARY KEY,
            cust_name varchar(200),
            cust_id varchar(100),
            owe_amount decimal(15,2),
            bill_mon varchar(10),
            response_data text,
            import_time timestamp DEFAULT CURRENT_TIMESTAMP
        )
        """
        cursor.execute(create_sql)

        # 清空并插入数据
        cursor.execute(f"DELETE FROM dict_zonghe_queryArrearsList")

        for data in data_list:
            # 解析响应数据
            out_data = data['response'].get('OUT_DATA', {})
            out_param = out_data.get('OUT_PARAM', [])

            if out_param:
                for item in out_param:
                    cursor.execute(
                        f"INSERT INTO dict_zonghe_queryArrearsList (cust_name, cust_id, owe_amount, bill_mon, response_data) VALUES (%s, %s, %s, %s, %s)",
                        (
                            item.get('CUST_NAME', ''),
                            item.get('CUST_ID', ''),
                            item.get('OWE_AMOUNT', 0),
                            item.get('BILL_MON', ''),
                            json.dumps(data['response'], ensure_ascii=False)
                        )
                    )
            else:
                # 如果没有具体数据，只保存原始响应
                cursor.execute(
                    f"INSERT INTO dict_zonghe_queryArrearsList (response_data) VALUES (%s)",
                    (json.dumps(data['response'], ensure_ascii=False),)
                )
        
        conn.commit()
        print(f"[成功] 保存了 {len(data_list)} 条数据到 dict_zonghe_queryArrearsList")
        
    except Exception as e:
        print(f"[错误] 保存数据失败: {e}")
    finally:
        if 'cursor' in locals(): cursor.close()
        if 'conn' in locals(): conn.close()

def main():
    print(f"开始测试接口: 查询欠费列表")
    
    cookies = load_cookies()
    if not cookies:
        print("[错误] 无法加载Cookie")
        return
    
    login_no, _, _ = get_login_credentials()

    # 这个接口不需要项目ID，直接调用一次
    data_list = []
    result = query_api("", cookies, login_no)
    if result:
        data_list.append({'response': result})
        print(f"[成功] 获取欠费列表数据成功")
    else:
        print(f"[失败] 获取欠费列表数据失败")
    
    save_simple_data(data_list)
    print(f"接口测试完成: 查询欠费列表")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "-all":
        main()
    else:
        print("使用方法: python dict_zonghe_queryArrearsList.py -all")
