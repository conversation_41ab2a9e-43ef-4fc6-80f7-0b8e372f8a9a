#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import time
import subprocess
from datetime import datetime

# 日志文件名
LOG_FILE = "main_dict_runlog.txt"

def log_message(message, print_to_console=True):
    """将消息写入日志文件并可选择性地打印到控制台"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    log_entry = f"[{timestamp}] {message}"

    # 写入日志文件
    try:
        with open(LOG_FILE, 'a', encoding='utf-8') as f:
            f.write(log_entry + '\n')
    except Exception as e:
        print(f"写入日志文件失败: {e}")

    # 打印到控制台
    if print_to_console:
        print(message)

def run_script(script_name, description=None):
    """运行指定的Python脚本并等待其完成"""
    if description:
        log_message(f"\n{'='*50}")
        log_message(f"🚀 {description}")
        log_message(f"{'='*50}")

    log_message(f"[信息] 正在运行 {script_name}...")

    try:
        # 使用subprocess运行脚本，指定encoding为utf-8
        result = subprocess.run([sys.executable, script_name],
                               capture_output=True,
                               text=True,
                               encoding='utf-8',  # 明确指定使用UTF-8编码
                               check=True)

        # 记录脚本的输出到日志
        if result.stdout:
            log_message(f"[输出] {script_name} 脚本输出:")
            # 将脚本输出按行记录到日志
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    log_message(f"  {line}")

        if result.returncode == 0:
            log_message(f"[成功] {script_name} 运行完成")
            return True
        else:
            log_message(f"[错误] {script_name} 运行失败，返回码: {result.returncode}")
            if result.stderr:
                log_message(f"错误信息: {result.stderr}")
            return False

    except subprocess.CalledProcessError as e:
        log_message(f"[错误] 运行 {script_name} 时出错: {e}")
        if e.stderr:
            log_message(f"错误信息: {e.stderr}")
        return False
    except Exception as e:
        log_message(f"[错误] 运行 {script_name} 时发生异常: {e}")
        return False

def main():
    """主函数，按顺序执行所有脚本"""
    # 在日志文件末尾添加新的运行记录开始标记
    try:
        with open(LOG_FILE, 'a', encoding='utf-8') as f:
            f.write(f"\n{'='*80}\n")
            f.write(f"=== main_dict.py 新运行记录开始 ===\n")
            f.write(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("="*80 + "\n\n")
    except Exception as e:
        print(f"写入日志开始标记失败: {e}")

    start_time = time.time()
    log_message(f"📊 开始执行数据采集与导入流程 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 步骤1: 运行getcookie.py，获取cookie
    if not run_script("getcookie.py", "步骤1: 获取登录Cookie"):
        log_message("[错误] 获取Cookie失败，流程终止")
        return

    # 步骤2: 运行NEW_sign_get_jsessionid.py，更新cookie
    if not run_script("NEW_sign_get_jsessionid.py", "步骤2: 更新JSESSIONID"):
        log_message("[错误] 更新JSESSIONID失败，流程终止")
        return

    # 步骤3: 运行get_sign_detail.py，获取签约项目明细
    if not run_script("get_sign_detail.py", "步骤3: 获取签约项目明细"):
        log_message("[错误] 获取签约项目明细失败，流程终止")
        return

    # 查找最新的签约项目明细文件
    detail_files = [f for f in os.listdir('.') if f.startswith('temp_签约项目明细_') and f.endswith('.xlsx')]
    if not detail_files:
        log_message("[错误] 未找到签约项目明细文件，流程终止")
        return

    latest_detail_file = max(detail_files, key=os.path.getmtime)
    log_message(f"[信息] 找到最新的签约项目明细文件: {latest_detail_file}")

    # 步骤4: 运行merge_headers.py，格式化表头
    if not run_script("merge_headers.py", "步骤4: 合并表头"):
        log_message("[错误] 合并表头失败，流程终止")
        return

    # 查找最新的合并表头后的文件
    merged_files = [f for f in os.listdir('.') if f.startswith('merged_header_') and f.endswith('.xlsx')]
    if not merged_files:
        log_message("[错误] 未找到合并表头后的文件，流程终止")
        return

    latest_merged_file = max(merged_files, key=os.path.getmtime)
    log_message(f"[信息] 找到最新的合并表头文件: {latest_merged_file}")

    # 步骤5: 运行import_to_mysql.py，导入数据到MySQL
    if not run_script("import_to_mysql.py", "步骤5: 导入数据到MySQL"):
        log_message("[错误] 导入数据到MySQL失败，流程终止")
        return

    # 步骤6: 运行开工日期处理project_start_date_oldcookie.py，获取项目开工日期
    if not run_script("开工日期处理project_start_date_oldcookie.py", "步骤6: 获取项目开工日期"):
        log_message("[错误] 获取项目开工日期失败，流程终止")
        return

    # 步骤7: 运行合同处理.py，处理合同数据
    if not run_script("合同处理.py", "步骤7: 处理合同数据"):
        log_message("[错误] 处理合同数据失败，流程终止")
        return

    # 步骤8: 运行DICT_hetong_local2remote.py，同步合同数据到远程数据库
    if not run_script("DICT_hetong_local2remote.py", "步骤8: 同步合同数据到远程数据库"):
        log_message("[错误] 同步合同数据到远程数据库失败，流程终止")
        return

    # 步骤9: 运行DICT_project_start_date_local2remote.py，同步项目开工日期到远程数据库
    if not run_script("DICT_project_start_date_local2remote.py", "步骤9: 同步项目开工日期到远程数据库"):
        log_message("[错误] 同步项目开工日期到远程数据库失败，流程终止")
        return

    # 步骤10: 运行DICT_sign_data_detail_local2remote.py，同步签约明细数据到远程数据库
    if not run_script("DICT_sign_data_detail_local2remote.py", "步骤10: 同步签约明细数据到远程数据库"):
        log_message("[错误] 同步签约明细数据到远程数据库失败，流程终止")
        return

    # 步骤11: 运行dict_run_turn2tkuanbiao_procedure.py，执行远程数据库存储过程
    if not run_script("dict_run_turn2tkuanbiao_procedure.py", "步骤11: 执行远程数据库存储过程"):
        log_message("[错误] 执行远程数据库存储过程失败，流程终止")
        return

    # 步骤12: 运行dict_download_kuanbiao_from_db2email.py，下载宽表数据并发送邮件
    if not run_script("dict_download_kuanbiao_from_db2email.py", "步骤12: 下载宽表数据并发送邮件"):
        log_message("[错误] 下载宽表数据并发送邮件失败，流程终止")
        return

    # 计算总耗时
    end_time = time.time()
    duration = end_time - start_time
    minutes, seconds = divmod(duration, 60)

    log_message(f"\n{'='*50}")
    log_message(f"✅ 全部流程执行完成！")
    log_message(f"总耗时: {int(minutes)}分{int(seconds)}秒")
    log_message(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    log_message(f"{'='*50}")

    # 在日志文件末尾添加运行结束标记
    try:
        with open(LOG_FILE, 'a', encoding='utf-8') as f:
            f.write("\n" + "="*80 + "\n")
            f.write(f"=== 本次运行结束 ===\n")
            f.write(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("="*80 + "\n\n")
    except Exception as e:
        print(f"写入日志结束标记失败: {e}")

if __name__ == "__main__":
    main()
