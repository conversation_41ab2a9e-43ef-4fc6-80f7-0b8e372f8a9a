#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深入调查 JavaScript 中 I.encrypt() 的真实行为
"""

import hashlib
import base64
from urllib.parse import quote, unquote
from Crypto.Cipher import DES
from Crypto.Util.Padding import pad, unpad
import binas<PERSON><PERSON>


def reverse_engineer_complete():
    """完全反向工程期望结果"""
    print("=== 完全反向工程 ===")
    
    # 已知信息
    password = "Dewen@428"
    login_key = "1751645934512463581"
    expected_result = "VDHesuGD7wIGwc4g5lGu8To/DJCbvhoXLNhgvlYLOvijLZkSzAqarPuovKdSjaUB7D3zY+aor2pR45MJ6ByKN48SSfgtNCOBI+WUcxVza+PAXEQFqdN0yVo2/Suq9I5X/LPMn3FJ15v38b6rElgQ129VRDtYXDUB5Gv+VLUZdCGUNKB8EtVCBC14AhMZAqcFSVqflT2OKF+Xn/oW6aJvogh53WmseSQHbaijwBDda9O4Y2GIKKewrnVX8TMzZ4VO14PUsZYDh3Hx2gyTvTpxta6jhXrIUYGxGfj0I5KBW6O2kDS5uD789urnkxA39YkcO3Jq5Egy0XuIaxyMi2uGeIoUev0en9Z1pSMU9ryi1zKyW+pJg9wRKDrGdAPKste1nYsLyiv7CNN8KNh3m3r/lbLXQ9mFouZEhHuBMcExh1SdNeXmod3Asbcw9aqeL9mmURgNiHUZUZcGf4yYWLScRA=="
    
    # 计算 MD5
    md5_result = hashlib.md5(password.encode('utf-8')).hexdigest()
    print(f"MD5 哈希: {md5_result}")
    
    # 解析期望结果
    url_decoded = unquote(expected_result)
    print(f"URL 解码长度: {len(url_decoded)}")
    
    # Base64 解码得到 DES 加密的数据
    des_encrypted_data = base64.b64decode(url_decoded)
    print(f"DES 加密数据长度: {len(des_encrypted_data)}")
    print(f"DES 加密数据 (hex): {binascii.hexlify(des_encrypted_data)[:100]}...")
    
    # 准备 DES 密钥
    key_bytes = login_key.encode('utf-8')[:8].ljust(8, b'\x00')
    print(f"DES 密钥: {key_bytes}")
    
    # DES 解密
    cipher = DES.new(key_bytes, DES.MODE_ECB)
    try:
        decrypted_data = cipher.decrypt(des_encrypted_data)
        print(f"DES 解密数据长度: {len(decrypted_data)}")
        print(f"DES 解密数据 (hex): {binascii.hexlify(decrypted_data)[:100]}...")
        
        # 尝试去除填充
        try:
            unpadded_data = unpad(decrypted_data, DES.block_size)
            print(f"去除填充后长度: {len(unpadded_data)}")
            print(f"去除填充后 (hex): {binascii.hexlify(unpadded_data)[:100]}...")
            
            # 这应该是 I.encrypt() 的结果
            return unpadded_data
            
        except Exception as e:
            print(f"去除填充失败: {e}")
            # 可能填充方式不同，尝试手动去除
            return try_manual_unpad(decrypted_data)
            
    except Exception as e:
        print(f"DES 解密失败: {e}")
        return None


def try_manual_unpad(data):
    """手动尝试去除填充"""
    print("\n--- 手动去除填充 ---")
    
    # 检查最后几个字节
    last_byte = data[-1]
    print(f"最后一个字节: {last_byte} (0x{last_byte:02x})")
    
    # 尝试 PKCS7 填充
    if 1 <= last_byte <= 8:
        padding_length = last_byte
        padding_bytes = data[-padding_length:]
        print(f"可能的填充长度: {padding_length}")
        print(f"填充字节: {[b for b in padding_bytes]}")
        
        # 检查是否所有填充字节都相同
        if all(b == last_byte for b in padding_bytes):
            unpadded = data[:-padding_length]
            print(f"PKCS7 去填充成功，长度: {len(unpadded)}")
            return unpadded
    
    # 尝试零填充
    while data and data[-1] == 0:
        data = data[:-1]
    print(f"零填充去除后长度: {len(data)}")
    
    return data


def analyze_i_encrypt_result(data):
    """分析 I.encrypt() 的结果"""
    if data is None:
        return
    
    print(f"\n=== 分析 I.encrypt() 结果 ===")
    print(f"数据长度: {len(data)}")
    print(f"数据 (hex): {binascii.hexlify(data)}")
    
    # 尝试作为 base64 解码
    try:
        as_string = data.decode('utf-8')
        print(f"作为字符串: {as_string[:100]}...")
        
        # 检查是否是 base64
        try:
            b64_decoded = base64.b64decode(as_string)
            print(f"Base64 解码长度: {len(b64_decoded)}")
            print(f"Base64 解码 (hex): {binascii.hexlify(b64_decoded)[:100]}...")
        except:
            print("不是有效的 base64")
            
    except:
        print("不是有效的 UTF-8 字符串")
    
    # 检查是否是 MD5 的某种变换
    md5_result = "f0a9c06391f93b2a5703cba3d9fc3ce3"
    print(f"原始 MD5: {md5_result}")
    
    # 检查长度关系
    if len(data) == 256:
        print("长度 256 - 可能是 RSA-2048 加密结果")
    elif len(data) == 32:
        print("长度 32 - 可能是 MD5 哈希")
    elif len(data) == 44:
        print("长度 44 - 可能是 base64 编码的 256 位数据")


def test_simple_encryption():
    """测试简单的加密方法"""
    print("\n=== 测试简单加密 ===")
    
    password = "Dewen@428"
    login_key = "1751645934512463581"
    
    md5_result = hashlib.md5(password.encode('utf-8')).hexdigest()
    print(f"MD5: {md5_result}")
    
    # 测试1: 直接用 MD5 作为 I.encrypt() 的结果
    print("\n测试1: MD5 直接作为输入")
    test_des_encrypt(md5_result, login_key)
    
    # 测试2: MD5 的 base64 编码
    print("\n测试2: MD5 的 base64 编码")
    md5_b64 = base64.b64encode(md5_result.encode('utf-8')).decode('utf-8')
    test_des_encrypt(md5_b64, login_key)
    
    # 测试3: MD5 的十六进制字节
    print("\n测试3: MD5 的十六进制字节")
    md5_bytes = bytes.fromhex(md5_result)
    test_des_encrypt_bytes(md5_bytes, login_key)


def test_des_encrypt(data_str, key_str):
    """测试 DES 加密字符串"""
    key_bytes = key_str.encode('utf-8')[:8].ljust(8, b'\x00')
    cipher = DES.new(key_bytes, DES.MODE_ECB)
    
    data_bytes = data_str.encode('utf-8')
    padded_data = pad(data_bytes, DES.block_size)
    encrypted = cipher.encrypt(padded_data)
    
    b64_result = base64.b64encode(encrypted).decode('utf-8')
    final_result = quote(b64_result)
    
    print(f"  输入: {data_str[:50]}...")
    print(f"  结果: {final_result[:50]}...")
    print(f"  长度: {len(final_result)}")


def test_des_encrypt_bytes(data_bytes, key_str):
    """测试 DES 加密字节"""
    key_bytes = key_str.encode('utf-8')[:8].ljust(8, b'\x00')
    cipher = DES.new(key_bytes, DES.MODE_ECB)
    
    padded_data = pad(data_bytes, DES.block_size)
    encrypted = cipher.encrypt(padded_data)
    
    b64_result = base64.b64encode(encrypted).decode('utf-8')
    final_result = quote(b64_result)
    
    print(f"  输入 (hex): {binascii.hexlify(data_bytes)}")
    print(f"  结果: {final_result[:50]}...")
    print(f"  长度: {len(final_result)}")


if __name__ == "__main__":
    # 反向工程
    i_encrypt_result = reverse_engineer_complete()
    
    # 分析结果
    analyze_i_encrypt_result(i_encrypt_result)
    
    # 测试简单方法
    test_simple_encryption()
    
    print("\n" + "="*60)
    print("💡 调查结论:")
    print("1. 需要确定 I.encrypt() 的真实实现")
    print("2. 可能不是标准的 RSA 加密")
    print("3. 可能是某种简单的编码或哈希变换")
    print("4. 建议检查 JavaScript 中 I 变量的具体定义")
