import imaplib
import email
import os
import re

def download_attachments():
    # 邮箱配置
    email_user = '<EMAIL>'
    email_password = 'your_password'
    email_server = 'imap.126.com'
    
    # 连接到邮箱服务器
    mail = imaplib.IMAP4_SSL(email_server)
    mail.login(email_user, email_password)
    mail.select('inbox')
    
    # 搜索邮件
    result, data = mail.uid('search', None, "ALL")
    if result == 'OK':
        for num in data[0].split():
            result, data = mail.uid('fetch', num, '(RFC822)')
            raw_email = data[0][1]
            
            # 解析邮件
            raw_email_string = raw_email.decode('utf-8')
            email_message = email.message_from_string(raw_email_string)
            
            # 查找附件
            for part in email_message.walk():
                if part.get_content_maintype() == 'multipart':
                    continue
                if part.get('Content-Disposition') is None:
                    continue
                
                filename = part.get_filename()
                if bool(filename):
                    filepath = os.path.join('attachments', filename)
                    if not os.path.isfile(filepath):
                        with open(filepath, 'wb') as f:
                            f.write(part.get_payload(decode=True))
                        print(f'附件已保存: {filepath}')
    
    mail.logout()

if __name__ == "__main__":
    download_attachments()