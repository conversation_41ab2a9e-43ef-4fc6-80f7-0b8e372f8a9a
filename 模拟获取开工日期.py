import requests
import json
import pymysql
from datetime import datetime

# 数据库连接配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'cmcc12345',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

# API请求配置
url = "http://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp//preparation/queryProjectStart"

headers = {
  'Host': "dict.gmcc.net:30722",
  'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  'Accept': "application/json, text/plain, */*",
  'Accept-Encoding': "gzip, deflate",
  'Content-Type': "application/json",
  'Pragma': "no-cache",
  'Cache-Control': "no-cache",
  'x-session-staffname': "dengyong",
  'x-session-regionid': "999",
  'x-session-sysusercode': "dengyong",
  'x-session-staffid': "1000032328",
  'Origin': "http://dict.gmcc.net:30722",
  'Referer': "http://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyNTAxMTEwMDEiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTI1MzAyMTI2NjAxMDc2In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyNTAxMTEwMDEiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTI1MzAyMTI2NjAxMDc2In0%3D",
  'Accept-Language': "zh-CN,zh;q=0.9",
  'Cookie': "BSS-SESSION=YzlkNzgwMGMtYTc2NC00ODhjLWFjNWUtYmNhNjVhODBmM2Nk; isLogin=ImlzTG9naW4i; requestId=f9f2ac90-282f-11f0-99ba-7762552e49f1; systemUserCode=ImxpYW9jaHVsaW4i; jsession_id_4_boss=n3B6F7020112A2F6AFD48825680A2657B-1"
}

def get_project_codes():
    """从MySQL获取项目编码列表"""
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    try:
        # 从开工时间视图获取项目编码
        cursor.execute("SELECT 项目编码 FROM 开工时间视图")
        project_codes = [row[0] for row in cursor.fetchall()]
        return project_codes
    except Exception as e:
        print(f"获取项目编码失败: {e}")
        return []
    finally:
        cursor.close()
        conn.close()

def get_start_date(project_code):
    """获取项目的开工日期"""
    payload = {
      "ROOT": {
        "HEADER": {
          "OPR_INFO": {
            "LOGIN_NO": "liaochulin"
          }
        },
        "BODY": {
          "PROJECT_ID": project_code
        }
      }
    }
    
    try:
        response = requests.post(url, data=json.dumps(payload), headers=headers)
        data = response.json()
        # 从响应中提取开工日期
        start_date = data.get('ROOT', {}).get('BODY', {}).get('OUT_DATA', {}).get('PLAN_START_DATE', '')
        return start_date
    except Exception as e:
        print(f"获取项目 {project_code} 的开工日期失败: {e}")
        return None

def save_start_date(project_code, start_date):
    """保存项目开工日期到数据库"""
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    current_date = datetime.now().strftime("%Y-%m-%d")
    
    try:
        # 使用INSERT INTO语句，按照已有表结构插入数据
        cursor.execute("""
        INSERT INTO project_start_date (项目编码, 开工时间, 查询日期)
        VALUES (%s, %s, %s)
        ON DUPLICATE KEY UPDATE 开工时间 = %s, 查询日期 = %s
        """, (project_code, start_date, current_date, start_date, current_date))
        conn.commit()
    except Exception as e:
        conn.rollback()
        print(f"保存项目 {project_code} 的开工日期失败: {e}")
    finally:
        cursor.close()
        conn.close()

def main():
    # 获取所有项目编码
    project_codes = get_project_codes()
    print(f"获取到 {len(project_codes)} 个项目编码")
    
    # 获取并保存每个项目的开工日期
    for i, project_code in enumerate(project_codes):
        print(f"处理项目 {i+1}/{len(project_codes)}: {project_code}")
        start_date = get_start_date(project_code)
        if start_date:
            save_start_date(project_code, start_date)
            print(f"项目 {project_code} 的开工日期: {start_date}")
        else:
            print(f"项目 {project_code} 未获取到开工日期")

if __name__ == "__main__":
    main()