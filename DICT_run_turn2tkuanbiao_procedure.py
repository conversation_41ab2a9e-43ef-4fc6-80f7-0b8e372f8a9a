#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DICT_run_turn2tkuanbiao_procedure.py - 运行远程数据库存储过程脚本
功能：连接远程数据库并执行 turn2tkuanbiao 存储过程

存储过程功能：
- 创建 t_kuanbiao 表（如果不存在）
- 清空 t_kuanbiao 表
- 从 宽表 复制数据到 t_kuanbiao 表
"""

import pymysql
from datetime import datetime
import sys
import traceback
import time

# 远程数据库配置
REMOTE_DB_CONFIG = {
    'host': '*************',
    'port': 12136,
    'user': 'root',
    'password': '5eb9a11916e3a66d',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

def connect_to_database(config, db_type="数据库"):
    """连接到数据库"""
    try:
        print(f"[信息] 正在连接{db_type} {config['host']}:{config['port']}...")
        conn = pymysql.connect(**config)
        print(f"[成功] 已连接到{db_type}: {config['database']}")
        return conn
    except Exception as e:
        print(f"[错误] 连接{db_type}失败: {e}")
        return None

def check_procedure_exists(conn, procedure_name):
    """检查存储过程是否存在"""
    try:
        cursor = conn.cursor()
        cursor.execute("SHOW PROCEDURE STATUS WHERE Name = %s", (procedure_name,))
        result = cursor.fetchone()
        cursor.close()
        
        if result:
            print(f"[信息] 存储过程 {procedure_name} 存在")
            return True
        else:
            print(f"[错误] 存储过程 {procedure_name} 不存在")
            return False
    except Exception as e:
        print(f"[错误] 检查存储过程存在性失败: {e}")
        return False

def get_table_record_count(conn, table_name):
    """获取表记录数"""
    try:
        cursor = conn.cursor()
        cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
        count = cursor.fetchone()[0]
        cursor.close()
        print(f"[信息] 表 {table_name} 当前记录数: {count}")
        return count
    except Exception as e:
        print(f"[错误] 获取表 {table_name} 记录数失败: {e}")
        return 0

def check_source_table(conn, table_name):
    """检查源表是否存在"""
    try:
        cursor = conn.cursor()
        cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
        result = cursor.fetchone()
        cursor.close()
        
        if result:
            print(f"[信息] 源表 {table_name} 存在")
            return True
        else:
            print(f"[警告] 源表 {table_name} 不存在")
            return False
    except Exception as e:
        print(f"[错误] 检查源表存在性失败: {e}")
        return False

def run_procedure(conn, procedure_name):
    """执行存储过程"""
    try:
        print(f"[信息] 开始执行存储过程 {procedure_name}...")
        start_time = time.time()
        
        cursor = conn.cursor()
        
        # 执行存储过程
        cursor.callproc(procedure_name)
        
        # 提交事务
        conn.commit()
        cursor.close()
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"[成功] 存储过程 {procedure_name} 执行完成")
        print(f"[统计] 执行耗时: {execution_time:.2f}秒")
        
        return True
    except Exception as e:
        print(f"[错误] 执行存储过程失败: {e}")
        print(traceback.format_exc())
        conn.rollback()
        return False

def validate_procedure_result(conn):
    """验证存储过程执行结果"""
    try:
        print(f"[信息] 正在验证存储过程执行结果...")
        
        # 检查源表记录数
        source_count = get_table_record_count(conn, "宽表")
        
        # 检查目标表记录数
        target_count = get_table_record_count(conn, "t_kuanbiao")
        
        if source_count == target_count:
            print(f"[验证] ✅ 数据复制成功: 源表={source_count}条, 目标表={target_count}条")
            return True
        else:
            print(f"[验证] ❌ 数据复制可能有问题: 源表={source_count}条, 目标表={target_count}条")
            return False
            
    except Exception as e:
        print(f"[错误] 验证执行结果失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 80)
    print("DICT_run_turn2tkuanbiao_procedure.py - 执行远程数据库存储过程")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)

    # 连接远程数据库
    print("\n1. 连接远程数据库...")
    remote_conn = connect_to_database(REMOTE_DB_CONFIG, "远程数据库")
    if not remote_conn:
        print("[错误] 无法连接远程数据库，程序退出")
        sys.exit(1)

    try:
        # 检查存储过程是否存在
        print(f"\n2. 检查存储过程 turn2tkuanbiao...")
        if not check_procedure_exists(remote_conn, "turn2tkuanbiao"):
            print("[错误] 存储过程不存在，程序退出")
            return False

        # 检查源表是否存在
        print(f"\n3. 检查源表 宽表...")
        if not check_source_table(remote_conn, "宽表"):
            print("[警告] 源表不存在，存储过程可能会失败")

        # 显示执行前的状态
        print(f"\n4. 执行前状态检查...")
        source_count = get_table_record_count(remote_conn, "宽表")
        
        # 检查目标表是否存在
        if check_source_table(remote_conn, "t_kuanbiao"):
            target_count_before = get_table_record_count(remote_conn, "t_kuanbiao")
        else:
            target_count_before = 0
            print("[信息] 目标表 t_kuanbiao 不存在，将由存储过程创建")

        # 执行存储过程
        print(f"\n5. 执行存储过程 turn2tkuanbiao...")
        if not run_procedure(remote_conn, "turn2tkuanbiao"):
            print("[错误] 存储过程执行失败")
            return False

        # 验证执行结果
        print(f"\n6. 验证执行结果...")
        validation_passed = validate_procedure_result(remote_conn)
        
        if validation_passed:
            print("[验证] ✅ 存储过程执行成功，数据复制完成")
        else:
            print("[验证] ❌ 存储过程执行可能有问题，请检查数据")

        print("\n" + "=" * 80)
        print("✅ 存储过程执行完成!")
        print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        return True

    except Exception as e:
        print(f"\n[错误] 程序执行过程中出现异常: {e}")
        print(traceback.format_exc())
        return False
    finally:
        # 关闭数据库连接
        if remote_conn:
            remote_conn.close()
            print("[信息] 远程数据库连接已关闭")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
