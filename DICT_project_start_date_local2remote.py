#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DICT_project_start_date_local2remote.py - 本地project_start_date表到远程数据库同步脚本
功能：将本地数据库的project_start_date表数据同步到远程数据库

表结构说明：
- 无主键，包含3个业务字段：项目编码、开工时间、查询日期
- 字符集：utf8mb4
- 引擎：InnoDB
"""

import pymysql
import pandas as pd
from datetime import datetime
import sys
import traceback
import time

# 本地数据库配置 (数据源)
LOCAL_DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'cmcc12345',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

# 远程数据库配置 (目标数据库)
REMOTE_DB_CONFIG = {
    'host': '*************',
    'port': 12136,
    'user': 'root',
    'password': '5eb9a11916e3a66d',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

# 表名
TABLE_NAME = 'project_start_date'

# project_start_date表字段列表（无主键，包含所有字段）
PROJECT_START_DATE_COLUMNS = [
    '项目编码', '开工时间', '查询日期'
]

def connect_to_database(config, db_type="数据库"):
    """连接到数据库"""
    try:
        print(f"[信息] 正在连接{db_type} {config['host']}:{config['port']}...")
        conn = pymysql.connect(**config)
        print(f"[成功] 已连接到{db_type}: {config['database']}")
        return conn
    except Exception as e:
        print(f"[错误] 连接{db_type}失败: {e}")
        return None

def test_table_exists(conn, table_name, db_type="数据库"):
    """测试表是否存在"""
    try:
        cursor = conn.cursor()
        cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
        result = cursor.fetchone()
        cursor.close()
        
        if result:
            print(f"[信息] {db_type}中表 {table_name} 存在")
            return True
        else:
            print(f"[警告] {db_type}中表 {table_name} 不存在")
            return False
    except Exception as e:
        print(f"[错误] 检查表存在性失败: {e}")
        return False

def get_record_count(conn, table_name, db_type="数据库"):
    """获取表记录数"""
    try:
        cursor = conn.cursor()
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        cursor.close()
        print(f"[信息] {db_type} {table_name} 表当前记录数: {count}")
        return count
    except Exception as e:
        print(f"[错误] 获取{db_type}记录数失败: {e}")
        return 0

def create_remote_table_if_not_exists(remote_conn):
    """在远程数据库创建project_start_date表（如果不存在）"""
    try:
        cursor = remote_conn.cursor()
        
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS `project_start_date` (
          `项目编码` text COLLATE utf8mb4_general_ci,
          `开工时间` text COLLATE utf8mb4_general_ci,
          `查询日期` text COLLATE utf8mb4_general_ci
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        """
        
        cursor.execute(create_table_sql)
        remote_conn.commit()
        cursor.close()
        print(f"[成功] 远程数据库表 {TABLE_NAME} 创建完成")
        return True
    except Exception as e:
        print(f"[错误] 创建远程表失败: {e}")
        return False

def truncate_remote_table(remote_conn, table_name):
    """清空远程表数据"""
    try:
        cursor = remote_conn.cursor()
        cursor.execute(f"TRUNCATE TABLE {table_name}")
        remote_conn.commit()
        cursor.close()
        print(f"[成功] 远程表 {table_name} 数据已清空")
        return True
    except Exception as e:
        print(f"[错误] 清空远程表失败: {e}")
        return False

def copy_data_from_local(local_conn, remote_conn, table_name, batch_size=1000):
    """从本地数据库复制数据到远程数据库 - 针对project_start_date表优化"""
    try:
        print(f"[信息] 开始从本地数据库复制 {table_name} 表数据...")
        start_time = time.time()

        # 获取本地表记录数
        local_count = get_record_count(local_conn, table_name, "本地")
        if local_count == 0:
            print("[警告] 本地表没有数据")
            return True

        # 分批读取和插入数据
        local_cursor = local_conn.cursor()
        remote_cursor = remote_conn.cursor()

        # 使用预定义的列名列表
        columns_str = ', '.join([f"`{col}`" for col in PROJECT_START_DATE_COLUMNS])
        placeholders = ', '.join(['%s'] * len(PROJECT_START_DATE_COLUMNS))

        print(f"[信息] 将复制 {len(PROJECT_START_DATE_COLUMNS)} 个字段")
        print(f"[信息] 预计处理 {local_count} 条记录，批次大小: {batch_size}")

        # 分批处理数据
        offset = 0
        total_inserted = 0

        while offset < local_count:
            # 从本地数据库读取一批数据
            select_sql = f"""
            SELECT {columns_str}
            FROM {table_name}
            LIMIT {batch_size} OFFSET {offset}
            """
            
            local_cursor.execute(select_sql)
            batch_data = local_cursor.fetchall()
            
            if not batch_data:
                break

            # 插入到远程数据库
            insert_sql = f"""
            INSERT INTO {table_name} ({columns_str})
            VALUES ({placeholders})
            """
            
            remote_cursor.executemany(insert_sql, batch_data)
            remote_conn.commit()
            
            batch_inserted = len(batch_data)
            total_inserted += batch_inserted
            offset += batch_size
            
            # 计算进度和预估剩余时间
            progress = min(total_inserted / local_count * 100, 100)
            elapsed_time = time.time() - start_time
            if total_inserted > 0:
                estimated_total_time = elapsed_time * local_count / total_inserted
                remaining_time = max(estimated_total_time - elapsed_time, 0)
                print(f"[进度] {total_inserted}/{local_count} ({progress:.1f}%) - "
                      f"已用时: {elapsed_time:.1f}s, 预计剩余: {remaining_time:.1f}s")

        local_cursor.close()
        remote_cursor.close()
        
        total_time = time.time() - start_time
        print(f"[成功] 数据复制完成! 共复制 {total_inserted} 条记录")
        print(f"[统计] 总耗时: {total_time:.2f}秒, 平均速度: {total_inserted/total_time:.1f} 条/秒")
        
        return True
    except Exception as e:
        print(f"[错误] 数据复制失败: {e}")
        print(traceback.format_exc())
        return False

def validate_sync_results(local_conn, remote_conn, table_name):
    """验证同步结果"""
    try:
        print(f"[信息] 正在验证同步结果...")
        
        # 比较记录数
        local_count = get_record_count(local_conn, table_name, "本地")
        remote_count = get_record_count(remote_conn, table_name, "远程")
        
        if local_count == remote_count:
            print(f"[验证] ✅ 记录数匹配: {local_count} 条")
        else:
            print(f"[验证] ❌ 记录数不匹配: 本地={local_count}, 远程={remote_count}")
            return False
        
        # 抽样验证数据内容（验证前5条记录）
        local_cursor = local_conn.cursor()
        remote_cursor = remote_conn.cursor()
        
        # 获取本地前5条记录的关键字段
        local_cursor.execute(f"""
        SELECT `项目编码`, `开工时间`, `查询日期`
        FROM {table_name}
        LIMIT 5
        """)
        local_samples = local_cursor.fetchall()
        
        # 获取远程前5条记录的关键字段
        remote_cursor.execute(f"""
        SELECT `项目编码`, `开工时间`, `查询日期`
        FROM {table_name}
        LIMIT 5
        """)
        remote_samples = remote_cursor.fetchall()
        
        local_cursor.close()
        remote_cursor.close()
        
        # 比较样本数据
        if local_samples == remote_samples:
            print(f"[验证] ✅ 抽样数据匹配")
            return True
        else:
            print(f"[验证] ❌ 抽样数据不匹配")
            return False
            
    except Exception as e:
        print(f"[错误] 验证同步结果失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 80)
    print("DICT_project_start_date_local2remote.py - 本地project_start_date表到远程数据库同步")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)

    # 连接本地数据库
    print("\n1. 连接本地数据库...")
    local_conn = connect_to_database(LOCAL_DB_CONFIG, "本地数据库")
    if not local_conn:
        print("[错误] 无法连接本地数据库，程序退出")
        sys.exit(1)

    # 连接远程数据库
    print("\n2. 连接远程数据库...")
    remote_conn = connect_to_database(REMOTE_DB_CONFIG, "远程数据库")
    if not remote_conn:
        print("[错误] 无法连接远程数据库，程序退出")
        local_conn.close()
        sys.exit(1)

    try:
        # 检查本地表是否存在
        print(f"\n3. 检查本地表 {TABLE_NAME}...")
        if not test_table_exists(local_conn, TABLE_NAME, "本地数据库"):
            print(f"[错误] 本地数据库中不存在表 {TABLE_NAME}")
            return False

        # 检查远程表是否存在，如果不存在则创建
        print(f"\n4. 检查远程表 {TABLE_NAME}...")
        if not test_table_exists(remote_conn, TABLE_NAME, "远程数据库"):
            print(f"[信息] 远程数据库中不存在表 {TABLE_NAME}，正在创建...")
            if not create_remote_table_if_not_exists(remote_conn):
                print("[错误] 创建远程表失败")
                return False
        else:
            print(f"[信息] 远程表 {TABLE_NAME} 已存在")

        # 清空远程表
        print(f"\n5. 清空远程表 {TABLE_NAME}...")
        if not truncate_remote_table(remote_conn, TABLE_NAME):
            print("[错误] 清空远程表失败")
            return False

        # 复制数据
        print(f"\n6. 从本地复制数据到远程...")
        if not copy_data_from_local(local_conn, remote_conn, TABLE_NAME):
            print("[错误] 数据复制失败")
            return False

        # 验证同步结果
        print(f"\n7. 验证同步结果...")
        if validate_sync_results(local_conn, remote_conn, TABLE_NAME):
            print("[验证] ✅ 同步结果验证通过")
        else:
            print("[验证] ❌ 同步结果验证未通过，请检查数据")

        print("\n" + "=" * 80)
        print("✅ project_start_date表数据同步完成!")
        print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        return True

    except Exception as e:
        print(f"\n[错误] 程序执行过程中出现异常: {e}")
        print(traceback.format_exc())
        return False
    finally:
        # 关闭数据库连接
        if local_conn:
            local_conn.close()
            print("[信息] 本地数据库连接已关闭")
        if remote_conn:
            remote_conn.close()
            print("[信息] 远程数据库连接已关闭")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
