#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试数据库连接和表创建
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'dict_romte'))
from dict_romte.config import get_db_config
import pymysql

def test_db_connection():
    """测试数据库连接"""
    DB_CONFIG = get_db_config('default')
    print('数据库配置:', DB_CONFIG)

    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SHOW TABLES LIKE 'dict_zonghe_%'")
        tables = cursor.fetchall()
        print(f'找到 {len(tables)} 个相关表:')
        for table in tables:
            print(f'  - {table[0]}')
        
        # 检查项目ID来源
        try:
            cursor.execute("SELECT COUNT(*) FROM v_distinct_project_id")
            count = cursor.fetchone()[0]
            print(f'v_distinct_project_id视图中有 {count} 个项目ID')
        except Exception as e:
            print(f'v_distinct_project_id视图不存在: {e}')
            try:
                cursor.execute("SELECT COUNT(DISTINCT `项目编码`) FROM sign_data_detail WHERE `项目编码` IS NOT NULL AND `项目编码` != ''")
                count = cursor.fetchone()[0]
                print(f'sign_data_detail表中有 {count} 个不同的项目编码')
            except Exception as e2:
                print(f'sign_data_detail表也不存在: {e2}')
        
        cursor.close()
        conn.close()
        print('数据库连接测试成功')
        return True
    except Exception as e:
        print(f'数据库连接失败: {e}')
        return False

if __name__ == "__main__":
    test_db_connection()
