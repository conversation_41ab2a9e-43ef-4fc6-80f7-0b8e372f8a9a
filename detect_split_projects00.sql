-- 创建临时表存储可能的拆分立项项目对
CREATE TEMPORARY TABLE potential_split_projects AS
SELECT 
    t1.`项目名称` AS project1,
    t2.`项目名称` AS project2,
    t1.`客户名称` AS customer,
    t1.`签约时间` AS sign_date1,
    t2.`签约时间` AS sign_date2,
    ABS(DATEDIFF(STR_TO_DATE(t1.`签约时间`, '%Y/%m/%d'), 
                STR_TO_DATE(t2.`签约时间`, '%Y/%m/%d'))) AS date_diff
FROM 
    BB t1
JOIN 
    BB t2 ON t1.`客户名称` = t2.`客户名称` 
        AND t1.`项目名称` <> t2.`项目名称`
WHERE 
    -- 签约时间相近（1个月内）
    ABS(DATEDIFF(STR_TO_DATE(t1.`签约时间`, '%Y/%m/%d'), 
                STR_TO_DATE(t2.`签约时间`, '%Y/%m/%d'))) <= 30;

-- 查询结果：找出客户相同、签约时间相近且建设内容相似的项目对
SELECT 
    p.project1,
    p.project2,
    p.customer AS `收入侧客户`,
    p.sign_date1 AS `项目1签约时间`,
    p.sign_date2 AS `项目2签约时间`,
    p.date_diff AS `签约时间差(天)`,
    t1.`项目建设内容及方案简介（CT）` AS `项目1_CT内容`,
    t2.`项目建设内容及方案简介（CT）` AS `项目2_CT内容`,
    t1.`项目建设内容及方案简介（IT）` AS `项目1_IT内容`,
    t2.`项目建设内容及方案简介（IT）` AS `项目2_IT内容`,
    CASE 
        WHEN (
            -- 使用LIKE进行简单的内容相似度判断
            (t1.`项目建设内容及方案简介（CT）` LIKE CONCAT('%', SUBSTRING(t2.`项目建设内容及方案简介（CT）`, 1, 20), '%') OR
             t2.`项目建设内容及方案简介（CT）` LIKE CONCAT('%', SUBSTRING(t1.`项目建设内容及方案简介（CT）`, 1, 20), '%'))
            OR
            (t1.`项目建设内容及方案简介（IT）` LIKE CONCAT('%', SUBSTRING(t2.`项目建设内容及方案简介（IT）`, 1, 20), '%') OR
             t2.`项目建设内容及方案简介（IT）` LIKE CONCAT('%', SUBSTRING(t1.`项目建设内容及方案简介（IT）`, 1, 20), '%'))
        ) THEN '是'
        ELSE '否'
    END AS `疑似拆分立项`
FROM 
    potential_split_projects p
JOIN 
    BB t1 ON p.project1 = t1.`项目名称`
JOIN 
    BB t2 ON p.project2 = t2.`项目名称`
WHERE
    -- 过滤出建设内容相似的项目
    (t1.`项目建设内容及方案简介（CT）` LIKE CONCAT('%', SUBSTRING(t2.`项目建设内容及方案简介（CT）`, 1, 20), '%') OR
     t2.`项目建设内容及方案简介（CT）` LIKE CONCAT('%', SUBSTRING(t1.`项目建设内容及方案简介（CT）`, 1, 20), '%'))
    OR
    (t1.`项目建设内容及方案简介（IT）` LIKE CONCAT('%', SUBSTRING(t2.`项目建设内容及方案简介（IT）`, 1, 20), '%') OR
     t2.`项目建设内容及方案简介（IT）` LIKE CONCAT('%', SUBSTRING(t1.`项目建设内容及方案简介（IT）`, 1, 20), '%'))
ORDER BY 
    p.customer, p.date_diff;

-- 删除临时表
DROP TEMPORARY TABLE IF EXISTS potential_split_projects;