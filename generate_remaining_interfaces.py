#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
批量生成剩余接口的数据表和爬虫程序
"""

import os

# 剩余接口配置
REMAINING_INTERFACES = [
    {
        'name': 'queryProjectPlanWithImplement',
        'chinese_name': '查询项目计划及实施情况',
        'api_path': 'saleCenterApp//preparation/queryProjectPlanWithImplement',
        'table_name': 'dict_zonghe_queryProjectPlanWithImplement',
        'params': ['LOGIN_NO', 'PROJECT_ID'],
        'key_fields': [
            'IMPLEMENT_PLAN_ID', 'PLAN_ID', 'PLAN_TYPE', 'PLAN_NAME', 'PLAN_DESC',
            'PLAN_DEAL_STAFF', 'PLAN_DEAL_NAME', 'REQUIRE_START_DATE', 'REQUIRE_FINISH_DATE',
            'IMPLEMENT_STATUS', 'IMPLEMENT_STATUS_DESC', 'ACT_FINSH_TIME', 'PROGRESS_RATE',
            'DELIVERY_TYPE', 'DELIVERY_TYPE_DESC', 'CREATE_DATE', 'PLAN_TYPE_DESC',
            'PLAN_LEVEL', 'PARENT_IMPLEMENT_PLAN_ID', 'STAFF_TYPE', 'PLAN_MODE',
            'SUBJECT_TYPE', 'SUBJECT_TYPE_DESC'
        ]
    },
    {
        'name': 'queryProjectAmount',
        'chinese_name': '查询项目金额信息',
        'api_path': 'saleCenterApp/projectAmount/queryProjectAmount',
        'table_name': 'dict_zonghe_queryProjectAmount',
        'params': ['LOGIN_NO', 'PROJECT_ID', 'PROJECT_STAGE'],
        'key_fields': [
            'AMOUNT_ID', 'PROJECT_ID', 'BENEFIT_TYPE', 'INC_CON_AMOUNT', 'CT_AMOUNT',
            'IT_CISERV_AMOUNT', 'PLATFORM9ONE_AMOUNT', 'CT_PNET5G_AMOUNT', 'IT_PNET5G_AMOUNT',
            'MOBILE_CLOUD_AMOUNT', 'MOBILE_CLOUD_IP_AMOUNT', 'IDC_AMOUNT', 'LINE_AMOUNT',
            'IOT_AMOUNT', 'STATUS_CD', 'STATUS_DATE', 'CREATE_STAFF', 'CREATE_NAME',
            'CREATE_DATE', 'UPDATE_STAFF', 'UPDATE_DATE', 'IT_AMOUNT', 'TD_AMOUNT',
            'JG_AMOUNT', 'LL_AMOUNT', 'RZ_AMOUNT', 'MC_AMOUNT', 'SALE_AMOUNT',
            'VN_AMOUNT', 'IB_AMOUNT', 'AI_AMOUNT'
        ]
    },
    {
        'name': 'queryProgram',
        'chinese_name': '查询项目方案信息',
        'api_path': 'saleCenterApp/formulation/queryProgram',
        'table_name': 'dict_zonghe_queryProgram',
        'params': ['LOGIN_NO', 'PROJECT_ID'],
        'key_fields': [
            'TRADE', 'TRADE_DESC', 'FIRST_SCENE', 'FIRST_SCENE_DESC', 'SECOND_SCENE',
            'SECOND_SCENE_DESC', 'SOLUTION_DETAIL_LIST', 'BUSINESS_MODEL_LIST',
            'COOPERATION_MODE_LIST'
        ]
    },
    {
        'name': 'qryIncomeProgressByProject',
        'chinese_name': '按项目查询收入进度',
        'api_path': 'saleCenterApp/incomeManage/qryIncomeProgressByProject',
        'table_name': 'dict_zonghe_qryIncomeProgressByProject',
        'params': ['LOGIN_NO', 'PROJECT_ID', 'PROJECT_STAGE'],
        'key_fields': [
            'EXPENSE_DETAIL_ID', 'EXPENSE_ID', 'PROD_ID', 'PROD_NAME', 'CT_OR_IT',
            'SUBJECT_CODE', 'SUBJECT_NAME', 'SUBJECT_CLASS', 'IS_FLAT_RATE', 'VAT_RATE',
            'CYCLE_TYPE', 'CYCLE_COUNT', 'COLL_CYCLE_TYPE', 'COLL_CYCLE_COUNT', 'MONEY',
            'VAT', 'MONEY_EX_TAX', 'PLAN_BILL_START_TIME', 'PLAN_COLL_START_TIME',
            'ACT_BILL_START_TIME', 'BILL_AMOUNT', 'BILL_AMOUNT_TOTAL_VAT', 'COLLECTION_AMOUNT'
        ]
    }
]

def generate_sql_file(interface):
    """生成SQL建表文件"""
    sql_content = f"""-- 创建综合查询{interface['chinese_name']}表
-- 对应接口：{interface['api_path']}

DROP TABLE IF EXISTS `{interface['table_name']}`;

CREATE TABLE IF NOT EXISTS `{interface['table_name']}` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  
  -- 入参字段（来源于源数据表）
  `INPUT_LOGIN_NO` varchar(100) DEFAULT NULL COMMENT '入参-登录用户名',
  `INPUT_PROJECT_ID` varchar(100) DEFAULT NULL COMMENT '入参-项目ID（来源：v_distinct_project_id视图或sign_data_detail表项目编码字段）',
"""
    
    # 添加额外的入参字段
    if 'PROJECT_STAGE' in interface['params']:
        sql_content += "  `INPUT_PROJECT_STAGE` varchar(50) DEFAULT NULL COMMENT '入参-项目阶段',\n"
    
    sql_content += """  
  -- 响应数据字段（基于接口返回的JSON结构）
"""
    
    # 添加关键字段
    for field in interface['key_fields']:
        if 'AMOUNT' in field or 'MONEY' in field:
            sql_content += f"  `{field}` decimal(15,2) DEFAULT NULL COMMENT '{field}',\n"
        elif 'DATE' in field or 'TIME' in field:
            sql_content += f"  `{field}` varchar(50) DEFAULT NULL COMMENT '{field}',\n"
        elif 'DESC' in field or 'NAME' in field or 'CONTENT' in field:
            sql_content += f"  `{field}` text DEFAULT NULL COMMENT '{field}',\n"
        else:
            sql_content += f"  `{field}` varchar(200) DEFAULT NULL COMMENT '{field}',\n"
    
    sql_content += """  
  -- 系统字段
  `RUN_IP` varchar(50) DEFAULT NULL COMMENT '运行IP',
  `REQUEST_ID` varchar(100) DEFAULT NULL COMMENT '请求ID',
  `RETURN_CODE` varchar(10) DEFAULT NULL COMMENT '返回码',
  `RETURN_MSG` varchar(200) DEFAULT NULL COMMENT '返回消息',
  `USER_MSG` varchar(200) DEFAULT NULL COMMENT '用户消息',
  `DETAIL_MSG` text DEFAULT NULL COMMENT '详细消息',
  
  -- 元数据字段
  `import_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_input_project_id` (`INPUT_PROJECT_ID`),
  KEY `idx_import_time` (`import_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='综合查询{interface['chinese_name']}表';
"""
    
    filename = f"create_{interface['table_name']}_table.sql"
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(sql_content)
    
    print(f"[生成] {filename}")

def generate_python_file(interface):
    """生成Python爬虫文件"""
    python_content = f'''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
综合查询{interface['chinese_name']}爬虫程序
对应接口：{interface['api_path']}
功能：从dict系统获取{interface['chinese_name']}并同步到MySQL数据库
"""

import sys
import os
import json
import time
import requests
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
import pymysql
from datetime import datetime

# 添加配置文件路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'dict_romte'))
from dict_romte.config import get_db_config, get_login_credentials

# 数据库配置
DB_CONFIG = get_db_config('default')

# API配置
API_URL = "https://dict.gmcc.net:30722/dictWeb/gatewayService/{interface['api_path']}"

def load_cookies():
    """从cookies.txt文件加载cookie"""
    try:
        with open('cookies.txt', 'r', encoding='utf-8') as f:
            cookie_data = json.load(f)
        
        cookies = {{}}
        for cookie in cookie_data.get('cookies', []):
            cookies[cookie['name']] = cookie['value']
        
        print(f"[信息] 成功加载 {{len(cookies)}} 个Cookie")
        return cookies
    except Exception as e:
        print(f"[错误] 加载Cookie失败: {{e}}")
        return {{}}

def get_project_ids():
    """从v_distinct_project_id视图获取项目ID列表"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 查询v_distinct_project_id视图
        cursor.execute("SELECT project_id FROM v_distinct_project_id")
        project_ids = [row[0] for row in cursor.fetchall()]
        
        print(f"[信息] 从v_distinct_project_id视图获取到 {{len(project_ids)}} 个项目ID")
        return project_ids
        
    except Exception as e:
        print(f"[错误] 获取项目ID失败: {{e}}")
        # 如果视图不存在，尝试从其他表获取
        try:
            cursor.execute("SELECT DISTINCT `项目编码` FROM sign_data_detail WHERE `项目编码` IS NOT NULL AND `项目编码` != ''")
            project_ids = [row[0] for row in cursor.fetchall()]
            print(f"[信息] 从sign_data_detail表获取到 {{len(project_ids)}} 个项目ID")
            return project_ids
        except Exception as e2:
            print(f"[错误] 从备用表获取项目ID也失败: {{e2}}")
            return []
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def build_request_headers(cookies):
    """构建请求头"""
    cookie_str = "; ".join([f"{{name}}={{value}}" for name, value in cookies.items()])
    
    headers = {{
        'Accept': 'application/json, text/plain, */*',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json',
        'Host': 'dict.gmcc.net:30722',
        'Origin': 'https://dict.gmcc.net:30722',
        'Pragma': 'no-cache',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Cookie': cookie_str
    }}
    return headers

def build_request_data(project_id, login_no):
    """构建请求数据"""
    body = {{"PROJECT_ID": project_id}}
    '''
    
    # 添加额外参数
    if 'PROJECT_STAGE' in interface['params']:
        python_content += '''
    body["PROJECT_STAGE"] = "1001"  # 默认项目阶段'''
    
    python_content += f'''
    
    return {{
        "ROOT": {{
            "HEADER": {{
                "OPR_INFO": {{
                    "LOGIN_NO": login_no
                }}
            }},
            "BODY": body
        }}
    }}

def query_{interface['name'].lower()}(project_id, cookies, login_no):
    """查询单个项目的{interface['chinese_name']}"""
    try:
        headers = build_request_headers(cookies)
        data = build_request_data(project_id, login_no)
        
        response = requests.post(API_URL, headers=headers, json=data, timeout=30, verify=False)
        
        if response.status_code == 200:
            result = response.json()
            
            # 检查返回码
            return_code = result.get('ROOT', {{}}).get('BODY', {{}}).get('RETURN_CODE', '')
            if return_code == '0':
                return result.get('ROOT', {{}}).get('BODY', {{}}).get('OUT_DATA', {{}})
            else:
                error_msg = result.get('ROOT', {{}}).get('BODY', {{}}).get('RETURN_MSG', '未知错误')
                print(f"[警告] 项目 {{project_id}} 查询失败: {{error_msg}}")
                return None
        else:
            print(f"[错误] 项目 {{project_id}} HTTP请求失败: {{response.status_code}}")
            return None
            
    except Exception as e:
        print(f"[错误] 项目 {{project_id}} 查询异常: {{e}}")
        return None

def main():
    """主函数"""
    print("=" * 60)
    print("综合查询{interface['chinese_name']}爬虫程序启动")
    print("=" * 60)
    
    # 加载Cookie
    cookies = load_cookies()
    if not cookies:
        print("[错误] Cookie加载失败，请先运行 login2cookie.py 获取Cookie")
        return
    
    # 获取登录用户名
    login_no, _, _ = get_login_credentials()
    
    # 获取项目ID列表
    project_ids = get_project_ids()
    if not project_ids:
        print("[错误] 未获取到项目ID列表")
        return
    
    print(f"[信息] 开始处理 {{len(project_ids)}} 个项目")
    
    success_count = 0
    error_count = 0
    
    for i, project_id in enumerate(project_ids, 1):
        print(f"[进度] {{i}}/{{len(project_ids)}} 处理项目: {{project_id}}")
        
        # 查询项目信息
        result = query_{interface['name'].lower()}(project_id, cookies, login_no)
        
        if result:
            success_count += 1
            print(f"  成功获取数据")
        else:
            error_count += 1
        
        # 每处理10个项目休息一下
        if i % 10 == 0:
            time.sleep(1)
    
    print(f"\\n[统计] 成功: {{success_count}}, 失败: {{error_count}}")
    
    print("=" * 60)
    print("综合查询{interface['chinese_name']}爬虫程序完成")
    print("=" * 60)

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "-all":
        main()
    else:
        print("使用方法: python {interface['table_name']}.py -all")
        print("说明: -all 参数表示轮询所有项目数据同步入库")'''
    
    filename = f"{interface['table_name']}.py"
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(python_content)
    
    print(f"[生成] {filename}")

def main():
    """主函数"""
    print("=" * 60)
    print("批量生成剩余接口的数据表和爬虫程序")
    print("=" * 60)
    
    for interface in REMAINING_INTERFACES:
        print(f"\\n正在生成接口: {interface['chinese_name']}")
        generate_sql_file(interface)
        generate_python_file(interface)
    
    print("\\n=" * 60)
    print("批量生成完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
