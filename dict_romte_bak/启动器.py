#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
启动器.py - 智能启动器
自动设置编码并运行主程序，支持多种运行模式
"""

import os
import sys
import subprocess
import argparse
from datetime import datetime

def setup_encoding():
    """设置UTF-8编码"""
    if sys.platform.startswith('win'):
        try:
            os.system('chcp 65001 >nul 2>&1')
            os.environ['PYTHONIOENCODING'] = 'utf-8'
            os.environ['LANG'] = 'zh_CN.UTF-8'
            if hasattr(sys.stdout, 'reconfigure'):
                sys.stdout.reconfigure(encoding='utf-8')
                sys.stderr.reconfigure(encoding='utf-8')
            print("✅ UTF-8编码设置成功")
            return True
        except Exception as e:
            print(f"❌ 设置编码失败: {e}")
            return False
    return True

def run_script(script_name, wait_for_completion=True):
    """运行指定的Python脚本"""
    if not os.path.exists(script_name):
        print(f"❌ 脚本文件不存在: {script_name}")
        return False
    
    print(f"🚀 启动脚本: {script_name}")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        env['LANG'] = 'zh_CN.UTF-8'
        
        if wait_for_completion:
            # 等待完成
            result = subprocess.run([sys.executable, script_name], 
                                  env=env, 
                                  cwd=os.path.dirname(os.path.abspath(__file__)))
            
            print("=" * 60)
            print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            if result.returncode == 0:
                print("✅ 脚本执行成功")
                return True
            else:
                print(f"❌ 脚本执行失败，退出码: {result.returncode}")
                return False
        else:
            # 后台运行
            subprocess.Popen([sys.executable, script_name], 
                           env=env, 
                           cwd=os.path.dirname(os.path.abspath(__file__)))
            print("🔄 脚本已在后台启动")
            return True
            
    except Exception as e:
        print(f"❌ 运行脚本时出错: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='智能启动器 - 自动设置编码并运行Python脚本')
    parser.add_argument('script', nargs='?', default='main_dict_local.py', 
                       help='要运行的脚本名称 (默认: main_dict_local.py)')
    parser.add_argument('--background', '-b', action='store_true', 
                       help='在后台运行脚本')
    parser.add_argument('--list', '-l', action='store_true', 
                       help='列出可用的Python脚本')
    
    args = parser.parse_args()
    
    # 设置编码
    setup_encoding()
    
    if args.list:
        # 列出可用脚本
        print("📋 可用的Python脚本:")
        for file in os.listdir('.'):
            if file.endswith('.py') and file != '启动器.py':
                print(f"  - {file}")
        return
    
    # 运行脚本
    success = run_script(args.script, not args.background)
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
