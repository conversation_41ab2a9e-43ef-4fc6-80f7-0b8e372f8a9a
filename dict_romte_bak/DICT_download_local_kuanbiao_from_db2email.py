#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 自动设置UTF-8编码，解决中文乱码问题
try:
    import utf8_setup
except ImportError:
    # 如果utf8_setup模块不存在，使用内置设置
    import sys
    if sys.platform.startswith('win'):
        import os as _os
        _os.system('chcp 65001 >nul 2>&1')
        _os.environ['PYTHONIOENCODING'] = 'utf-8'

import os
import pandas as pd
import pymysql
import smtplib
from datetime import datetime
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication

# 配置信息
# 数据库配置
DB_HOST = "localhost"
DB_USER = "root"
DB_PASSWORD = "5eb9a11916e3a66d"  # 请替换为您的数据库密码
DB_NAME = "dict_spider"    # 请替换为您的数据库名称
DB_PORT = 12136

# 'host': '*************',
# 'port': 12136,
# 'user': 'root',
# 'password': '5eb9a11916e3a66d',
# 'database': 'dict_spider',
# 'charset': 'utf8mb4'


# 126邮箱配置
EMAIL = "<EMAIL>"
PASSWORD = "XBbyfQEf2PpqUif5"  # 授权密码
SMTP_SERVER = "smtp.126.com"
SMTP_PORT = 465

# 收件人
RECIPIENT = "<EMAIL>"  # 请替换为您的收件人邮箱
    # "<EMAIL>,<EMAIL>"

def download_kuanbiao_data():
    """从数据库下载宽表视图数据"""
    today = datetime.now().strftime("%Y%m%d")
    filename = f"宽表{today}.xlsx"
    
    print(f"[信息] 开始从数据库下载宽表数据 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 连接数据库
        print("[信息] 正在连接数据库...")
        conn = pymysql.connect(
            host=DB_HOST,
            user=DB_USER,
            password=DB_PASSWORD,
            database=DB_NAME,
            port=DB_PORT
        )
        
        # 执行查询
        print("[信息] 正在查询宽表视图数据...")
        query = "SELECT * FROM t_kuanbiao "     # 请替换为实际的视图名称
        df = pd.read_sql(query, conn)
        
        # 关闭数据库连接
        conn.close()
        
        # 保存为Excel文件
        df.to_excel(filename, index=False)
        print(f"[成功] 宽表数据已保存为 {filename}")
        
        return filename
    except Exception as e:
        print(f"[错误] 下载宽表数据失败: {e}")
        import traceback
        print(traceback.format_exc())
        return None

def send_email_with_attachment(attachment_path):
    """发送带附件的邮件"""
    today = datetime.now().strftime("%Y%m%d")
    subject = f"宽表明细{today}"
    
    print(f"[信息] 开始发送邮件 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 创建邮件对象
        msg = MIMEMultipart()
        msg['From'] = EMAIL
        msg['To'] = RECIPIENT
        msg['Subject'] = subject
        
        # 添加邮件正文
        body = f"附件为{today}宽表数据，请查收。\n\n此邮件由系统自动发送，请勿回复。"
        msg.attach(MIMEText(body, 'plain'))
        
        # 添加附件
        with open(attachment_path, 'rb') as file:
            attachment = MIMEApplication(file.read())
            attachment.add_header('Content-Disposition', 'attachment', filename=os.path.basename(attachment_path))
            msg.attach(attachment)
        
        # 连接到SMTP服务器并发送邮件
        print(f"[信息] 正在连接到SMTP服务器: {SMTP_SERVER}...")
        server = smtplib.SMTP_SSL(SMTP_SERVER, SMTP_PORT)
        server.login(EMAIL, PASSWORD)
        
        print(f"[信息] 正在发送邮件到: {RECIPIENT}...")
        server.send_message(msg)
        server.quit()
        
        print(f"[成功] 邮件已发送到: {RECIPIENT}")
        return True
    except Exception as e:
        print(f"[错误] 发送邮件失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def main():
    print(f"[开始] 开始从数据库下载宽表数据并发送邮件 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 下载宽表数据
    attachment_path = download_kuanbiao_data()
    
    if attachment_path and os.path.exists(attachment_path):
        # 发送邮件
        success = send_email_with_attachment(attachment_path)

        if success:
            print("[完成] 任务完成：宽表数据已下载并发送")
        else:
            print("[失败] 任务失败：邮件发送失败")
    else:
        print("[失败] 任务失败：宽表数据下载失败")

if __name__ == "__main__":
    main()