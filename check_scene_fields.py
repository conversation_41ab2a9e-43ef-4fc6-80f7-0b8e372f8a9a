#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查场景相关字段和成本侧供应商数据
"""

import pymysql

DB_CONFIG = {
    'host': '*************',
    'port': 12136,
    'user': 'root',
    'password': '5eb9a11916e3a66d',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

try:
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    print('=== 查找包含场景的字段 ===')
    cursor.execute('SHOW COLUMNS FROM t_kuanbiao')
    columns = cursor.fetchall()
    
    scene_fields = []
    for col in columns:
        field_name = col[0]
        if '场景' in field_name or '行业' in field_name or '类型' in field_name or '分类' in field_name:
            scene_fields.append(field_name)
    
    if scene_fields:
        print('找到相关字段:')
        for field in scene_fields:
            print(f'  - {field}')
    else:
        print('未找到包含场景的字段')
    
    # 检查成本侧供应商字段的数据情况
    print('\n=== 检查成本侧供应商数据 ===')
    cursor.execute('SELECT COUNT(*) FROM t_kuanbiao WHERE 成本侧供应商 IS NOT NULL AND 成本侧供应商 != ""')
    supplier_count = cursor.fetchone()[0]
    print(f'有成本侧供应商的记录数: {supplier_count}')
    
    if supplier_count > 0:
        cursor.execute('SELECT 项目编码, 收入侧客户, 成本侧供应商 FROM t_kuanbiao WHERE 成本侧供应商 IS NOT NULL AND 成本侧供应商 != "" LIMIT 5')
        results = cursor.fetchall()
        print('\n样本数据:')
        for row in results:
            print(f'  项目: {row[0]}, 客户: {row[1]}, 供应商: {row[2]}')
    
    # 检查项目建设内容字段
    print('\n=== 检查项目建设内容字段 ===')
    cursor.execute('SELECT COUNT(*) FROM t_kuanbiao WHERE `项目建设内容及方案简介（CT）` IS NOT NULL AND `项目建设内容及方案简介（CT）` != ""')
    ct_count = cursor.fetchone()[0]
    print(f'有CT内容的记录数: {ct_count}')
    
    cursor.execute('SELECT COUNT(*) FROM t_kuanbiao WHERE `项目建设内容及方案简介（IT）` IS NOT NULL AND `项目建设内容及方案简介（IT）` != ""')
    it_count = cursor.fetchone()[0]
    print(f'有IT内容的记录数: {it_count}')
    
    if ct_count > 0:
        cursor.execute('SELECT 项目编码, `项目建设内容及方案简介（CT）` FROM t_kuanbiao WHERE `项目建设内容及方案简介（CT）` IS NOT NULL AND `项目建设内容及方案简介（CT）` != "" LIMIT 3')
        results = cursor.fetchall()
        print('\nCT内容样本:')
        for row in results:
            ct_content = row[1][:100] + '...' if len(row[1]) > 100 else row[1]
            print(f'  项目: {row[0]}')
            print(f'  CT内容: {ct_content}')
    
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f'错误: {e}')
