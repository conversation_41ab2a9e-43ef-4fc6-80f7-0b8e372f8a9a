import os
import pandas as pd
from sqlalchemy import create_engine
import pymysql
from datetime import datetime
import re
import shutil

# 目标文件夹
TARGET_FOLDER = "shichang"
IMPORTED_FOLDER = os.path.join(TARGET_FOLDER, "inserted2db")

# 数据库配置0
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'cmcc12345',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

def ensure_folder_exists(folder_path):
    """确保目标文件夹存在"""
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)
        print(f"[信息] 创建目标文件夹: {folder_path}")
    return folder_path

def connect_to_database():
    """连接到MySQL数据库"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        print(f"[成功] 已连接到数据库: {DB_CONFIG['database']}")
        return conn
    except Exception as e:
        print(f"[错误] 连接数据库失败: {e}")
        exit(1)

def create_tables_if_not_exist(conn):
    """创建必要的数据表（如果不存在）"""
    cursor = conn.cursor()
    try:
        # 创建BC融合业务工号级数据表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS bc_channeng_gonghao (
            id INT AUTO_INCREMENT PRIMARY KEY,
            员工编号 VARCHAR(50),
            姓名 VARCHAR(50),
            部门_分公司 VARCHAR(100),
            网格 VARCHAR(50),
            BOSS工号 VARCHAR(50),
            日办理量_拉新 INT,
            日办理量_其中_新证 INT,
            日办理量_其中_集团属性 INT,
            日办理量_其中_拉新集团社保_鉴真 INT,
            日办理量_其中_融宽 INT,
            日办理量_其中_完美一单 INT,
            日办理量_其中_XR INT,
            日办理量_合约_精准流量扩容 INT,
            日办理量_合约_宽带流量扩容 INT,
            日办理量_其中_合约_鉴真 INT,
            日办理量_云电脑 INT,
            日办理量_其中云电脑_鉴真 INT,
            日办理量_宽带 INT,
            日办理量_其中_宽带_鉴真 INT,
            日办理量_FTTR INT,
            日办理量_其中_FTTR_鉴真 INT,
            日办理量_合计 INT,
            当日产能_看管集团鉴真_总产能 INT,
            月累计_拉新 INT,
            月累计_其中_新证 INT,
            月累计_其中_集团属性 INT,
            月累计_其中_拉新集团社保_鉴真 INT,
            月累计_其中_融宽 INT,
            月累计_其中_完美一单 INT,
            月累计_其中_XR INT,
            月累计_合约_精准流量扩容 INT,
            月累计_合约_宽带流量扩容 INT,
            月累计_其中_合约_鉴真 INT,
            月累计_云电脑 INT,
            月累计_其中云电脑_鉴真 INT,
            月累计_宽带 INT,
            月累计_其中_宽带_鉴真 INT,
            月累计_FTTR INT,
            月累计_其中_FTTR_鉴真 INT,
            月累计_合计 INT,
            月累计产能_看管集团鉴真_总产能 INT,
            周办理量_拉新 INT,
            周办理量_其中_新证 INT,
            周办理量_其中_集团属性 INT,
            周办理量_其中_拉新集团社保_鉴真 INT,
            周办理量_其中_融宽 INT,
            周办理量_其中_完美一单 INT,
            周办理量_其中_XR INT,
            周办理量_合约_精准流量扩容 INT,
            周办理量_合约_宽带流量扩容 INT,
            周办理量_其中_合约_鉴真 INT,
            周办理量_云电脑 INT,
            周办理量_其中云电脑_鉴真 INT,
            周办理量_宽带 INT,
            周办理量_其中_宽带_鉴真 INT,
            周办理量_FTTR INT,
            周办理量_其中_FTTR_鉴真 INT,
            周办理量_合计 INT,
            周累计产能_看管集团鉴真_总产能 INT,
            是否破零 VARCHAR(10),
            是否达标_20笔 VARCHAR(10),
            是否达标_30笔 VARCHAR(10),
            是否达标_40笔 VARCHAR(10),
            月份 VARCHAR(10),
            日期 DATE,
            导入时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
            文件名 VARCHAR(255)
        )
        """)

        # 创建导入日志表（如果不存在）
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS import_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            file_name VARCHAR(255),
            table_name VARCHAR(50),
            record_count INT,
            import_time DATETIME
        )
        """)

        conn.commit()
        print("[信息] 已确保所需数据表存在")
    except Exception as e:
        conn.rollback()
        print(f"[错误] 创建数据表失败: {e}")
    finally:
        cursor.close()

def check_file_already_imported(conn, filename):
    """检查文件是否已经被导入过数据库"""
    cursor = conn.cursor()
    try:
        sql = "SELECT COUNT(*) FROM import_logs WHERE file_name = %s"
        cursor.execute(sql, (filename,))
        count = cursor.fetchone()[0]
        return count > 0
    except Exception as e:
        print(f"[警告] 检查文件导入记录失败: {e}")
        return False
    finally:
        cursor.close()

def move_to_imported_folder(filepath, filename):
    """将已导入数据的Excel文件移动到inserted2db文件夹"""
    try:
        # 确保目标文件夹存在
        ensure_folder_exists(IMPORTED_FOLDER)

        # 构建目标路径
        target_path = os.path.join(IMPORTED_FOLDER, filename)

        # 如果目标文件已存在，添加时间戳避免冲突
        if os.path.exists(target_path):
            file_name, file_ext = os.path.splitext(filename)
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            new_filename = f"{file_name}_{timestamp}{file_ext}"
            target_path = os.path.join(IMPORTED_FOLDER, new_filename)

        # 移动文件
        shutil.move(filepath, target_path)
        print(f"[成功] 已将文件 {filename} 移动到 {IMPORTED_FOLDER} 文件夹")
    except Exception as e:
        print(f"[警告] 移动文件 {filename} 失败: {e}")

def import_bc_report_data(conn, filepath, filename):
    """导入BC融合业务日报表数据到数据库"""
    try:
        print(f"[信息] 正在导入BC融合业务日报表数据: {filepath}")
        
        # 检查文件是否已经导入过
        if check_file_already_imported(conn, filename):
            print(f"[信息] 文件 {filename} 已经导入过数据库，跳过导入")
            # 将文件移动到已导入文件夹
            move_to_imported_folder(filepath, filename)
            return True

        # 创建SQLAlchemy引擎
        engine_url = f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?charset={DB_CONFIG['charset']}"
        engine = create_engine(engine_url)

        # 读取Excel文件中的"工号级"sheet
        print("[信息] 正在读取'工号级'sheet...")
        
        # 直接指定第二行（索引为1）作为表头
        df = pd.read_excel(filepath, sheet_name="工号级", header=1)
        
        # 打印当前DataFrame的列，帮助调试
        print("[信息] Excel文件中的列:", df.columns.tolist())
        
        # 定义Excel列名到数据库列名的映射
        column_mapping = {
            '员工编号': '员工编号',
            '姓名': '姓名',
            '部门/分公司': '部门_分公司',
            '网格': '网格',
            'BOSS工号': 'BOSS工号',
            '日办理量_拉新': '日办理量_拉新',
            '日办理量_其中：新证': '日办理量_其中_新证',
            '日办理量_其中：集团属性': '日办理量_其中_集团属性',
            '日办理量_其中：拉新集团社保（鉴真）': '日办理量_其中_拉新集团社保_鉴真',
            '日办理量_其中：融宽': '日办理量_其中_融宽',
            '日办理量_其中：完美一单': '日办理量_其中_完美一单',
            '日办理量_其中：XR': '日办理量_其中_XR',
            '日办理量_合约（精准流量扩容）': '日办理量_合约_精准流量扩容',
            '日办理量_合约（宽带流量扩容）': '日办理量_合约_宽带流量扩容',
            '日办理量_其中：合约（鉴真）': '日办理量_其中_合约_鉴真',
            '日办理量_云电脑': '日办理量_云电脑',
            '日办理量_其中云电脑（鉴真）': '日办理量_其中云电脑_鉴真',
            '日办理量_宽带': '日办理量_宽带',
            '日办理量_其中：宽带（鉴真）': '日办理量_其中_宽带_鉴真',
            '日办理量_FTTR': '日办理量_FTTR',
            '日办理量_其中：FTTR（鉴真）': '日办理量_其中_FTTR_鉴真',
            '日办理量_合计': '日办理量_合计',
            '当日产能_看管集团鉴真-总产能': '当日产能_看管集团鉴真_总产能',
            '月累计_拉新': '月累计_拉新',
            '月累计_其中：新证': '月累计_其中_新证',
            '月累计_其中：集团属性': '月累计_其中_集团属性',
            '月累计_其中：拉新集团社保（鉴真）': '月累计_其中_拉新集团社保_鉴真',
            '月累计_其中：融宽': '月累计_其中_融宽',
            '月累计_其中：完美一单': '月累计_其中_完美一单',
            '月累计_其中：XR': '月累计_其中_XR',
            '月累计_合约（精准流量扩容）': '月累计_合约_精准流量扩容',
            '月累计_合约（宽带流量扩容）': '月累计_合约_宽带流量扩容',
            '月累计_其中：合约（鉴真）': '月累计_其中_合约_鉴真',
            '月累计_云电脑': '月累计_云电脑',
            '月累计_其中云电脑（鉴真）': '月累计_其中云电脑_鉴真',
            '月累计_宽带': '月累计_宽带',
            '月累计_其中：宽带（鉴真）': '月累计_其中_宽带_鉴真',
            '月累计_FTTR': '月累计_FTTR',
            '月累计_其中：FTTR（鉴真）': '月累计_其中_FTTR_鉴真',
            '月累计_合计': '月累计_合计',
            '月累计产能_看管集团鉴真-总产能': '月累计产能_看管集团鉴真_总产能',
            '周办理量_拉新': '周办理量_拉新',
            '周办理量_其中：新证': '周办理量_其中_新证',
            '周办理量_其中：集团属性': '周办理量_其中_集团属性',
            '周办理量_其中：拉新集团社保（鉴真）': '周办理量_其中_拉新集团社保_鉴真',
            '周办理量_其中：融宽': '周办理量_其中_融宽',
            '周办理量_其中：完美一单': '周办理量_其中_完美一单',
            '周办理量_其中：XR': '周办理量_其中_XR',
            '周办理量_合约（精准流量扩容）': '周办理量_合约_精准流量扩容',
            '周办理量_合约（宽带流量扩容）': '周办理量_合约_宽带流量扩容',
            '周办理量_其中：合约（鉴真）': '周办理量_其中_合约_鉴真',
            '周办理量_云电脑': '周办理量_云电脑',
            '周办理量_其中云电脑（鉴真）': '周办理量_其中云电脑_鉴真',
            '周办理量_宽带': '周办理量_宽带',
            '周办理量_其中：宽带（鉴真）': '周办理量_其中_宽带_鉴真',
            '周办理量_FTTR': '周办理量_FTTR',
            '周办理量_其中：FTTR（鉴真）': '周办理量_其中_FTTR_鉴真',
            '周办理量_合计': '周办理量_合计',
            '周累计产能_看管集团鉴真-总产能': '周累计产能_看管集团鉴真_总产能',
            '是否破零': '是否破零',
            '是否达标≥20笔': '是否达标_20笔',
            '是否达标≥30笔': '是否达标_30笔',
            '是否达标≥40笔': '是否达标_40笔'
        }
        
        # 重命名列
        df = df.rename(columns=column_mapping)
        
        # 从文件名中提取日期
        date_match = re.search(r'(\d{8})', filename)
        if date_match:
            file_date = date_match.group(1)
            # 转换为日期格式 YYYY-MM-DD
            file_date = f"{file_date[:4]}-{file_date[4:6]}-{file_date[6:8]}"
            # 添加日期列
            df['日期'] = file_date
            # 提取月份
            df['月份'] = file_date[:7]  # YYYY-MM
        
        # 添加文件名列，用于追踪数据来源
        df['文件名'] = filename
        
        # 添加导入时间列
        import_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        df['导入时间'] = import_time
        
        # 移除空行（所有值都是NaN的行）
        df = df.dropna(how='all')
        
        # 导入数据到bc_channeng_gonghao表
        if len(df) > 0:
            # 将DataFrame中的列名与数据库表的列名进行匹配
            cursor = conn.cursor()
            cursor.execute("DESCRIBE bc_channeng_gonghao")
            table_columns = [column[0] for column in cursor.fetchall()]
            cursor.close()
            
            # 检查是否有不匹配的列
            df_columns = set(df.columns)
            db_columns = set(table_columns)
            
            # 移除DataFrame中存在但数据库中不存在的列
            columns_to_drop = df_columns - db_columns
            if columns_to_drop:
                print(f"[警告] 以下列在Excel中存在但在数据库中不存在，将被忽略: {', '.join(columns_to_drop)}")
                df = df.drop(columns=columns_to_drop)
            
            # 检查是否有数据库中存在但DataFrame中不存在的列
            missing_db_columns = db_columns - df_columns
            if missing_db_columns:
                print(f"[警告] 以下列在数据库中存在但在Excel中不存在，将设为NULL: {', '.join(missing_db_columns)}")
                for col in missing_db_columns:
                    if col not in ['id', '导入时间', '文件名', '日期', '月份']:  # 跳过自动生成的列
                        df[col] = None
            
            # 打印前5行数据，帮助调试
            print("[信息] 数据预览:")
            print(df.head().to_string())
            
            # 导入数据
            df.to_sql('bc_channeng_gonghao', engine, if_exists='append', index=False)
            print(f"[成功] 已导入 {len(df)} 条BC融合业务工号级数据")
        else:
            print("[警告] '工号级'sheet中没有数据")
            
        # 记录导入信息
        cursor = conn.cursor()
        
        # 记录BC融合业务工号级数据导入
        sql = """
        INSERT INTO import_logs (file_name, table_name, record_count, import_time)
        VALUES (%s, %s, %s, %s)
        """
        cursor.execute(sql, (filename, 'bc_channeng_gonghao', len(df), import_time))
        conn.commit()
        cursor.close()
        
        # 将已导入的Excel文件移动到inserted2db文件夹
        move_to_imported_folder(filepath, filename)
        
        return True
    except Exception as e:
        print(f"[错误] 导入BC融合业务日报表数据失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def process_bc_reports():
    """处理目标文件夹中的BC融合业务日报表文件"""
    print(f"📊 开始处理BC融合业务日报表 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 确保目标文件夹存在
    target_folder = ensure_folder_exists(TARGET_FOLDER)
    # 确保已导入文件夹存在
    ensure_folder_exists(IMPORTED_FOLDER)

    # 连接数据库
    db_conn = connect_to_database()

    # 创建必要的数据表
    create_tables_if_not_exist(db_conn)

    # 获取目标文件夹中的所有Excel文件
    excel_files = [f for f in os.listdir(target_folder) if f.endswith('.xlsx') and
                  '客户经理BC融合业务日报表' in f and
                  os.path.isfile(os.path.join(target_folder, f))]

    if not excel_files:
        print("[信息] 没有找到BC融合业务日报表文件")
        db_conn.close()
        return

    print(f"[信息] 找到 {len(excel_files)} 个BC融合业务日报表文件")

    # 处理每个文件
    for filename in excel_files:
        filepath = os.path.join(target_folder, filename)
        import_bc_report_data(db_conn, filepath, filename)

    # 关闭数据库连接
    db_conn.close()
    print("✅ BC融合业务日报表处理完成")

if __name__ == "__main__":
    process_bc_reports()






