import requests
import base64
import ddddocr
from io import BytesIO
from getpassword import get_encrypted_password
import os

# ========== 配置信息 ==========
USERNAME = "zhengdewen"  # 当前使用的用户名

# 密码映射表
PASSWORD_MAP = {
    "suyuquan": "your_plain_password_1",
    "antifouling": "your_plain_password_2",
    "zhengdewen": "Dewen@428"
}

# 百度 OCR 凭证
BAIDU_API_KEY = "vEKkNsQVoKlNBGrPrycVIqiq"
BAIDU_SECRET_KEY = "LIxdVLtDL4zRsttU20KqO1EX31V0aFC6"

def get_baidu_access_token(api_key, secret_key):
    print("[信息] 获取百度 Access Token...")
    url = "https://aip.baidubce.com/oauth/2.0/token"
    params = {
        "grant_type": "client_credentials",
        "client_id": api_key,
        "client_secret": secret_key
    }
    try:
        response = requests.post(url, params=params)
        response.raise_for_status()
        return response.json()["access_token"]
    except Exception as e:
        print(f"[错误] 获取百度 Access Token 失败: {e}")
        exit(1)

def get_captcha():
    url = "http://dict.gmcc.net:30722/dictWeb/gatewayService/bss-base-operation/base/getVerificationImage"
    try:
        response = requests.post(url)
        response.raise_for_status()
        data = response.json()
        body = data["ROOT"]["BODY"]
        if body["RETURN_CODE"] != "0":
            raise Exception(f"验证码获取失败: {body['RETURN_MSG']}")
        base64_str = body["OUT_DATA"]["base64String"]
        key = body["OUT_DATA"]["key"]
        print(f"[信息] 获取验证码成功{base64_str}，key: {key}")
        return base64_str, key
    except Exception as e:
        print(f"[错误] 获取验证码失败: {e}")
        exit(1)

def recognize_captcha_baidu(base64_str, access_token):
    print("[信息] 使用百度 OCR 识别验证码...")
    url = f"https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic?access_token={access_token}"
    headers = {'Content-Type': 'application/x-www-form-urlencoded'}
    img_data = base64.b64decode(base64_str)
    base64_image = base64.b64encode(img_data).decode()
    data = {"image": base64_image}

    try:
        response = requests.post(url, headers=headers, data=data)
        response.raise_for_status()
        result = response.json()
        if "words_result" in result and result["words_result"]:
            captcha = result["words_result"][0]["words"].strip()
            print(f"[信息] 识别出的验证码: {captcha}")
            return captcha
        else:
            raise Exception("未识别出任何文字")
    except Exception as e:
        print(f"[错误] 百度 OCR 识别失败: {e}")
        print(f"[调试] 响应内容: {response.text if 'response' in locals() else '无响应'}")
        exit(1)

def get_user_password(username):
    """
    获取用户的加密密码
    """
    if username not in PASSWORD_MAP:
        print(f"[错误] 未知用户名: {username}")
        exit(1)
        print(f"{PASSWORD_MAP[username]}:900000000000")
    return get_encrypted_password(username, PASSWORD_MAP[username], method="with_md5")

def login(username, captcha, key):
    url = "http://dict.gmcc.net:30722/dictWeb/gatewayService/gateway/login"
    try:
        # 获取加密后的密码
        password = get_user_password(username)
        print(f"[信息] 用户 {username} 的加密密码: {password}")
        params = {
            "sysUserCode": username,
            "password": password,
            "smsCode": captcha,
            "key": key
        }
        
        session = requests.Session()
        response = session.get(url, params=params)
        response.raise_for_status()
        print("[信息] 登录成功")
        return session.cookies
    except Exception as e:
        print(f"[错误] 登录失败: {e}")
        print(f"[调试] 响应内容: {response.text if 'response' in locals() else '无响应'}")
        exit(1)

def save_cookies(cookies, filename="cookies.txt"):
    try:
        with open(filename, "w") as f:
            # 检查 cookies 是否为 RequestsCookieJar 对象
            if hasattr(cookies, 'items'):
                # 如果是字典类型的 cookies
                for name, value in cookies.items():
                    f.write(f"{name}={value}\n")
            elif hasattr(cookies, '__iter__'):
                # 如果是可迭代的 cookies 集合
                for cookie in cookies:
                    if hasattr(cookie, 'name') and hasattr(cookie, 'value'):
                        f.write(f"{cookie.name}={cookie.value}\n")
                    else:
                        f.write(f"{cookie}\n")
            else:
                # 如果是其他类型，直接写入字符串表示
                f.write(str(cookies))
                
        # 检查文件是否为空
        if os.path.getsize(filename) == 0:
            print(f"[警告] {filename} 文件为空，cookies 可能未正确保存")
            print(f"[调试] cookies 类型: {type(cookies)}")
            print(f"[调试] cookies 内容: {cookies}")
        else:
            print(f"[信息] Cookie {cookies}已保存到 {filename}")
    except Exception as e:
        print(f"[错误] 保存 Cookie 失败: {e}")
        exit(1)

def ddocr(base64_str):
    ocr = ddddocr.DdddOcr()
    img_bytes = base64.b64decode(base64_str)
    captcha = ocr.classification(img_bytes)
    print(f"[信息] ddddocr 识别结果: {captcha}")
    return captcha

def main():
    print("🚀 开始执行验证码登录流程...")
    base64_str, key = get_captcha()
    captcha_text = ddocr(base64_str)
    print(f'{USERNAME}:88888, {captcha_text}, {key}')

    cookies = login(USERNAME, captcha_text, key)
    save_cookies(cookies)

if __name__ == "__main__":
    main()