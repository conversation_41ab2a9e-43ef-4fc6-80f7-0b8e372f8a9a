-- 创建综合查询项目方案信息表
-- 对应接口：saleCenterApp/formulation/queryProgram

DROP TABLE IF EXISTS `dict_zonghe_queryProgram`;

CREATE TABLE IF NOT EXISTS `dict_zonghe_queryProgram` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  
  -- 入参字段（来源于源数据表）
  `INPUT_LOGIN_NO` varchar(100) DEFAULT NULL COMMENT '入参-登录用户名',
  `INPUT_PROJECT_ID` varchar(100) DEFAULT NULL COMMENT '入参-项目ID（来源：v_distinct_project_id视图或sign_data_detail表项目编码字段）',
  
  -- 响应数据字段（基于接口返回的JSON结构）
  `TRADE` varchar(50) DEFAULT NULL COMMENT '行业',
  `TRADE_DESC` varchar(200) DEFAULT NULL COMMENT '行业描述',
  `FIRST_SCENE` varchar(100) DEFAULT NULL COMMENT '一级场景',
  `FIRST_SCENE_DESC` varchar(200) DEFAULT NULL COMMENT '一级场景描述',
  `SECOND_SCENE` varchar(100) DEFAULT NULL COMMENT '二级场景',
  `SECOND_SCENE_DESC` varchar(200) DEFAULT NULL COMMENT '二级场景描述',
  `SOLUTION_DETAIL_LIST` text DEFAULT NULL COMMENT '解决方案详情列表',
  `BUSINESS_MODEL_LIST` text DEFAULT NULL COMMENT '商业模式列表',
  `COOPERATION_MODE_LIST` text DEFAULT NULL COMMENT '合作模式列表',
  
  -- 系统字段
  `RUN_IP` varchar(50) DEFAULT NULL COMMENT '运行IP',
  `REQUEST_ID` varchar(100) DEFAULT NULL COMMENT '请求ID',
  `RETURN_CODE` varchar(10) DEFAULT NULL COMMENT '返回码',
  `RETURN_MSG` varchar(200) DEFAULT NULL COMMENT '返回消息',
  `USER_MSG` varchar(200) DEFAULT NULL COMMENT '用户消息',
  `DETAIL_MSG` text DEFAULT NULL COMMENT '详细消息',
  
  -- 元数据字段
  `import_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_input_project_id` (`INPUT_PROJECT_ID`),
  KEY `idx_trade` (`TRADE`),
  KEY `idx_first_scene` (`FIRST_SCENE`),
  KEY `idx_second_scene` (`SECOND_SCENE`),
  KEY `idx_import_time` (`import_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='综合查询项目方案信息表';
