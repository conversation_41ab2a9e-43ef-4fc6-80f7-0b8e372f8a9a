#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查数据库中的数据
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'dict_romte'))
from dict_romte.config import get_db_config
import pymysql

def check_data():
    """检查数据库中的数据"""
    DB_CONFIG = get_db_config('default')
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 检查项目基本信息表
        cursor.execute("SELECT COUNT(*) FROM dict_zonghe_queryProjectInfo")
        count = cursor.fetchone()[0]
        print(f"dict_zonghe_queryProjectInfo 表中有 {count} 条记录")
        
        if count > 0:
            cursor.execute("""
                SELECT INPUT_PROJECT_ID, PROJECT_NAME, CUST_NAME, PROJECT_STAGE_NAME, ESTIMATED_AMOUNT 
                FROM dict_zonghe_queryProjectInfo 
                LIMIT 5
            """)
            records = cursor.fetchall()
            print("\n前5条记录:")
            for record in records:
                print(f"  项目ID: {record[0]}")
                print(f"  项目名称: {record[1]}")
                print(f"  客户名称: {record[2]}")
                print(f"  项目阶段: {record[3]}")
                print(f"  预计金额: {record[4]}")
                print("  " + "-" * 50)
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"[错误] 检查数据失败: {e}")

if __name__ == "__main__":
    check_data()
