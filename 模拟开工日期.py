import requests
import json

url = "http://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp//preparation/queryProjectStart"

payload = {
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "zheng<PERSON><PERSON>"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20250111001"
    }
  }
}

headers = {
  'Host': "dict.gmcc.net:30722",
  'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  'Accept': "application/json, text/plain, */*",
  'Accept-Encoding': "gzip, deflate",
  'Content-Type': "application/json",
  'Pragma': "no-cache",
  'Cache-Control': "no-cache",
  'x-session-staffname': "dengyong",
  'x-session-regionid': "999",
  'x-session-sysusercode': "dengyong",
  'x-session-staffid': "1000032328",
  'Accept-Language': "zh-CN,zh;q=0.9",
  'Cookie': "BSS-SESSION=MThkZDMyMTItNmRhNC00Y2Y5LTkwZjMtODBkODgzN2U3MWJi; "
}

response = requests.post(url, data=json.dumps(payload), headers=headers)

print(response.text)