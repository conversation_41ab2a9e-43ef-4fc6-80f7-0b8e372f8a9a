import os
import email
import poplib
from datetime import datetime, timedelta
import time
import pymysql
import pandas as pd
from sqlalchemy import create_engine
import re

# 邮箱配置
EMAIL = "<EMAIL>"
PASSWORD = "XBbyfQEf2PpqUif5"  # 授权密码
POP3_SERVER = "pop.126.com"
POP3_PORT = 995

# 目标文件夹
TARGET_FOLDER = "shichang"

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'cmcc12345',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

def ensure_folder_exists(folder_path):
    """确保目标文件夹存在"""
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)
        print(f"[信息] 创建目标文件夹: {folder_path}")
    return folder_path

def connect_to_mailbox():
    """连接到126邮箱(POP3)"""
    try:
        # 连接到POP3服务器
        mail = poplib.POP3_SSL(POP3_SERVER, POP3_PORT)
        # 身份认证
        mail.user(EMAIL)
        mail.pass_(PASSWORD)
        # 获取邮件信息
        email_count, total_size = mail.stat()
        print(f"[成功] 已连接到邮箱: {EMAIL}, 共有 {email_count} 封邮件")
        return mail, email_count
    except Exception as e:
        print(f"[错误] 连接邮箱失败: {e}")
        exit(1)

def connect_to_database():
    """连接到MySQL数据库"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        print(f"[成功] 已连接到数据库: {DB_CONFIG['database']}")
        return conn
    except Exception as e:
        print(f"[错误] 连接数据库失败: {e}")
        exit(1)

def get_recent_emails(mail, email_count, days=7):
    """获取最近几天的邮件"""
    recent_emails = []
    cutoff_date = datetime.now() - timedelta(days=days)
    
    # POP3协议不支持按日期搜索，需要下载所有邮件头并筛选
    print(f"[信息] 正在检索最近{days}天的邮件...")
    
    # 从最新的邮件开始检查
    for i in range(email_count, 0, -1):
        try:
            # 获取邮件头
            resp, lines, octets = mail.top(i, 0)
            # 解析邮件头
            msg_content = b'\r\n'.join(lines).decode('utf-8', errors='ignore')
            msg = email.message_from_string(msg_content)
            
            # 获取日期
            date_str = msg.get("Date", "")
            if not date_str:
                continue
                
            try:
                # 尝试解析日期
                msg_date = email.utils.parsedate_to_datetime(date_str)
                # 检查是否在指定日期范围内
                if msg_date >= cutoff_date:
                    recent_emails.append(i)
                else:
                    # 由于邮件是按时间倒序的，如果遇到早于截止日期的邮件，可以停止检查
                    break
            except:
                # 如果日期解析失败，仍然包含该邮件（保险起见）
                recent_emails.append(i)
        except Exception as e:
            print(f"[警告] 检索邮件 {i} 头信息失败: {e}")
    
    print(f"[信息] 找到 {len(recent_emails)} 封最近{days}天的邮件")
    return recent_emails

def save_email_record(conn, sender, recipients, send_date, subject, attachment_list, receive_date):
    """保存邮件记录到数据库"""
    cursor = conn.cursor()
    try:
        sql = """
        INSERT INTO email_records (sender, recipients, send_date, subject, attachment_list, receive_date)
        VALUES (%s, %s, %s, %s, %s, %s)
        """
        cursor.execute(sql, (sender, recipients, send_date, subject, attachment_list, receive_date))
        conn.commit()
        print(f"[成功] 邮件记录已保存到数据库")
        return cursor.lastrowid
    except Exception as e:
        conn.rollback()
        print(f"[错误] 保存邮件记录失败: {e}")
        return None
    finally:
        cursor.close()

def import_market_share_data(conn, filepath, filename):
    """导入市场份额数据到数据库"""
    try:
        print(f"[信息] 正在导入市场份额数据: {filepath}")
        
        # 创建SQLAlchemy引擎
        engine_url = f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?charset={DB_CONFIG['charset']}"
        engine = create_engine(engine_url)
        
        # 读取Excel文件中的"日监控"sheet
        print("[信息] 正在读取'日监控'sheet...")
        df_daily = pd.read_excel(filepath, sheet_name="日监控")
        
        # 确保数据从第二行开始（第一行为表头）
        if len(df_daily) > 0:
            # 导入数据到market_share_daily表
            df_daily.to_sql('market_share_daily', engine, if_exists='append', index=False)
            print(f"[成功] 已导入 {len(df_daily)} 条日监控数据")
        else:
            print("[警告] '日监控'sheet中没有数据")
        
        # 读取Excel文件中的"号码明细"sheet
        print("[信息] 正在读取'号码明细'sheet...")
        df_detail = pd.read_excel(filepath, sheet_name="号码明细")
        
        # 确保数据从第二行开始（第一行为表头）
        if len(df_detail) > 0:
            # 导入数据到market_share_detail表
            df_detail.to_sql('market_share_detail', engine, if_exists='append', index=False)
            print(f"[成功] 已导入 {len(df_detail)} 条号码明细数据")
        else:
            print("[警告] '号码明细'sheet中没有数据")
        
        # 记录导入信息
        cursor = conn.cursor()
        import_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 记录日监控数据导入
        sql = """
        INSERT INTO import_logs (file_name, table_name, record_count, import_time)
        VALUES (%s, %s, %s, %s)
        """
        cursor.execute(sql, (filename, 'market_share_daily', len(df_daily), import_time))
        
        # 记录号码明细数据导入
        cursor.execute(sql, (filename, 'market_share_detail', len(df_detail), import_time))
        
        conn.commit()
        cursor.close()
        
        total_records = len(df_daily) + len(df_detail)
        print(f"[成功] 总共导入 {total_records} 条市场份额数据")
        return True
    except Exception as e:
        print(f"[错误] 导入市场份额数据失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def download_attachments(mail, email_ids, target_folder, db_conn):
    """下载邮件附件并记录到数据库"""
    downloaded_count = 0
    market_share_files = []
    
    for email_id in email_ids:
        try:
            # 获取完整邮件
            resp, lines, octets = mail.retr(email_id)
            # 解析邮件内容
            msg_content = b'\r\n'.join(lines)
            msg = email.message_from_bytes(msg_content)
            
            # 获取邮件信息
            subject = msg.get("Subject", "")
            try:
                # 处理编码的主题
                if subject.startswith("=?"):
                    subject = email.header.decode_header(subject)[0][0]
                    if isinstance(subject, bytes):
                        subject = subject.decode('utf-8')
            except:
                subject = f"未知主题_{email_id}"
            
            # 获取发件人
            sender = msg.get("From", "")
            if sender.startswith("=?"):
                sender = email.header.decode_header(sender)[0][0]
                if isinstance(sender, bytes):
                    sender = sender.decode('utf-8')
            
            # 获取收件人
            recipients = msg.get("To", "")
            if recipients.startswith("=?"):
                recipients = email.header.decode_header(recipients)[0][0]
                if isinstance(recipients, bytes):
                    recipients = recipients.decode('utf-8')
            
            # 获取发送时间
            date_str = msg.get("Date", "")
            try:
                # 尝试解析日期格式
                send_date = email.utils.parsedate_to_datetime(date_str)
                send_date_str = send_date.strftime("%Y-%m-%d %H:%M:%S")
            except:
                send_date_str = date_str
            
            print(f"[信息] 处理邮件: {subject}")
            
            # 下载附件
            attachment_names = []
            for part in msg.walk():
                if part.get_content_maintype() == 'multipart':
                    continue
                if part.get('Content-Disposition') is None:
                    continue
                    
                filename = part.get_filename()
                if filename:
                    try:
                        if isinstance(filename, bytes):
                            filename = filename.decode()
                        elif filename.startswith("=?"):
                            filename = email.header.decode_header(filename)[0][0]
                            if isinstance(filename, bytes):
                                filename = filename.decode('utf-8')
                    except:
                        filename = f"附件_{email_id}_{int(time.time())}"
                    
                    filepath = os.path.join(target_folder, filename)
                    
                    # 保存附件
                    with open(filepath, 'wb') as f:
                        f.write(part.get_payload(decode=True))
                    
                    print(f"[成功] 下载附件: {filename}")
                    attachment_names.append(filename)
                    downloaded_count += 1
                    
                    # 检查是否为市场份额文件
                    if re.match(r'拍照集团份额\d{8}\.xlsx', filename):
                        market_share_files.append((filepath, filename))
            
            # 保存邮件记录到数据库
            receive_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            attachment_list = ", ".join(attachment_names)
            save_email_record(db_conn, sender, recipients, send_date_str, subject, attachment_list, receive_date)
            
        except Exception as e:
            print(f"[错误] 处理邮件 {email_id} 时出错: {e}")
    
    # 导入市场份额数据
    for filepath, filename in market_share_files:
        import_market_share_data(db_conn, filepath, filename)
    
    return downloaded_count

def create_tables_if_not_exist(conn):
    """创建必要的数据表（如果不存在）"""
    cursor = conn.cursor()
    try:
        # 创建邮件记录表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS email_records (
            id INT AUTO_INCREMENT PRIMARY KEY,
            sender VARCHAR(255),
            recipients VARCHAR(255),
            send_date VARCHAR(50),
            subject VARCHAR(255),
            attachment_list TEXT,
            receive_date DATETIME
        )
        """)
        
        # 创建导入日志表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS import_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            file_name VARCHAR(255),
            table_name VARCHAR(50),
            record_count INT,
            import_time DATETIME
        )
        """)
        
        # 创建市场份额日监控表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS market_share_daily (
            id INT AUTO_INCREMENT PRIMARY KEY,
            日期 DATE,
            集团名称 VARCHAR(100),
            总证件 INT,
            属于拍照 INT,
            属于非拍照新流入 INT,
            拍照时中山移动 INT,
            截止当日在网 INT,
            截止当日流失 INT,
            拍照后新增 INT,
            其中属于XR INT,
            其中客经 INT,
            其中网格经理 INT,
            其中同一网格渠道 INT,
            其他 INT,
            当日到达 INT,
            拍照宽带 INT,
            当日宽带 INT,
            宽带流失 INT,
            宽带新增 INT,
            宽带到达 INT,
            导入时间 DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        # 创建市场份额号码明细表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS market_share_detail (
            id INT AUTO_INCREMENT PRIMARY KEY,
            更新日期 DATE,
            号码 VARCHAR(20),
            集团名称 VARCHAR(100),
            新入网归属类型 VARCHAR(50),
            工号 VARCHAR(50),
            渠道编码 VARCHAR(50),
            渠道名称 VARCHAR(100),
            分公司 VARCHAR(50),
            网格 VARCHAR(50),
            是否XR VARCHAR(10),
            是否有宽带 VARCHAR(10),
            是否有高质量合约 VARCHAR(10),
            导入时间 DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        conn.commit()
        print("[信息] 已确保所需数据表存在")
    except Exception as e:
        conn.rollback()
        print(f"[错误] 创建数据表失败: {e}")
    finally:
        cursor.close()

def main():
    print(f"📥 开始从126邮箱下载最近7天的邮件附件 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 确保目标文件夹存在
    target_folder = ensure_folder_exists(TARGET_FOLDER)
    
    # 连接邮箱
    mail, email_count = connect_to_mailbox()
    
    # 连接数据库
    db_conn = connect_to_database()
    
    # 创建必要的数据表
    create_tables_if_not_exist(db_conn)
    
    # 获取最近7天的邮件
    email_ids = get_recent_emails(mail, email_count, days=7)
    
    # 下载附件并记录到数据库
    if email_ids:
        downloaded_count = download_attachments(mail, email_ids, target_folder, db_conn)
        print(f"[完成] 共下载了 {downloaded_count} 个附件到 {target_folder} 文件夹")
    else:
        print("[信息] 没有找到最近7天的邮件")
    
    # 关闭连接
    mail.quit()
    db_conn.close()
    print("✅ 邮件附件下载完成")

if __name__ == "__main__":
    main()