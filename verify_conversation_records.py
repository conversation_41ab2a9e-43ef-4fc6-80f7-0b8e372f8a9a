#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证对话记录是否正确保存到数据库
"""

import sys
import os
import pymysql

sys.path.append(os.path.join(os.path.dirname(__file__), 'dict_romte'))
from dict_romte.config import get_db_config

def verify_records():
    """验证记录"""
    try:
        DB_CONFIG = get_db_config('default')
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        print("🔍 验证对话记录保存情况")
        print("=" * 60)
        
        # 检查对话记录表
        cursor.execute("SELECT COUNT(*) FROM dict_conversation_records")
        conversation_count = cursor.fetchone()[0]
        print(f"📋 对话记录: {conversation_count} 条")
        
        # 显示最新会话的记录
        cursor.execute("""
            SELECT sequence_no, message_type, 
                   SUBSTRING(COALESCE(instruction, response), 1, 50) as content,
                   operations, status
            FROM dict_conversation_records 
            WHERE session_id = (
                SELECT session_id FROM dict_conversation_records 
                ORDER BY create_time DESC LIMIT 1
            )
            ORDER BY sequence_no
        """)
        
        records = cursor.fetchall()
        print(f"\n📝 最新会话记录 ({len(records)} 条):")
        for record in records:
            seq, msg_type, content, operations, status = record
            print(f"  {seq}. [{msg_type}] {content}...")
            if operations:
                print(f"     操作: {operations[:60]}...")
            print(f"     状态: {status}")
        
        # 检查项目总结表
        cursor.execute("SELECT COUNT(*) FROM dict_project_summary")
        summary_count = cursor.fetchone()[0]
        print(f"\n📊 项目总结: {summary_count} 条")
        
        if summary_count > 0:
            cursor.execute("""
                SELECT project_name, total_interfaces, crawler_programs, 
                       database_tables, completion_rate, quality_rating, project_status
                FROM dict_project_summary 
                ORDER BY create_time DESC LIMIT 1
            """)
            
            summary = cursor.fetchone()
            if summary:
                project_name, total_interfaces, crawler_programs, database_tables, completion_rate, quality_rating, project_status = summary
                print(f"\n🎯 最新项目总结:")
                print(f"  项目名称: {project_name}")
                print(f"  总接口数: {total_interfaces}")
                print(f"  爬虫程序: {crawler_programs}")
                print(f"  数据表数: {database_tables}")
                print(f"  完成率: {completion_rate}%")
                print(f"  质量评级: {quality_rating}")
                print(f"  项目状态: {project_status}")
        
        cursor.close()
        conn.close()
        
        print(f"\n✅ 记录验证完成")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

if __name__ == "__main__":
    verify_records()
