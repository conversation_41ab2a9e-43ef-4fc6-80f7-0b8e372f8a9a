#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json
from datetime import datetime, timedelta
import os
import sys

def load_cookies(filename="cookies.txt"):
    """从cookies.txt文件中加载cookie，支持JSON格式和简单键值对格式"""
    cookies = {}
    try:
        if os.path.exists(filename):
            with open(filename, "r", encoding="utf-8") as f:
                content = f.read().strip()

                # 判断是否为JSON格式
                if content.startswith('{') and content.endswith('}'):
                    # JSON格式解析
                    import json
                    data = json.loads(content)
                    if "cookies" in data and isinstance(data["cookies"], list):
                        for cookie in data["cookies"]:
                            if "name" in cookie and "value" in cookie:
                                cookies[cookie["name"]] = cookie["value"]
                        print(f"[信息] 已从{filename}加载Cookie (JSON格式，共{len(cookies)}个)")
                    else:
                        print(f"[错误] JSON格式不正确，缺少cookies数组")
                        exit(1)
                else:
                    # 简单键值对格式解析
                    for line in content.split('\n'):
                        if "=" in line:
                            name, value = line.strip().split("=", 1)
                            cookies[name] = value
                    print(f"[信息] 已从{filename}加载Cookie (键值对格式，共{len(cookies)}个)")

            return cookies
        else:
            print(f"[警告] Cookie文件{filename}不存在，请先运行getcookie.py获取Cookie")
            exit(1)
    except json.JSONDecodeError as e:
        print(f"[错误] JSON格式解析失败: {e}")
        exit(1)
    except Exception as e:
        print(f"[错误] 加载Cookie失败: {e}")
        exit(1)

def get_hetong_all(start_date=None, end_date=None):
    """获取合同信息列表，可选指定日期范围"""
    # 加载cookie
    cookies_dict = load_cookies()
    cookie_str = "; ".join([f"{name}={value}" for name, value in cookies_dict.items()])

    # 调试信息：显示加载的cookie
    print(f"[调试] 加载的cookies字典: {cookies_dict}")
    print(f"[调试] 拼接的cookie字符串: {cookie_str}")
    
    url = "http://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/common/excelController/export"
    
    # 如果没有提供日期范围，默认不限制日期
    if start_date is None and end_date is None:
        more_param = {
            "CONTRACT_SERIAL_NO": "",
            "PROJECT_NO": "",
            "CONTRACT_NAME": "",
            "INC_EXP_TYPE": "",
            "CONTRACT_STATUS": "",
            "START_DATE": "",
            "END_DATE": "",
            "REGION_CODE": "",
            "POST_CODE": "10034,10072,10081,20001,10001,10061,10000,10270,10150,10417,10464,10455,10275,10149",
            "CONTRACT_NO": ""
        }
    else:
        # 如果只提供了开始日期，结束日期默认为当前日期
        if end_date is None:
            end_date = datetime.now()
        
        # 如果只提供了结束日期，开始日期默认为结束日期前30天
        if start_date is None:
            start_date = end_date - timedelta(days=30)
            
        # 格式化日期为字符串
        start_date_str = start_date.strftime("%Y-%m-%d 00:00:00")
        end_date_str = end_date.strftime("%Y-%m-%d 23:59:59")
        
        more_param = {
            "CONTRACT_SERIAL_NO": "",
            "PROJECT_NO": "",
            "CONTRACT_NAME": "",
            "INC_EXP_TYPE": "",
            "CONTRACT_STATUS": "",
            "START_DATE": start_date_str,
            "END_DATE": end_date_str,
            "REGION_CODE": "",
            "POST_CODE": "10034,10072,10081,20001,10001,10061,10000,10270,10150,10417,10464,10455,10275,10149",
            "CONTRACT_NO": ""
        }
    
    payload = {
        "ROOT": {
            "HEADER": {
                "OPR_INFO": {
                    "LOGIN_NO": "zhengdewen"
                }
            },
            "BODY": {
                "IS_EXPORT": "20",
                "CONFIG_CODE": "queryContractListExport",
                "MORE_PARAM": json.dumps(more_param)
            }
        }
    }
    
    headers = {
        'Host': "dict.gmcc.net:30722",
        'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        'Accept': "application/json, text/plain, */*",
        'Accept-Encoding': "gzip, deflate",
        'Content-Type': "application/json",
        'Pragma': "no-cache",
        'Cache-Control': "no-cache",
        'x-session-staffname': "dengyong",
        'x-session-regionid': "200",
        'x-session-sysusercode': "dengyong",
        'x-session-staffid': "10000",
        'Origin': "http://dict.gmcc.net:30722",
        'Referer': "http://dict.gmcc.net:30722/dictWeb/gdydFlowPath/contractManage",
        'Accept-Language': "zh-CN,zh;q=0.9",
        'Cookie': cookie_str
    }
    
    try:
        print("[信息] 正在发送请求获取合同信息列表...")
        if start_date and end_date:
            print(f"[信息] 查询日期范围: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
        
        response = requests.post(url, data=json.dumps(payload), headers=headers)
        response.raise_for_status()  # 检查响应状态
        
        # 生成带时间戳的文件名
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        
        # 根据是否有日期范围生成不同的文件名
        if start_date and end_date:
            start_str = start_date.strftime("%Y%m%d")
            end_str = end_date.strftime("%Y%m%d")
            filename = f"合同信息列表_{start_str}至{end_str}_{timestamp}.xlsx"
        else:
            filename = f"合同信息列表_全部_{timestamp}.xlsx"
        
        # 直接将响应内容以二进制方式写入到带时间戳的xlsx文件中
        with open(filename, "wb") as f:
            f.write(response.content)
        print(f"[成功] 响应内容已保存为 {filename}")
        return filename
        
    except Exception as e:
        print(f"[错误] 获取合同信息列表失败: {e}")
        
        # 如果发生错误，尝试保存响应内容以便调试
        if 'response' in locals():
            error_filename = f"error_合同信息列表_{datetime.now().strftime('%Y%m%d%H%M%S')}.txt"
            with open(error_filename, "w", encoding="utf-8") as f:
                f.write(f"状态码: {response.status_code}\n")
                f.write(f"响应头: {response.headers}\n\n")
                f.write(f"响应内容:\n{response.text}")
            print(f"[信息] 错误响应已保存为 {error_filename}")
        return None

def parse_date(date_str):
    """解析日期字符串为datetime对象"""
    try:
        return datetime.strptime(date_str, "%Y-%m-%d")
    except ValueError:
        try:
            return datetime.strptime(date_str, "%Y%m%d")
        except ValueError:
            print(f"[错误] 无效的日期格式: {date_str}，请使用YYYY-MM-DD或YYYYMMDD格式")
            return None

def main():
    """主函数，处理命令行参数"""
    start_date = None
    end_date = None
    
    # 解析命令行参数
    if len(sys.argv) > 1:
        if len(sys.argv) >= 2:
            start_date = parse_date(sys.argv[1])
            if start_date is None:
                return
        
        if len(sys.argv) >= 3:
            end_date = parse_date(sys.argv[2])
            if end_date is None:
                return
    
    # 获取合同信息列表
    get_hetong_all(start_date, end_date)

if __name__ == "__main__":
    print("🚀 开始获取合同信息列表...")
    main()
    print("✅ 合同信息获取完成")
