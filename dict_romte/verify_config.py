#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单的配置验证脚本
"""

import os

def verify_files():
    files = [
        'login2cookie.py',
        'import_to_mysql.py',
        'import_hetong_to_mysql.py',
        'LOCAL_run_procedure.py',
        'DICT_download_local_kuanbiao_from_db2email.py',
        'NEW_sign_get_jsessionid.py',
        'get_sign_detail.py',
        'ne76_get_hetong_all.py'
    ]

    print("配置导入验证结果:")
    print("=" * 50)
    print(f"当前工作目录: {os.getcwd()}")
    print("=" * 50)

    success_count = 0

    for filename in files:
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
                
            patterns = [
                'from config import',
                'get_db_config',
                'EMAIL_CONFIG',
                'LOGIN_CONFIG',
                'FILE_CONFIG'
            ]
            
            found = False
            for pattern in patterns:
                if pattern in content:
                    found = True
                    break
            
            if found:
                print(f"✅ {filename} - 已使用统一配置")
                success_count += 1
            else:
                print(f"❌ {filename} - 未使用统一配置")
                
        except FileNotFoundError:
            print(f"❌ {filename} - 文件不存在")
        except Exception as e:
            print(f"❌ {filename} - 检查失败: {e}")
    
    print("=" * 50)
    print(f"总计: {success_count}/{len(files)} 个文件已使用统一配置")
    
    return success_count

if __name__ == "__main__":
    # 确保在正确的目录中运行
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    verify_files()
