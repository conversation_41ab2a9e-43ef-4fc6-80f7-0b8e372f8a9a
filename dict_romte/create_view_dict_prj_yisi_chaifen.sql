-- ========================================
-- 创建视图：v_dict_prj_yisi_chaifen
-- 功能：将dict_prj_yisi_chaifen表的项目编码列表拆分为单个项目记录
-- 创建时间：2025-07-30
-- ========================================

-- 删除已存在的视图
DROP VIEW IF EXISTS v_dict_prj_yisi_chaifen;

-- 创建拆分视图
CREATE VIEW v_dict_prj_yisi_chaifen AS
SELECT 
    -- 原记录标识
    d.id AS 原记录ID,
    
    -- 基本信息
    d.客户名称,
    d.时间窗口开始,
    d.时间窗口结束,
    d.涉及项目数量 AS 原记录项目总数,
    
    -- 拆分后的项目信息
    TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(d.项目编码列表, ',', numbers.n), ',', -1)) AS 项目编码,
    TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(d.项目名称列表, ',', numbers.n), ',', -1)) AS 项目名称,
    TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(d.收入侧合同编码列表, ',', numbers.n), ',', -1)) AS 收入侧合同编码,
    
    -- 项目序号信息
    numbers.n AS 项目序号,
    
    -- 财务信息（保持原值）
    d.IT收入累加值,
    d.单项目最大IT收入,
    d.合同含税金额累加值,
    
    -- 相似度分析信息
    d.CT内容平均相似度,
    d.IT内容平均相似度,
    d.CT内容最高相似度,
    d.IT内容最高相似度,
    d.综合相似度评分,
    d.相似度分析详情,
    d.相似度判断依据,
    
    -- 分布信息
    d.项目阶段分布,
    d.所属行业分布,
    d.归属区县分布,
    d.项目经理列表,
    
    -- 其他信息
    d.风险等级,
    d.检测时间,
    d.数据来源,
    d.备注
    
FROM dict_prj_yisi_chaifen d
CROSS JOIN (
    SELECT 1 n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL 
    SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL 
    SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10
) numbers
WHERE 
    -- 确保序号不超过实际项目数量
    numbers.n <= (
        CHAR_LENGTH(d.项目编码列表) - CHAR_LENGTH(REPLACE(d.项目编码列表, ',', '')) + 1
    )
    -- 确保拆分出的项目编码不为空
    AND TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(d.项目编码列表, ',', numbers.n), ',', -1)) != ''
    
ORDER BY d.id, numbers.n;

-- ========================================
-- 视图使用说明
-- ========================================
/*
视图功能说明：
1. 将dict_prj_yisi_chaifen表中的"项目编码列表"按逗号拆分
2. 每个项目编码+其他字段形成一条记录
3. 保留原记录的所有信息，便于追溯
4. 添加项目序号，便于排序和分析

字段说明：
- 原记录ID：对应dict_prj_yisi_chaifen表的id
- 项目编码：从"项目编码列表"中拆分出的单个项目编码
- 项目名称：从"项目名称列表"中拆分出的对应项目名称
- 项目序号：该项目在原记录中的序号（1,2,3...）
- 原记录项目总数：原记录包含的项目总数

使用示例：
-- 查看所有拆分后的记录
SELECT * FROM v_dict_prj_yisi_chaifen;

-- 按客户查看拆分后的项目
SELECT 客户名称, 项目编码, 项目名称, IT收入累加值 
FROM v_dict_prj_yisi_chaifen 
WHERE 客户名称 LIKE '%中山%';

-- 统计每个客户的项目数量
SELECT 客户名称, COUNT(*) as 项目数量
FROM v_dict_prj_yisi_chaifen 
GROUP BY 客户名称;
*/
