#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
query_yisi_chaifen_results.py - 查询疑似拆分立项检测结果
功能：查询和展示dict_prj_yisi_chaifen表中的检测结果

作者：系统自动生成
创建时间：2025-07-30
"""

# 自动设置UTF-8编码，解决中文乱码问题
try:
    import utf8_setup
except ImportError:
    # 如果utf8_setup模块不存在，使用内置设置
    import sys, os
    if sys.platform.startswith('win'):
        os.system('chcp 65001 >nul 2>&1')
        os.environ['PYTHONIOENCODING'] = 'utf-8'

import pymysql
import pandas as pd
from datetime import datetime
from config import get_db_config

def query_all_results():
    """查询所有疑似拆分立项结果"""
    try:
        # 连接数据库
        db_config = get_db_config('remote')
        conn = pymysql.connect(**db_config)
        
        query = """
        SELECT 
            客户名称,
            时间窗口开始,
            时间窗口结束,
            涉及项目数量,
            IT收入累加值,
            单项目最大IT收入,
            风险等级,
            检测时间,
            项目编码列表,
            项目名称列表,
            备注
        FROM dict_prj_yisi_chaifen 
        ORDER BY IT收入累加值 DESC
        """
        
        df = pd.read_sql(query, conn)
        conn.close()
        
        print("=" * 120)
        print("疑似拆分立项检测结果")
        print("=" * 120)
        
        if len(df) == 0:
            print("没有检测到疑似拆分立项")
            return
        
        for index, row in df.iterrows():
            print(f"\n【结果 {index + 1}】")
            print(f"客户名称: {row['客户名称']}")
            print(f"时间窗口: {row['时间窗口开始']} 到 {row['时间窗口结束']}")
            print(f"涉及项目数量: {row['涉及项目数量']} 个")
            print(f"IT收入累加值: {row['IT收入累加值']:.2f} 万元")
            print(f"单项目最大IT收入: {row['单项目最大IT收入']:.2f} 万元")
            print(f"风险等级: {row['风险等级']}")
            print(f"检测时间: {row['检测时间']}")
            
            # 显示项目编码（限制长度）
            project_codes = str(row['项目编码列表'])
            if len(project_codes) > 100:
                print(f"项目编码: {project_codes[:100]}...")
            else:
                print(f"项目编码: {project_codes}")
            
            # 显示项目名称（限制长度）
            project_names = str(row['项目名称列表'])
            if len(project_names) > 100:
                print(f"项目名称: {project_names[:100]}...")
            else:
                print(f"项目名称: {project_names}")
            
            print(f"备注: {row['备注']}")
            print("-" * 120)
        
        return df
        
    except Exception as e:
        print(f"[错误] 查询结果失败: {e}")
        return None

def query_statistics():
    """查询统计信息"""
    try:
        # 连接数据库
        db_config = get_db_config('remote')
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        print("\n" + "=" * 80)
        print("统计信息")
        print("=" * 80)
        
        # 总体统计
        cursor.execute("SELECT COUNT(*) FROM dict_prj_yisi_chaifen")
        total_count = cursor.fetchone()[0]
        print(f"总检测结果数量: {total_count}")
        
        cursor.execute("SELECT COUNT(DISTINCT 客户名称) FROM dict_prj_yisi_chaifen")
        customer_count = cursor.fetchone()[0]
        print(f"涉及客户数量: {customer_count}")
        
        # 按风险等级统计
        cursor.execute("""
            SELECT 风险等级, COUNT(*) as 数量, 
                   AVG(IT收入累加值) as 平均IT收入, 
                   MAX(IT收入累加值) as 最大IT收入,
                   MIN(IT收入累加值) as 最小IT收入
            FROM dict_prj_yisi_chaifen 
            GROUP BY 风险等级 
            ORDER BY FIELD(风险等级, '高', '中', '低')
        """)
        
        print("\n按风险等级统计:")
        print("-" * 80)
        for row in cursor.fetchall():
            print(f"风险等级: {row[0]} | 数量: {row[1]} | 平均IT收入: {row[2]:.2f}万元 | 最大: {row[3]:.2f}万元 | 最小: {row[4]:.2f}万元")
        
        # 按客户统计
        cursor.execute("""
            SELECT 客户名称, COUNT(*) as 检测次数, 
                   SUM(IT收入累加值) as 总IT收入,
                   MAX(风险等级) as 最高风险等级
            FROM dict_prj_yisi_chaifen 
            GROUP BY 客户名称 
            ORDER BY 总IT收入 DESC
            LIMIT 10
        """)
        
        print("\n客户风险排行榜（前10名）:")
        print("-" * 80)
        for i, row in enumerate(cursor.fetchall(), 1):
            print(f"{i:2d}. {row[0]} | 检测次数: {row[1]} | 总IT收入: {row[2]:.2f}万元 | 最高风险: {row[3]}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"[错误] 查询统计信息失败: {e}")

def query_by_customer(customer_name):
    """按客户名称查询"""
    try:
        # 连接数据库
        db_config = get_db_config('remote')
        conn = pymysql.connect(**db_config)
        
        query = """
        SELECT * FROM dict_prj_yisi_chaifen 
        WHERE 客户名称 LIKE %s
        ORDER BY IT收入累加值 DESC
        """
        
        df = pd.read_sql(query, conn, params=[f'%{customer_name}%'])
        conn.close()
        
        if len(df) == 0:
            print(f"没有找到客户 '{customer_name}' 的相关记录")
            return
        
        print(f"\n客户 '{customer_name}' 的疑似拆分立项记录:")
        print("=" * 100)
        
        for index, row in df.iterrows():
            print(f"\n记录 {index + 1}:")
            print(f"  时间窗口: {row['时间窗口开始']} 到 {row['时间窗口结束']}")
            print(f"  项目数量: {row['涉及项目数量']} 个")
            print(f"  IT收入累加值: {row['IT收入累加值']:.2f} 万元")
            print(f"  风险等级: {row['风险等级']}")
            print(f"  项目编码: {str(row['项目编码列表'])[:80]}...")
        
    except Exception as e:
        print(f"[错误] 按客户查询失败: {e}")

def export_to_excel():
    """导出结果到Excel文件"""
    try:
        # 连接数据库
        db_config = get_db_config('remote')
        conn = pymysql.connect(**db_config)
        
        query = """
        SELECT * FROM dict_prj_yisi_chaifen 
        ORDER BY IT收入累加值 DESC
        """
        
        df = pd.read_sql(query, conn)
        conn.close()
        
        if len(df) == 0:
            print("没有数据可导出")
            return
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"疑似拆分立项检测结果_{timestamp}.xlsx"
        
        # 导出到Excel
        df.to_excel(filename, index=False, engine='openpyxl')
        print(f"[成功] 结果已导出到文件: {filename}")
        
    except Exception as e:
        print(f"[错误] 导出Excel失败: {e}")

def main():
    """主函数"""
    print("疑似拆分立项检测结果查询工具")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 查看所有检测结果")
        print("2. 查看统计信息")
        print("3. 按客户名称查询")
        print("4. 导出到Excel文件")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-4): ").strip()
        
        if choice == '1':
            query_all_results()
        elif choice == '2':
            query_statistics()
        elif choice == '3':
            customer_name = input("请输入客户名称（支持模糊查询）: ").strip()
            if customer_name:
                query_by_customer(customer_name)
            else:
                print("客户名称不能为空")
        elif choice == '4':
            export_to_excel()
        elif choice == '0':
            print("退出程序")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
