#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试宽表文件移动功能
"""

# 自动设置UTF-8编码，解决中文乱码问题
try:
    import utf8_setup
except ImportError:
    # 如果utf8_setup模块不存在，使用内置设置
    import sys, os
    if sys.platform.startswith('win'):
        os.system('chcp 65001 >nul 2>&1')
        os.environ['PYTHONIOENCODING'] = 'utf-8'

import os
import shutil
from datetime import datetime
from file_manager import FileManager

def create_test_kuanbiao_file():
    """创建测试用的宽表文件"""
    today = datetime.now().strftime("%Y%m%d")
    test_filename = f"宽表{today}_test.xlsx"
    
    # 创建一个简单的测试文件
    with open(test_filename, 'w', encoding='utf-8') as f:
        f.write("这是一个测试宽表文件")
    
    print(f"[创建] 测试文件已创建: {test_filename}")
    return test_filename

def test_move_kuanbiao_file():
    """测试宽表文件移动功能"""
    print("=" * 60)
    print("测试宽表文件移动功能")
    print("=" * 60)
    
    # 创建测试文件
    test_file = create_test_kuanbiao_file()
    
    try:
        # 确保文件存在
        if not os.path.exists(test_file):
            print(f"[错误] 测试文件不存在: {test_file}")
            return False
        
        print(f"[信息] 测试文件路径: {os.path.abspath(test_file)}")
        
        # 初始化文件管理器
        file_manager = FileManager()
        
        # 检查已处理目录是否存在
        processed_dir = file_manager.processed_dir
        print(f"[信息] 已处理目录: {processed_dir}")
        
        if not os.path.exists(processed_dir):
            print(f"[信息] 创建已处理目录: {processed_dir}")
            os.makedirs(processed_dir)
        
        # 移动文件
        print(f"[测试] 正在移动文件: {test_file}")
        success, new_path = file_manager.move_file_to_processed(test_file, "宽表测试文件")
        
        if success:
            print(f"[成功] 文件已移动到: {new_path}")
            
            # 验证文件是否真的移动了
            if os.path.exists(new_path) and not os.path.exists(test_file):
                print("[验证] ✅ 文件移动成功，原文件已删除，新文件存在")
                
                # 清理测试文件
                os.remove(new_path)
                print("[清理] 测试文件已清理")
                return True
            else:
                print("[验证] ❌ 文件移动验证失败")
                return False
        else:
            print(f"[失败] 文件移动失败")
            # 清理测试文件
            if os.path.exists(test_file):
                os.remove(test_file)
            return False
            
    except Exception as e:
        print(f"[错误] 测试过程中出现异常: {e}")
        import traceback
        print(traceback.format_exc())
        
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)
        return False

def test_import_functionality():
    """测试导入功能是否正常"""
    print("\n" + "=" * 60)
    print("测试导入功能")
    print("=" * 60)
    
    try:
        # 测试配置导入
        from config import get_db_config, EMAIL_CONFIG, get_email_recipients_string, FILE_CONFIG
        print("✅ 配置导入成功")
        
        # 测试文件管理器导入
        from file_manager import FileManager
        print("✅ 文件管理器导入成功")
        
        # 测试文件管理器初始化
        fm = FileManager()
        print(f"✅ 文件管理器初始化成功，已处理目录: {fm.processed_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试宽表文件移动功能...")
    
    # 测试导入功能
    import_success = test_import_functionality()
    
    # 测试文件移动功能
    move_success = test_move_kuanbiao_file()
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    print(f"导入功能测试: {'✅ 通过' if import_success else '❌ 失败'}")
    print(f"文件移动测试: {'✅ 通过' if move_success else '❌ 失败'}")
    
    if import_success and move_success:
        print("\n🎉 所有测试通过！宽表文件移动功能工作正常。")
        print("📝 修改后的 DICT_download_local_kuanbiao_from_db2email.py 应该能够正常工作。")
    else:
        print("\n⚠️ 部分测试失败，请检查相关配置和依赖。")
    
    return import_success and move_success

if __name__ == "__main__":
    main()
