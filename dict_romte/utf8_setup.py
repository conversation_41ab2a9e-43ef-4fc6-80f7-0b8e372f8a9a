#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
utf8_setup.py - UTF-8编码设置模块
在其他脚本中导入此模块即可自动设置UTF-8编码
使用方法：在脚本开头添加 import utf8_setup
"""

import sys
import os

def setup_utf8_encoding():
    """设置UTF-8编码，解决Windows控制台中文乱码问题"""
    if sys.platform.startswith('win'):
        try:
            # 设置控制台代码页为UTF-8 (65001)
            os.system('chcp 65001 >nul 2>&1')
            
            # 设置环境变量
            os.environ['PYTHONIOENCODING'] = 'utf-8'
            os.environ['LANG'] = 'zh_CN.UTF-8'
            
            # 重新配置标准输出编码
            if hasattr(sys.stdout, 'reconfigure'):
                sys.stdout.reconfigure(encoding='utf-8')
                sys.stderr.reconfigure(encoding='utf-8')
            
            return True
        except Exception as e:
            print(f"设置UTF-8编码时出错: {e}")
            return False
    return True

# 自动执行设置
setup_utf8_encoding()
