#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
analyze_similarity_distribution.py - 分析项目内容相似度分布
功能：分析kuanbiao视图中项目建设内容的相似度分布情况，为阈值设定提供参考

作者：系统自动生成
创建时间：2025-07-30
"""

# 自动设置UTF-8编码，解决中文乱码问题
try:
    import utf8_setup
except ImportError:
    # 如果utf8_setup模块不存在，使用内置设置
    import sys, os
    if sys.platform.startswith('win'):
        os.system('chcp 65001 >nul 2>&1')
        os.environ['PYTHONIOENCODING'] = 'utf-8'

import pymysql
import pandas as pd
from datetime import datetime, timedelta
import traceback
import re
import hashlib
import math
from collections import Counter
from config import get_db_config

def preprocess_text_simple(text):
    """简化的文本预处理"""
    if pd.isna(text) or text == '' or text is None:
        return ""
    
    text = str(text).strip()
    
    # 移除特殊字符，保留中文、英文、数字
    text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', ' ', text)
    
    # 简单分词：按空格和常见分隔符分割
    words = re.split(r'[\s，。、；：！？]+', text)
    
    # 过滤短词和常见停用词
    stop_words = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这', '与', '及', '等', '为', '将', '通过', '进行', '实现', '建设', '系统', '项目', '方案', '内容', '包括', '主要', '采用', '提供', '服务', '管理', '平台', '软件', '硬件', '设备', '技术', '开发', '设计', '安装', '部署', '维护', '运营'}
    
    filtered_words = []
    for word in words:
        word = word.strip()
        if len(word) > 1 and word not in stop_words:
            filtered_words.append(word)
    
    return filtered_words

def calculate_jaccard_similarity(text1, text2):
    """计算Jaccard相似度"""
    words1 = set(preprocess_text_simple(text1))
    words2 = set(preprocess_text_simple(text2))
    
    if not words1 and not words2:
        return 1.0  # 两个空文本认为相似
    
    if not words1 or not words2:
        return 0.0  # 一个空一个非空认为不相似
    
    intersection = words1.intersection(words2)
    union = words1.union(words2)
    
    return len(intersection) / len(union) if union else 0.0

def calculate_cosine_similarity_simple(text1, text2):
    """简化的余弦相似度计算"""
    words1 = preprocess_text_simple(text1)
    words2 = preprocess_text_simple(text2)
    
    if not words1 and not words2:
        return 1.0
    
    if not words1 or not words2:
        return 0.0
    
    # 创建词频向量
    all_words = set(words1 + words2)
    
    vector1 = [words1.count(word) for word in all_words]
    vector2 = [words2.count(word) for word in all_words]
    
    # 计算余弦相似度
    dot_product = sum(a * b for a, b in zip(vector1, vector2))
    magnitude1 = math.sqrt(sum(a * a for a in vector1))
    magnitude2 = math.sqrt(sum(b * b for b in vector2))
    
    if magnitude1 == 0 or magnitude2 == 0:
        return 0.0
    
    return dot_product / (magnitude1 * magnitude2)

def calculate_text_similarity_simple(text1, text2):
    """计算文本相似度（结合多种算法）"""
    # 计算Jaccard相似度
    jaccard_sim = calculate_jaccard_similarity(text1, text2)
    
    # 计算余弦相似度
    cosine_sim = calculate_cosine_similarity_simple(text1, text2)
    
    # 取平均值作为最终相似度
    return (jaccard_sim + cosine_sim) / 2

def get_kuanbiao_data(conn):
    """获取kuanbiao视图数据"""
    try:
        print("[信息] 正在获取kuanbiao视图数据...")

        query = """
        SELECT
            `签约上报日期`,
            `项目编码`,
            `项目名称`,
            `收入侧客户`,
            `效益评估_概算__IT收入`,
            `项目建设内容及方案简介（CT）`,
            `项目建设内容及方案简介（IT）`
        FROM kuanbiao
        WHERE `收入侧客户` IS NOT NULL
        AND `收入侧客户` != ''
        AND `签约上报日期` IS NOT NULL
        AND `签约上报日期` != ''
        AND `效益评估_概算__IT收入` IS NOT NULL
        AND `效益评估_概算__IT收入` != ''
        AND (`项目建设内容及方案简介（CT）` IS NOT NULL OR `项目建设内容及方案简介（IT）` IS NOT NULL)
        """

        df = pd.read_sql(query, conn)
        print(f"[信息] 获取到 {len(df)} 条原始数据")
        
        # 数据去重
        print("[信息] 正在进行数据去重...")
        
        # 创建去重标识：基于项目编码+收入侧客户+签约上报日期
        df['去重标识'] = df.apply(lambda row: hashlib.md5(
            f"{row['项目编码']}_{row['收入侧客户']}_{row['签约上报日期']}".encode('utf-8')
        ).hexdigest(), axis=1)
        
        # 去重，保留第一条记录
        df_dedup = df.drop_duplicates(subset=['去重标识'], keep='first')
        print(f"[信息] 去重后剩余 {len(df_dedup)} 条有效数据")
        
        # 删除去重标识列
        df_dedup = df_dedup.drop('去重标识', axis=1)
        
        return df_dedup

    except Exception as e:
        print(f"[错误] 获取kuanbiao数据失败: {e}")
        traceback.print_exc()
        return None

def parse_date(date_str):
    """解析日期字符串，支持多种格式"""
    if pd.isna(date_str) or date_str == '' or date_str is None:
        return None
    
    date_str = str(date_str).strip()
    
    # 支持的日期格式
    date_formats = [
        '%Y-%m-%d',
        '%Y/%m/%d', 
        '%Y年%m月%d日',
        '%Y-%m-%d %H:%M:%S',
        '%Y/%m/%d %H:%M:%S'
    ]
    
    for fmt in date_formats:
        try:
            return datetime.strptime(date_str, fmt).date()
        except ValueError:
            continue
    
    return None

def parse_amount(amount_str):
    """解析金额字符串，转换为数值"""
    if pd.isna(amount_str) or amount_str == '' or amount_str is None:
        return 0.0
    
    amount_str = str(amount_str).strip()
    
    # 移除非数字字符（保留小数点和负号）
    amount_str = re.sub(r'[^\d.-]', '', amount_str)
    
    try:
        return float(amount_str)
    except ValueError:
        return 0.0

def analyze_similarity_distribution():
    """分析相似度分布"""
    try:
        # 连接数据库
        print("1. 连接数据库...")
        db_config = get_db_config('remote')
        conn = pymysql.connect(**db_config)
        print(f"[成功] 已连接到数据库: {db_config['host']}:{db_config['port']}")
        
        # 获取数据
        print("\n2. 获取kuanbiao视图数据...")
        df = get_kuanbiao_data(conn)
        if df is None or len(df) == 0:
            print("[错误] 获取数据失败或数据为空")
            return False
        
        # 数据预处理
        df['签约日期'] = df['签约上报日期'].apply(parse_date)
        df['IT收入'] = df['效益评估_概算__IT收入'].apply(parse_amount)
        
        # 过滤掉无效数据
        df = df[df['签约日期'].notna() & (df['IT收入'] > 0)]
        
        print(f"[信息] 有效数据条数: {len(df)}")
        
        # 分析相似度分布
        print("\n3. 分析相似度分布...")
        
        similarity_results = []
        customer_groups = df.groupby('收入侧客户')
        
        for customer, customer_df in customer_groups:
            if len(customer_df) < 2:  # 至少需要2个项目
                continue
                
            # 筛选单个项目IT收入不超过400万的项目
            small_projects_df = customer_df[customer_df['IT收入'] <= 400]
            
            if len(small_projects_df) < 2:
                continue
            
            # 按签约日期排序
            small_projects_df = small_projects_df.sort_values('签约日期').reset_index(drop=True)
            
            # 使用滑动窗口检测
            for i in range(len(small_projects_df)):
                start_date = small_projects_df.iloc[i]['签约日期']
                end_date = start_date + timedelta(days=90)  # 3个月
                
                # 获取时间窗口内的项目
                window_projects = small_projects_df[
                    (small_projects_df['签约日期'] >= start_date) & 
                    (small_projects_df['签约日期'] <= end_date)
                ]
                
                if len(window_projects) < 2:
                    continue
                
                # 计算IT收入累加值
                total_it_income = window_projects['IT收入'].sum()
                
                # 只分析IT收入超过400万的组合
                if total_it_income > 400:
                    # 获取CT和IT内容
                    ct_contents = window_projects['项目建设内容及方案简介（CT）'].fillna('').tolist()
                    it_contents = window_projects['项目建设内容及方案简介（IT）'].fillna('').tolist()
                    
                    # 计算CT相似度
                    ct_similarities = []
                    for j in range(len(ct_contents)):
                        for k in range(j+1, len(ct_contents)):
                            similarity = calculate_text_similarity_simple(ct_contents[j], ct_contents[k])
                            ct_similarities.append(similarity)
                    
                    # 计算IT相似度
                    it_similarities = []
                    for j in range(len(it_contents)):
                        for k in range(j+1, len(it_contents)):
                            similarity = calculate_text_similarity_simple(it_contents[j], it_contents[k])
                            it_similarities.append(similarity)
                    
                    # 统计结果
                    ct_avg = (sum(ct_similarities) / len(ct_similarities)) * 100 if ct_similarities else 0
                    it_avg = (sum(it_similarities) / len(it_similarities)) * 100 if it_similarities else 0
                    ct_max = max(ct_similarities) * 100 if ct_similarities else 0
                    it_max = max(it_similarities) * 100 if it_similarities else 0
                    
                    similarity_results.append({
                        '客户名称': customer,
                        '项目数量': len(window_projects),
                        'IT收入累加值': total_it_income,
                        'CT平均相似度': ct_avg,
                        'IT平均相似度': it_avg,
                        'CT最高相似度': ct_max,
                        'IT最高相似度': it_max
                    })
        
        # 输出分析结果
        print("\n" + "=" * 80)
        print("相似度分布分析结果")
        print("=" * 80)
        
        if not similarity_results:
            print("没有找到IT收入超过400万的项目组合")
            return True
        
        print(f"总分析组合数: {len(similarity_results)}")
        
        # 统计相似度分布
        ct_similarities = [r['CT平均相似度'] for r in similarity_results]
        it_similarities = [r['IT平均相似度'] for r in similarity_results]
        
        print(f"\nCT相似度统计:")
        print(f"  平均值: {sum(ct_similarities)/len(ct_similarities):.1f}%")
        print(f"  最大值: {max(ct_similarities):.1f}%")
        print(f"  最小值: {min(ct_similarities):.1f}%")
        print(f"  ≥80%的组合数: {len([s for s in ct_similarities if s >= 80])}")
        print(f"  ≥60%的组合数: {len([s for s in ct_similarities if s >= 60])}")
        print(f"  ≥40%的组合数: {len([s for s in ct_similarities if s >= 40])}")
        
        print(f"\nIT相似度统计:")
        print(f"  平均值: {sum(it_similarities)/len(it_similarities):.1f}%")
        print(f"  最大值: {max(it_similarities):.1f}%")
        print(f"  最小值: {min(it_similarities):.1f}%")
        print(f"  ≥80%的组合数: {len([s for s in it_similarities if s >= 80])}")
        print(f"  ≥60%的组合数: {len([s for s in it_similarities if s >= 60])}")
        print(f"  ≥40%的组合数: {len([s for s in it_similarities if s >= 40])}")
        
        # 同时满足条件的统计
        print(f"\n同时满足条件的组合数:")
        both_80 = len([r for r in similarity_results if r['CT平均相似度'] >= 80 and r['IT平均相似度'] >= 80])
        both_60 = len([r for r in similarity_results if r['CT平均相似度'] >= 60 and r['IT平均相似度'] >= 60])
        both_40 = len([r for r in similarity_results if r['CT平均相似度'] >= 40 and r['IT平均相似度'] >= 40])
        
        print(f"  CT≥80% 且 IT≥80%: {both_80}")
        print(f"  CT≥60% 且 IT≥60%: {both_60}")
        print(f"  CT≥40% 且 IT≥40%: {both_40}")
        
        # 显示高相似度案例
        print(f"\n高相似度案例（前10名）:")
        print("-" * 80)
        
        # 按CT+IT平均相似度排序
        similarity_results.sort(key=lambda x: x['CT平均相似度'] + x['IT平均相似度'], reverse=True)
        
        for i, result in enumerate(similarity_results[:10], 1):
            print(f"{i:2d}. {result['客户名称']}")
            print(f"    项目数量: {result['项目数量']} | IT收入: {result['IT收入累加值']:.2f}万元")
            print(f"    CT相似度: {result['CT平均相似度']:.1f}% | IT相似度: {result['IT平均相似度']:.1f}%")
            print()
        
        print("=" * 80)
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"[错误] 分析失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = analyze_similarity_distribution()
    if success:
        print("\n[成功] 相似度分布分析完成")
    else:
        print("\n[失败] 相似度分布分析失败")
