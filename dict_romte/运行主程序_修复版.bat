@echo off
:: ========================================
:: DICT Spider Main Program Launcher
:: Fixed version to solve encoding issues
:: ========================================

:: Set console code page to UTF-8 (936 for GBK, 65001 for UTF-8)
chcp 65001 >nul 2>&1

:: Set Python encoding environment variables
set PYTHONIOENCODING=utf-8
set LANG=zh_CN.UTF-8
set PYTHONLEGACYWINDOWSSTDIO=utf-8

:: Clear screen
cls

:: Show header in English to avoid encoding issues
echo ========================================
echo DICT Spider Main Program Launcher
echo Fixed version - No encoding issues
echo ========================================
echo.

:: Check if we are in the correct directory
if not exist "main_dict_local.py" (
    echo ERROR: main_dict_local.py not found
    echo Please make sure you are in the dict_romte directory
    echo Current directory: %CD%
    echo.
    echo Available Python files:
    dir *.py /b 2>nul
    echo.
    pause
    exit /b 1
)

:: Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python and add it to PATH
    pause
    exit /b 1
)

:: Show current directory and Python version
echo Current directory: %CD%
python --version
echo.

:: Run the main program with proper encoding
echo Starting main program...
echo ========================================
echo.

:: Use python with explicit encoding settings
python -u main_dict_local.py

:: Check exit code
set EXIT_CODE=%errorlevel%
echo.
echo ========================================
if %EXIT_CODE% equ 0 (
    echo Program execution completed successfully
) else (
    echo Program execution failed with exit code: %EXIT_CODE%
)
echo ========================================
echo.

:: Show completion message
echo Press any key to exit...
pause >nul
