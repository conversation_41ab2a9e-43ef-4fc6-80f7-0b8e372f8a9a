#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
analyze_project_codes.py - 分析dict_prj_yisi_chaifen表的项目编码列表字段
查看数据格式，为创建拆分视图做准备
"""

import pymysql
import pandas as pd
from config import get_db_config

def analyze_project_codes_data():
    """分析项目编码列表数据"""
    try:
        # 连接数据库
        db_config = get_db_config('remote')
        conn = pymysql.connect(**db_config)
        
        print("[信息] 正在分析dict_prj_yisi_chaifen表的项目编码列表...")
        
        # 查询表结构
        print("\n=== 表结构分析 ===")
        cursor = conn.cursor()
        cursor.execute("DESCRIBE dict_prj_yisi_chaifen")
        columns = cursor.fetchall()
        
        print("字段列表：")
        for col in columns:
            print(f"  {col[0]} - {col[1]} - {col[2] if col[2] == 'YES' else 'NOT NULL'}")
        
        # 查询数据总量
        cursor.execute("SELECT COUNT(*) FROM dict_prj_yisi_chaifen")
        total_count = cursor.fetchone()[0]
        print(f"\n总记录数: {total_count}")
        
        if total_count == 0:
            print("[警告] 表中没有数据")
            return
        
        # 查询前5条记录的项目编码列表
        print("\n=== 项目编码列表样本数据 ===")
        query = """
        SELECT 
            id,
            客户名称,
            涉及项目数量,
            项目编码列表,
            项目名称列表
        FROM dict_prj_yisi_chaifen 
        ORDER BY id 
        LIMIT 5
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        
        for i, row in enumerate(results, 1):
            print(f"\n--- 记录 {i} ---")
            print(f"ID: {row[0]}")
            print(f"客户名称: {row[1]}")
            print(f"涉及项目数量: {row[2]}")
            print(f"项目编码列表: {row[3]}")
            print(f"项目名称列表: {row[4][:100]}..." if len(str(row[4])) > 100 else f"项目名称列表: {row[4]}")
            
            # 分析项目编码格式
            if row[3]:
                codes = str(row[3]).split(',')
                print(f"拆分后的项目编码数量: {len(codes)}")
                print(f"前3个项目编码: {codes[:3]}")
        
        # 统计项目编码列表的特征
        print("\n=== 项目编码列表统计分析 ===")
        stats_query = """
        SELECT 
            MIN(涉及项目数量) as 最少项目数,
            MAX(涉及项目数量) as 最多项目数,
            AVG(涉及项目数量) as 平均项目数,
            COUNT(DISTINCT 客户名称) as 涉及客户数
        FROM dict_prj_yisi_chaifen
        """
        
        cursor.execute(stats_query)
        stats = cursor.fetchone()
        print(f"最少项目数: {stats[0]}")
        print(f"最多项目数: {stats[1]}")
        print(f"平均项目数: {stats[2]:.2f}")
        print(f"涉及客户数: {stats[3]}")
        
        # 查看项目编码列表长度分布
        print("\n=== 项目编码列表长度分布 ===")
        length_query = """
        SELECT 
            涉及项目数量,
            COUNT(*) as 记录数
        FROM dict_prj_yisi_chaifen
        GROUP BY 涉及项目数量
        ORDER BY 涉及项目数量
        """
        
        cursor.execute(length_query)
        length_dist = cursor.fetchall()
        
        for row in length_dist:
            print(f"项目数量 {row[0]}: {row[1]} 条记录")
        
        cursor.close()
        conn.close()
        
        print("\n[成功] 数据分析完成")
        
    except Exception as e:
        print(f"[错误] 数据分析失败: {e}")
        import traceback
        traceback.print_exc()

def test_split_logic():
    """测试拆分逻辑"""
    print("\n=== 测试项目编码拆分逻辑 ===")
    
    # 模拟数据
    test_data = [
        {
            'id': 1,
            '客户名称': '测试客户A',
            '项目编码列表': 'PROJ001,PROJ002,PROJ003',
            '项目名称列表': '项目A,项目B,项目C',
            'IT收入累加值': 150.50
        },
        {
            'id': 2,
            '客户名称': '测试客户B',
            '项目编码列表': 'PROJ004,PROJ005',
            '项目名称列表': '项目D,项目E',
            'IT收入累加值': 200.00
        }
    ]
    
    print("原始数据:")
    for data in test_data:
        print(f"  {data}")
    
    print("\n拆分后的数据:")
    expanded_data = []
    
    for data in test_data:
        codes = data['项目编码列表'].split(',')
        names = data['项目名称列表'].split(',')
        
        for i, code in enumerate(codes):
            expanded_row = {
                '原记录ID': data['id'],
                '客户名称': data['客户名称'],
                '项目编码': code.strip(),
                '项目名称': names[i].strip() if i < len(names) else '',
                'IT收入累加值': data['IT收入累加值'],
                '项目序号': i + 1,
                '总项目数': len(codes)
            }
            expanded_data.append(expanded_row)
            print(f"  {expanded_row}")
    
    print(f"\n拆分结果: 原始 {len(test_data)} 条记录 -> 拆分后 {len(expanded_data)} 条记录")

if __name__ == "__main__":
    print("dict_prj_yisi_chaifen 项目编码列表分析工具")
    print("=" * 60)
    
    # 分析实际数据
    analyze_project_codes_data()
    
    # 测试拆分逻辑
    test_split_logic()
