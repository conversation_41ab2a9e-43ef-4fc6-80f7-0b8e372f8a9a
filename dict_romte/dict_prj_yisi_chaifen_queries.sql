-- 疑似拆分立项检测结果表查询SQL脚本
-- 表名：dict_prj_yisi_chaifen
-- 创建时间：2025-07-30

-- ========================================
-- 1. 查看表结构
-- ========================================
DESCRIBE dict_prj_yisi_chaifen;

-- ========================================
-- 2. 基本统计查询
-- ========================================

-- 总体统计
SELECT 
    COUNT(*) as 总检测结果数,
    COUNT(DISTINCT 客户名称) as 涉及客户数,
    AVG(IT收入累加值) as 平均IT收入累加值,
    MAX(IT收入累加值) as 最大IT收入累加值,
    MIN(IT收入累加值) as 最小IT收入累加值
FROM dict_prj_yisi_chaifen;

-- 按风险等级统计
SELECT 
    风险等级,
    COUNT(*) as 数量,
    ROUND(AVG(IT收入累加值), 2) as 平均IT收入,
    ROUND(MAX(IT收入累加值), 2) as 最大IT收入,
    ROUND(MIN(IT收入累加值), 2) as 最小IT收入,
    ROUND(AVG(涉及项目数量), 1) as 平均项目数量
FROM dict_prj_yisi_chaifen 
GROUP BY 风险等级 
ORDER BY FIELD(风险等级, '高', '中', '低');

-- ========================================
-- 3. 高风险客户排行榜
-- ========================================

-- 按IT收入累加值排序的高风险客户
SELECT 
    客户名称,
    COUNT(*) as 检测次数,
    ROUND(SUM(IT收入累加值), 2) as 总IT收入累加值,
    ROUND(AVG(IT收入累加值), 2) as 平均IT收入累加值,
    MAX(风险等级) as 最高风险等级,
    SUM(涉及项目数量) as 总涉及项目数
FROM dict_prj_yisi_chaifen 
GROUP BY 客户名称 
ORDER BY 总IT收入累加值 DESC
LIMIT 20;

-- ========================================
-- 4. 时间分布分析
-- ========================================

-- 按月份统计检测结果
SELECT 
    DATE_FORMAT(时间窗口开始, '%Y-%m') as 月份,
    COUNT(*) as 检测数量,
    ROUND(AVG(IT收入累加值), 2) as 平均IT收入,
    COUNT(DISTINCT 客户名称) as 涉及客户数
FROM dict_prj_yisi_chaifen 
GROUP BY DATE_FORMAT(时间窗口开始, '%Y-%m')
ORDER BY 月份;

-- ========================================
-- 5. 详细检测结果查询
-- ========================================

-- 查看所有高风险检测结果
SELECT 
    客户名称,
    时间窗口开始,
    时间窗口结束,
    涉及项目数量,
    ROUND(IT收入累加值, 2) as IT收入累加值,
    ROUND(单项目最大IT收入, 2) as 单项目最大IT收入,
    风险等级,
    LEFT(项目编码列表, 100) as 项目编码前100字符,
    LEFT(项目名称列表, 100) as 项目名称前100字符
FROM dict_prj_yisi_chaifen 
WHERE 风险等级 = '高'
ORDER BY IT收入累加值 DESC;

-- 查看中风险检测结果
SELECT 
    客户名称,
    时间窗口开始,
    时间窗口结束,
    涉及项目数量,
    ROUND(IT收入累加值, 2) as IT收入累加值,
    风险等级
FROM dict_prj_yisi_chaifen 
WHERE 风险等级 = '中'
ORDER BY IT收入累加值 DESC;

-- ========================================
-- 6. 特定客户查询示例
-- ========================================

-- 查询特定客户的所有检测结果（示例：公安局相关）
SELECT 
    客户名称,
    时间窗口开始,
    时间窗口结束,
    涉及项目数量,
    ROUND(IT收入累加值, 2) as IT收入累加值,
    风险等级,
    检测时间
FROM dict_prj_yisi_chaifen 
WHERE 客户名称 LIKE '%公安局%'
ORDER BY IT收入累加值 DESC;

-- ========================================
-- 7. 项目数量分析
-- ========================================

-- 按涉及项目数量分组统计
SELECT 
    涉及项目数量,
    COUNT(*) as 检测结果数,
    ROUND(AVG(IT收入累加值), 2) as 平均IT收入,
    COUNT(DISTINCT 客户名称) as 客户数
FROM dict_prj_yisi_chaifen 
GROUP BY 涉及项目数量
ORDER BY 涉及项目数量;

-- ========================================
-- 8. 时间窗口分析
-- ========================================

-- 分析时间窗口长度分布
SELECT 
    DATEDIFF(时间窗口结束, 时间窗口开始) as 时间窗口天数,
    COUNT(*) as 检测结果数,
    ROUND(AVG(IT收入累加值), 2) as 平均IT收入
FROM dict_prj_yisi_chaifen 
GROUP BY DATEDIFF(时间窗口结束, 时间窗口开始)
ORDER BY 时间窗口天数;

-- ========================================
-- 9. 风险预警查询
-- ========================================

-- 超高风险预警（IT收入累加值超过1000万）
SELECT 
    '超高风险预警' as 预警类型,
    客户名称,
    ROUND(IT收入累加值, 2) as IT收入累加值,
    涉及项目数量,
    时间窗口开始,
    时间窗口结束
FROM dict_prj_yisi_chaifen 
WHERE IT收入累加值 > 1000
ORDER BY IT收入累加值 DESC;

-- 项目数量异常预警（单个时间窗口涉及项目超过5个）
SELECT 
    '项目数量异常预警' as 预警类型,
    客户名称,
    涉及项目数量,
    ROUND(IT收入累加值, 2) as IT收入累加值,
    时间窗口开始,
    时间窗口结束
FROM dict_prj_yisi_chaifen 
WHERE 涉及项目数量 > 5
ORDER BY 涉及项目数量 DESC;

-- ========================================
-- 10. 数据清理和维护查询
-- ========================================

-- 检查重复记录
SELECT 
    客户名称,
    时间窗口开始,
    时间窗口结束,
    COUNT(*) as 重复次数
FROM dict_prj_yisi_chaifen 
GROUP BY 客户名称, 时间窗口开始, 时间窗口结束
HAVING COUNT(*) > 1;

-- 检查数据完整性
SELECT 
    '数据完整性检查' as 检查项,
    SUM(CASE WHEN 客户名称 IS NULL OR 客户名称 = '' THEN 1 ELSE 0 END) as 客户名称为空,
    SUM(CASE WHEN IT收入累加值 IS NULL OR IT收入累加值 <= 0 THEN 1 ELSE 0 END) as IT收入异常,
    SUM(CASE WHEN 涉及项目数量 IS NULL OR 涉及项目数量 <= 0 THEN 1 ELSE 0 END) as 项目数量异常,
    SUM(CASE WHEN 风险等级 NOT IN ('高', '中', '低') THEN 1 ELSE 0 END) as 风险等级异常
FROM dict_prj_yisi_chaifen;

-- ========================================
-- 11. 导出用查询
-- ========================================

-- 完整数据导出查询
SELECT 
    id,
    客户名称,
    时间窗口开始,
    时间窗口结束,
    涉及项目数量,
    项目编码列表,
    项目名称列表,
    ROUND(IT收入累加值, 2) as IT收入累加值,
    ROUND(单项目最大IT收入, 2) as 单项目最大IT收入,
    风险等级,
    检测时间,
    备注
FROM dict_prj_yisi_chaifen 
ORDER BY IT收入累加值 DESC;

-- ========================================
-- 12. 业务分析查询
-- ========================================

-- 分析单项目最大IT收入与累加值的关系
SELECT 
    客户名称,
    ROUND(IT收入累加值, 2) as IT收入累加值,
    ROUND(单项目最大IT收入, 2) as 单项目最大IT收入,
    ROUND((IT收入累加值 - 单项目最大IT收入), 2) as 其他项目IT收入,
    ROUND((单项目最大IT收入 / IT收入累加值) * 100, 1) as 最大项目占比,
    涉及项目数量
FROM dict_prj_yisi_chaifen 
WHERE IT收入累加值 > 500
ORDER BY 最大项目占比 DESC;
