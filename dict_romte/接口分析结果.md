# 项目综合查询接口分析结果

## 1. saleCenterApp/common/dataDictService/batchLoadCodeList
**中文名称**: 批量加载数据字典代码列表

### 1.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/common/dataDictService/batchLoadCodeList`
**方法**: POST
**调用次数**: 19

### 1.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 262
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": [
      {
        "TABLE_EN_NAME": "PLAN_CHANGE_CONFIRM",
        "FIELD_EN_NAME": "PATH_NAME"
      },
      {
        "TABLE_EN_NAME": "PAYMENT_ACCURACY",
        "FIELD_EN_NAME": "OPEN_FLAG"
      },
      {
        "TABLE_EN_NAME": "RISK_NOTICE",
        "FIELD_EN_NAME": "RISK_NOTICE"
      }
    ]
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 1.3 响应结果
**状态码**: 200 
**响应大小**: 1046 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:10 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184210945_1_1514",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "PAYMENT_ACCURACY": {
          "OPEN_FLAG": [
            {
              "FIELD_VALUE": "N",
              "FIELD_DESC": "Y开N关",
              "FIELD_URL": "",
              "SHOW_ORDER": 0
            }
          ]
        },
        "RISK_NOTICE": {
          "RISK_NOTICE": [
            {
              "FIELD_VALUE": "MENU_NAME",
              "FIELD_DESC": "incomeRecognitionCancel,incomePlanStatistics,devsReportManager,projectAccountingInfo,projectAllIncomeManage,realTimeInvoicingList,arrearageDetail,projectTimeEfficient,nineOneProjectDetailReport,provinceNineOneProjectReport,selfPowerAssessmentDetailReport,selfPowerAssessmentStatisticsReport,iframeAnalysis",
              "FIELD_URL": "",
              "STATUS_CODE": "",
              "SHOW_ORDER": 0
            }
          ]
        },
        "PLAN_CHANGE_CONFIRM": {
          "PATH_NAME": [
            {
              "FIELD_VALUE": "contractManage,incomePlanChangeNew,planChangeConfirm,contractAmountSettlementAdd,expenditureRevenvePlanChange",
              "FIELD_DESC": "",
              "FIELD_URL": "",
              "STATUS_CODE": "",
              "SHOW_ORDER": 0
            }
          ]
        }
      },
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 2. iom-app-svc/iom/api/wo/getTodo
**中文名称**: 获取待办工单

### 2.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/iom-app-svc/iom/api/wo/getTodo`
**方法**: POST
**调用次数**: 3

### 2.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 189
Content-Type: application/json;charset=UTF-8
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "BODY": {
      "BUSI_INFO": {
        "loginNo": "liaochulin",
        "stepId": "6391",
        "prodId": "GD_CYJHBGQR",
        "pageSize": 10,
        "pageNum": 1,
        "opCodes": [
          "GD_DICT",
          "GD_HZHB"
        ]
      },
      "OPR_INFO": {
        "loginNo": "liaochulin"
      }
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `LOGIN_NO/loginNo` | 用户登录Session信息 |
| `pageSize/pageNum` | 前端分页组件默认值 |
| `stepId/prodId` | 工单系统配置参数 |

### 2.3 响应结果
**状态码**: 200 
**响应大小**: 511 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:11 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A"
    },
    "BODY": {
      "RUN_IP": "**************",
      "REQUEST_ID": "20250714184211000_1_948",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "pageNum": 1,
        "pageSize": 10,
        "size": 0,
        "startRow": 0,
        "endRow": 0,
        "total": 0,
        "pages": 0,
        "list": [],
        "prePage": 0,
        "nextPage": 0,
        "isFirstPage": true,
        "isLastPage": true,
        "hasPreviousPage": false,
        "hasNextPage": false,
        "navigatePages": 8,
        "navigatepageNums": [],
        "navigateFirstPage": 0,
        "navigateLastPage": 0,
        "firstPage": 0,
        "lastPage": 0
      },
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 3. saleCenterApp/projectManage/queryProjectInfo
**中文名称**: 查询项目基本信息

### 3.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectManage/queryProjectInfo`
**方法**: POST
**调用次数**: 20

### 3.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 103
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 3.3 响应结果
**状态码**: 200 
**响应大小**: 2903 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:11 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184211962_1_1561",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "PROJECT_NO": "CMGDZSICT20230614042",
        "PROJECT_NAME": "东区街道起湾社区三线整治服务项目",
        "BBOSS_PROJECT_ID": "I23302126601114",
        "PROJECT_TYPE": "10",
        "PROJECT_TYPE_DESC": "DICT项目",
        "TRADE": "NS",
        "TRADE_DESC": "农商",
        "IS_BRING_INTO_PMO": "0",
        "PROJECT_CONTRACT_TYPE": "3000",
        "PROJECT_CONTRACT_TYPE_NAME": "普通立项",
        "IS_INVESTMENT_PROJECT": "0",
        "PROJECT_LABLE": "20",
        "PROJECT_LABLE_NAME": "ICT业务-5G专网",
        "SALE_OPP_ID": "76023060810091275",
        "CUST_ID": "87600000143547",
        "PROJECT_ID": "CMGDZSICT20230614042",
        "ESTIMATED_CON_PERIOD": "1",
        "IS_CONNECTED_OPP": "1",
        "CUST_NAME": "江苏瑞英达通信工程有限公司",
        "REGION_CODE": "760",
        "REGION_CODE_DESC": "中山分公司",
        "FIRST_SCENE": "NS_ZHNL",
        "SECOND_SCENE": "202305171436320004",
        "PROJECT_STAGE": "1002",
        "PROJECT_STAGE_NAME": "售中阶段",
        "PROJECT_PROGRESS": "2029",
        "PROJECT_PROGRESS_NAME": "项目终验",
        "ESTIMATED_AMOUNT": "281.00",
        "PROJECT_SCOPE": "1001",
        "REQUIREMENTS_TITEL": "东区街道起湾社区三线整治服务项目",
        "GROUP_ID": "2001330585",
        "BUILD_MODE": "1001",
        "BUILD_MODE_DESC": "纯建设",
        "ELECTION_MODE": "1030",
        "IS_BIG_PROJECT": "1000",
        "IS_EXCELLENT_PROJECT": "1000",
        "PARENT_PROJECT_ID": "0",
        "IS_SPEC_MARKET": "1001",
        "IS_MAINTENANCE": "20",
        "PROJECT_STATUS": "1000",
        "PROJECT_SOURCE": "1000",
        "BID_FLAG": "0",
        "ORG_DESC": "党政和行业拓展室",
        "BID_FLAG_NAME": "否",
        "FLOW_TYPE
... (响应内容过长，已截断)
```

---

## 4. saleCenterApp/common/dataDictService/loadCodeList
**中文名称**: 加载数据字典代码列表

### 4.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/common/dataDictService/loadCodeList`
**方法**: POST
**调用次数**: 44

### 4.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 128
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "TABLE_EN_NAME": "PROJECT",
      "FIELD_EN_NAME": "OPERATION_MANUAL"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `LOGIN_NO/loginNo` | 用户登录Session信息 |
| `TABLE_EN_NAME/FIELD_EN_NAME` | 前端硬编码的数据字典配置 |

### 4.3 响应结果
**状态码**: 200 
**响应大小**: 946 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:11 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184211961_1_543",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "OUT_PARAM": [
          {
            "FIELD_VALUE": "baseUrl",
            "FIELD_DESC": "根路径",
            "FIELD_URL": "http://*************:30721/web/#/5",
            "SHOW_ORDER": 0
          },
          {
            "FIELD_VALUE": "incomeRecognitionCheck",
            "FIELD_DESC": "收入确认申请",
            "FIELD_URL": "http://*************:30721/web/#/5/30",
            "SHOW_ORDER": 0
          },
          {
            "FIELD_VALUE": "planFormulation",
            "FIELD_DESC": "方案制定",
            "FIELD_URL": "http://*************:30721/web/#/5/29",
            "SHOW_ORDER": 0
          },
          {
            "FIELD_VALUE": "productOrder",
            "FIELD_DESC": "产品订购",
            "FIELD_URL": "http://*************:30721/web/#/5/31",
            "SHOW_ORDER": 0
          },
          {
            "FIELD_VALUE": "projectDisclosureInit",
            "FIELD_DESC": "项目交底发起",
            "FIELD_URL": "http://*************:30721/web/#/5/33",
            "SHOW_ORDER": 0
          }
        ]
      },
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 5. saleCenterApp/formulation/queryProjectDemand
**中文名称**: 查询项目需求信息

### 5.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/formulation/queryProjectDemand`
**方法**: POST
**调用次数**: 14

### 5.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 103
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 5.3 响应结果
**状态码**: 200 
**响应大小**: 3996 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:12 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184212146_1_1332",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "SUPPORT_APPLICATION_TIME": "2023-06-14 16:09:32",
        "REGION_CODE": "760",
        "GOV_ENTER_CENTER": "",
        "GRID_MEMBER": "",
        "MAIN_PROCESS_TYPE": "1001",
        "MAIN_PROCESS_TYPE_DESC": "5G",
        "PROJECT_CONTRACT_TYPE": "3000",
        "PROJECT_CONTRACT_TYPE_NAME": "普通立项",
        "ELECTION_MODE": "1030",
        "ELECTION_MODE_NAME": "单一来源采购",
        "CONTRACT_PERIOD": "1",
        "CONTRACT_DURATION": "4",
        "IT_REQUIREMENTS_DESC": "该项目面向基层乡镇治理场景，为后期打造一体的5G数字社区综合服务应用打下基础，包括乡村安防管控、综合治理等场景需求。具体内容：前期起湾社区光缆路由管道建设，运营商融合光缆架空敷设及管道敷设施工，融合光缆交接箱建设以及广电网络光缆敷设施工，后期利用5G网络优势，为社区建立5G专网，以及5G双域专网，为社区提供惠农服务移动端入口，为政府人员提供基层治理工具，借助5G低时延、大带宽特性，提供乡村综治等服务。",
        "CT_REQUIREMENTS_DESC": "提供数字电路一年，5G双域专网、5G专网免费体验半年。",
        "IS_CONNECTED_OPP": "1",
        "CUST_ID": "87600000143547",
        "ESTIMATED_AMOUNT": "281.00",
        "PROJECT_NAME": "东区街道起湾社区三线整治服务项目",
        "REGION_CODE_DESC": "中山分公司",
        "REQUIREMENTS_TITEL": "东区街道起湾社区三线整治服务项目",
        "ORG_DESC": "党政和行业拓展室",
        "SALE_OPP_ID": "76023060810091275",
        "THE_DEGREE_OF_URGENCY": "",
        "BID_FLAG": "0",
        "BID_FLAG_NAME": "否",
        "YS_FLAG": "",
        "GW_NAME": "",
        "GW_NO": "",
        "PROJECT_TYPE": "10",
        "PRE_SOLUTION_FINISH_TIME": "2023-06-19 00:00:00",
        "DISCLOSURE_START_TIME": "2023-06-25 00:00:00",
        "DISCLOSURE_FINISH_TIME": "2023-06-25 00:00:00",
        "PM_PUBM_COUNTY_INFO_OUT": {
          "PUBM_COUNTY_ID": "ZS01",
          "PUBM_COUNTY_NAME": "城区分公司",
          "REGION_ID": "760",
          "ONEDICT_REGION_ID": "7600",
          "
... (响应内容过长，已截断)
```

---

## 6. iom-app-svc/iom/api/wo/qryWoListByProject
**中文名称**: 按项目查询工单列表

### 6.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/iom-app-svc/iom/api/wo/qryWoListByProject`
**方法**: POST
**调用次数**: 17

### 6.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 161
Content-Type: application/json;charset=UTF-8
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "BODY": {
      "BUSI_INFO": {
        "projectId": "CMGDZSICT20230614042",
        "commodityID": "GD_DICT01",
        "qryType": "1",
        "currPage": 1,
        "pageSize": 99,
        "pageNum": 1
      },
      "OPR_INFO": {}
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `pageSize/pageNum` | 前端分页组件默认值 |

### 6.3 响应结果
**状态码**: 200 
**响应大小**: 9042 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:14 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A"
    },
    "BODY": {
      "RUN_IP": "**************",
      "REQUEST_ID": "20250714184214623_1_987",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "pageNum": 1,
        "pageSize": 99,
        "size": 7,
        "startRow": 1,
        "endRow": 7,
        "total": 7,
        "pages": 1,
        "list": [
          {
            "woNbr": "1223062600000185",
            "soNbr": "GD23062600000141",
            "localNetId": "1",
            "groupId": "760",
            "workAreaId": "",
            "orderLogin": "limiaona",
            "servDeptId": -1,
            "branchId": -1,
            "workItemId": 823062600004423,
            "nWoNbr": "",
            "relaWoNbr": "",
            "collabWoNbr": "",
            "stepId": "2021",
            "azFlag": "N",
            "dynFlag": "N",
            "dealFlag": "3",
            "woType": "R",
            "arFlag": "A",
            "actType": "A",
            "workMode": "N",
            "doTimes": 3,
            "priority": 0,
            "mainFlag": "W",
            "mergFlag": "W",
            "userId": "10000",
            "bookFlag": "N",
            "bookedFlag": "N",
            "notifyFlag": "N",
            "opCode": "GD_DICT",
            "processLogin": "zengxiaomin3",
            "acceptLogin": "zengxiaomin3",
            "note": "进行 项目交底审核 操作,项目交底审核接收",
            "createDate": "2023-06-26 10:39:43",
            "preAlarmDate": "2023-07-05 10:00:00",
            "alarmDate": "2023-07-05 10:00:00",
            "complDate": "2023-06-26 10:40:12",
            "runSts": "C",
            "runStsDate": "2023-06-26 10:40:12",
            "busiSts": "S",
            "busiStsDate": "2023-06-26 10:39:43",
            "remarks": "无",
            "workSystem": "NONE",
            "maintAreaId": -1,
            "tenantId": "1",
            "overTimeDate": "2023-07-06 17:00:00",
    
... (响应内容过长，已截断)
```

---

## 7. saleCenterApp/contractManage/qryContractByProject
**中文名称**: 按项目查询合同信息

### 7.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/contractManage/qryContractByProject`
**方法**: POST
**调用次数**: 3

### 7.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 157
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "IN_PARAM": {
        "PROJECT_ID": "CMGDZSICT20230614042"
      },
      "PAGE_INFO": {
        "PAGE_NUM": 1,
        "PAGE_SIZE": 5
      }
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 7.3 响应结果
**状态码**: 200 
**响应大小**: 6375 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:12 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184212469_1_1321",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "OUT_PARAM": [
          {
            "CONTRACT_ID": "14217230",
            "PROJECT_ID": "CMGDZSICT20230614042",
            "CONTRACT_NO": "CMGD-ZS-202300950",
            "CONTRACT_NAME": "起湾社区5G智慧改造工程项目合同书",
            "CONTRACT_SERIAL_NO": "00212023039076",
            "DRAFT_TYPE": "OUT",
            "RETROACTIVE": "N",
            "SUPERIOR_SIGNATURE": "N",
            "CONTRACT_SUBJECT": "ERP",
            "CON_SIGNED_SUBJECT": "760",
            "INC_EXP_TYPE": "BALANCE",
            "CONTACT_TEL": "18824950008",
            "SIGN_TIME": "2023-06-22 08:00:00",
            "SIGNED_START_DATE": "2023-06-22 00:00:00",
            "SIGNED_END_DATE": "2024-06-21 00:00:00",
            "CONTRACT_PERIOD": "12",
            "CONTRACT_MONEY": 2810525.0,
            "CONTRACT_MONEY_EX_TAX": 2578463.3,
            "AMOUNT_TYPE": "CONTRACT_AMOUNT",
            "AMOUNT_INCLUDING_TAX": 2810525.0,
            "AMOUNT_EXCLUDING_TAX": 2578463.3,
            "TAX_AMOUNT": 232061.7,
            "AMOUNT_IMPROVE_INCLUDING_TAX": 2810525.0,
            "AMOUNT_IMPROVE_EXCLUDING_TAX": 2578463.3,
            "TAX_AMOUNT_IMPROVE": 232061.7,
            "CURRENCY": "CNY",
            "PAYMENT_DEPOSIT": "N",
            "IS_FRAMEWORK": "0",
            "CONTRACT_CONTENT": "依据《民法典》及相关法律法规的规定，甲乙双方在平等、互利的基础上，经充分协商，就“起湾社区“三线”改造工程（第三次）”项目合作实施事项达成一致意见，订立本合同，共同信守执行。",
            "CREATE_COMPANY_NO": "00210038000000000000",
            "CREATE_COMPANY_NAME": "中国移动广东公司\\中山分公司",
            "CREATE_DEPT_CODE": "00210038051051819537",
            "CREATE_DEPT_NAME": "中国移动广东公司\\中山分公司\\城区分公司\\政企客户中心\\DICT项目拓展室",

... (响应内容过长，已截断)
```

---

## 8. saleCenterApp/projectImplement/queryProjectProdprcDict
**中文名称**: 查询项目产品流程字典

### 8.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectImplement/queryProjectProdprcDict`
**方法**: POST
**调用次数**: 3

### 8.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 91
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "SUBJECT_LABLES": "ZZJC"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 8.3 响应结果
**状态码**: 200 
**响应大小**: 8560 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:13 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184213540_1_1512",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": [
        {
          "SUBJECT_CODE": "21503",
          "PROD_ID": "9201",
          "SUBJECT_NAME": "ICT自主集成服务费",
          "SUBJECT_CLASS": "9100",
          "VAT_RATE": 0.06,
          "BUSI_ACCEPT_METHOD": "1004",
          "BILL_METHOD": "10",
          "OPTION_BUSI": "1000",
          "STATUS_CD": "1000",
          "IS_ADD_PROD": "N",
          "SUBJECT_LABLES": ",ZZJC,TF,ZZIT,AUTO_JC_YJ,,AUTO_JF",
          "PROD_LABLES": "1017"
        },
        {
          "SUBJECT_CODE": "21503",
          "PROD_ID": "prod.10086000050216",
          "SUBJECT_NAME": "ICT自主集成服务费",
          "SUBJECT_CLASS": "2000",
          "VAT_RATE": 0.06,
          "BUSI_ACCEPT_METHOD": "1001",
          "BILL_METHOD": "20",
          "OPTION_BUSI": "1000",
          "STATUS_CD": "1100",
          "CREATE_STAFF": "zhongxl20220920",
          "CREATE_DATE": "2022-09-20 00:00:00",
          "IS_ADD_PROD": "Y",
          "SUBJECT_LABLES": ",ICT,ZZJC,ZZIT,AUTO_JC_YJ,,AUTO_JF"
        },
        {
          "SUBJECT_CODE": "21504",
          "PROD_ID": "9201",
          "SUBJECT_NAME": "ICT自主运维服务费",
          "SUBJECT_CLASS": "9100",
          "VAT_RATE": 0.06,
          "BUSI_ACCEPT_METHOD": "1004",
          "BILL_METHOD": "10",
          "OPTION_BUSI": "1000",
          "STATUS_CD": "1000",
          "IS_ADD_PROD": "N",
          "SUBJECT_LABLES": ",ZZJC,TF,ZZIT,AUTO_FW_YW,ZZYW,AUTO_JC_RJ,,AUTO_YW",
          "PROD_LABLES": "1017"
        },
        {
          "SUBJECT_CODE": "21505",
          "PROD_ID": "9201",
          "SUBJECT_NAME": "ICT自主安装服务费",
          "SUBJECT_CLASS": "9100",
          "VAT_RATE": 0.09,
 
... (响应内容过长，已截断)
```

---

## 9. saleCenterApp//preparation/queryProjectPlanWithImplement
**中文名称**: 查询项目计划及实施情况

### 9.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp//preparation/queryProjectPlanWithImplement`
**方法**: POST
**调用次数**: 5

### 9.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 103
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 9.3 响应结果
**状态码**: 200 
**响应大小**: 3095 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:12 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184212604_1_1244",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": [
        {
          "IMPLEMENT_PLAN_ID": "14438693",
          "PLAN_ID": "14438582",
          "PLAN_TYPE": "30",
          "PLAN_NAME": "安装服务",
          "PLAN_DESC": "安装服务",
          "PLAN_DEAL_STAFF": "zengxiaomin3",
          "PLAN_DEAL_NAME": "曾小敏",
          "REQUIRE_START_DATE": "2023-06-26 00:00:00",
          "REQUIRE_FINISH_DATE": "2023-10-26 23:59:59",
          "IMPLEMENT_STATUS": "1020",
          "IMPLEMENT_STATUS_DESC": "已完成",
          "ACT_FINSH_TIME": "2024-12-23 15:58:04",
          "PROGRESS_RATE": "100",
          "DELIVERY_TYPE": "1",
          "DELIVERY_TYPE_DESC": "IT类",
          "CREATE_DATE": "2023-06-26 11:04:13",
          "PLAN_TYPE_DESC": "安装服务",
          "PLAN_LEVEL": "1",
          "PARENT_IMPLEMENT_PLAN_ID": "0",
          "QUERY_PROJECT_PLAN_OUT_LIST": [],
          "STAFF_TYPE": "0",
          "PLAN_MODE": "10",
          "SUBJECT_TYPE": "5000",
          "SUBJECT_TYPE_DESC": "ICT安装服务"
        },
        {
          "IMPLEMENT_PLAN_ID": "194216146",
          "PLAN_ID": "CMGDZSICT20230614042",
          "PLAN_TYPE": "160",
          "PLAN_NAME": "项目带班任务",
          "PLAN_DEAL_STAFF": "honggaoliang",
          "PLAN_DEAL_NAME": "洪高梁",
          "IMPLEMENT_STATUS": "1000",
          "IMPLEMENT_STATUS_DESC": "未开始",
          "PROGRESS_RATE": "0",
          "DELIVERY_TYPE": "6",
          "DELIVERY_TYPE_DESC": "带班任务",
          "CREATE_DATE": "2025-06-09 23:14:57",
          "PLAN_TYPE_DESC": "项目带班任务",
          "PLAN_LEVEL": "1",
          "PARENT_IMPLEMENT_PLAN_ID": "0",
          "QUERY_PROJECT_PLAN_OUT_LIST": [
            {
              "IMPLEMENT_PLAN_
... (响应内容过长，已截断)
```

---

## 10. saleCenterApp/projectAmount/queryProjectAmount
**中文名称**: 查询项目金额信息

### 10.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectAmount/queryProjectAmount`
**方法**: POST
**调用次数**: 1

### 10.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 126
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042",
      "PROJECT_STAGE": "1001"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 10.3 响应结果
**状态码**: 200 
**响应大小**: 1028 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:12 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184212605_1_1357",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "AMOUNT_ID": "14398136",
        "PROJECT_ID": "CMGDZSICT20230614042",
        "BENEFIT_TYPE": "1001",
        "INC_CON_AMOUNT": 2810525.0,
        "CT_AMOUNT": 11088.0,
        "IT_CISERV_AMOUNT": 2660525.0,
        "PLATFORM9ONE_AMOUNT": 0.0,
        "CT_PNET5G_AMOUNT": 0.0,
        "IT_PNET5G_AMOUNT": 2564337.0,
        "MOBILE_CLOUD_AMOUNT": 0.0,
        "MOBILE_CLOUD_IP_AMOUNT": 0.0,
        "IDC_AMOUNT": 0.0,
        "LINE_AMOUNT": 11088.0,
        "IOT_AMOUNT": 0.0,
        "STATUS_CD": "1000",
        "STATUS_DATE": "2023-06-25 16:50:39",
        "CREATE_STAFF": "zengxiaomin3",
        "CREATE_NAME": "曾小敏",
        "CREATE_DATE": "2023-06-25 16:50:39",
        "UPDATE_STAFF": "zengxiaomin3",
        "UPDATE_DATE": "2025-01-23 14:48:07",
        "IT_AMOUNT": 2799437.0,
        "TD_AMOUNT": 0.0,
        "JG_AMOUNT": 0.0,
        "LL_AMOUNT": 0.0,
        "RZ_AMOUNT": 0.0,
        "MC_AMOUNT": 0.0,
        "SALE_AMOUNT": 0.0,
        "VN_AMOUNT": 0.0,
        "IB_AMOUNT": 0.0,
        "AI_AMOUNT": 0.0
      },
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 11. saleCenterApp//biddingSupport/saleBiddingInfo
**中文名称**: 查询销售投标信息

### 11.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp//biddingSupport/saleBiddingInfo`
**方法**: POST
**调用次数**: 2

### 11.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 103
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 11.3 响应结果
**状态码**: 200 
**响应大小**: 280 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:13 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "**************",
      "REQUEST_ID": "20250714184213799_1_69",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {},
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 12. saleCenterApp/formulation/queryProgram
**中文名称**: 查询项目方案信息

### 12.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/formulation/queryProgram`
**方法**: POST
**调用次数**: 6

### 12.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 103
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 12.3 响应结果
**状态码**: 200 
**响应大小**: 3095 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:14 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184213955_1_1540",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "TRADE": "NS",
        "TRADE_DESC": "农商",
        "FIRST_SCENE": "NS_ZHNL",
        "FIRST_SCENE_DESC": "智慧农林",
        "SECOND_SCENE": "202305171436320004",
        "SECOND_SCENE_DESC": "智慧乡镇",
        "SOLUTION_DETAIL_LIST": [
          {
            "DELIVERY_TYPE": "1000",
            "DELIVERY_DESC": ""
          },
          {
            "DELIVERY_TYPE": "1001",
            "DELIVERY_CLASSES": [
              "1000"
            ],
            "DELIVERY_CLASSES_DESC": "安装",
            "DELIVERY_DESC": "该项目面向基层乡镇治理场景，为后期打造一体的5G数字社区综合服务应用打下基础，包括乡村安防管控、综合治理等场景需求。具体内容：前期起湾社区光缆路由管道建设，运营商融合光缆架空敷设及管道敷设施工，融合光缆交接箱建设以及广电网络光缆敷设施工，后期利用5G网络优势，为社区建立5G专网，以及5G双域专网，为社区提供惠农服务移动端入口，为政府人员提供基层治理工具，借助5G低时延、大带宽特性，提供乡村综治等服务。"
          },
          {
            "DELIVERY_TYPE": "1002",
            "DELIVERY_DESC": ""
          },
          {
            "DELIVERY_TYPE": "1003",
            "DELIVERY_DESC": ""
          },
          {
            "DELIVERY_TYPE": "1004",
            "DELIVERY_DESC": "提供5G双域专网、5G专网免费体验半年。数据专线一年。"
          },
          {
            "DELIVERY_TYPE": "1006",
            "DELIVERY_DESC": "纯安装服务",
            "SPECIAL_COMPANY_PERSON": ""
          }
        ],
        "BUSINESS_MODEL_LIST": [
          {
            "EQUIPMENT_TYPE": "1002",
            "EQUIPMENT_TYPE_DESC": "IT-安装",
            "COOPERATION_MODE": "1001",
            "COOPERATION_MODE_DESC": "甄选",
            "BUSINESS_MODEL": "1010",
            "BUSINESS_MODEL_DESC": "主要责任人（通信服务固定费率）",
            "REMARK": ""
          }
        ],
        "COOPERATION_MODE_LIST": [
          "1001"
        ],
      
... (响应内容过长，已截断)
```

---

## 13. saleCenterApp/incomeManage/qryIncomeProgressByProject
**中文名称**: 按项目查询收入进度

### 13.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/incomeManage/qryIncomeProgressByProject`
**方法**: POST
**调用次数**: 3

### 13.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 126
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042",
      "PROJECT_STAGE": "1001"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 13.3 响应结果
**状态码**: 200 
**响应大小**: 5616 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:14 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184214204_1_1329",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "RESULT_LIST": [
          {
            "EXPENSE_PLAN_DETAIL_LIST": [
              {
                "PROJECT_EXPENSE_PLAN_DETAIL": {
                  "EXPENSE_DETAIL_ID": "14218226",
                  "EXPENSE_ID": "14218225",
                  "PROD_ID": "9201",
                  "PROD_NAME": "ICT开发集成项目",
                  "CT_OR_IT": "1000",
                  "SUBJECT_CODE": "23650",
                  "SUBJECT_NAME": "ICT其他工程建设费",
                  "SUBJECT_CLASS": "9000",
                  "IS_FLAT_RATE": "20",
                  "VAT_RATE": 0.09,
                  "CYCLE_TYPE": "10",
                  "CYCLE_COUNT": "1",
                  "COLL_CYCLE_TYPE": "10",
                  "COLL_CYCLE_COUNT": "1",
                  "MONEY": 150000.0,
                  "VAT": 3302.75,
                  "MONEY_EX_TAX": 137614.68,
                  "PLAN_BILL_START_TIME": "2023-06-01 00:00:00",
                  "PLAN_COLL_START_TIME": "2023-06-01 00:00:00",
                  "ACT_BILL_START_TIME": "2023-06-01 00:00:00",
                  "BILL_AMOUNT": 40000.0,
                  "BILL_AMOUNT_TOTAL_VAT": 3302.75,
                  "COLLECTION_AMOUNT": 0.0,
                  "INCOME_CONFIRM_PROGRESS": "1.00",
                  "CON_MILESTONE_TYPE": "10",
                  "INCOME_CONFIRM_STATUS": "1007",
                  "STATUS_CD": "1000",
                  "CREATE_STAFF": "1000030682",
                  "CREATE_NAME": "曾小敏",
                  "CREATE_DATE": "2023-06-20 15:43:02",
                  "UPDATE_STAFF": "1000030682",
                  "UPDATE_NAME": "曾小敏",
                  "UP
... (响应内容过长，已截断)
```

---

## 14. esop-inter-svc/inter/hwEsop/ebus/queryUsedInvoices
**中文名称**: 查询已使用发票信息

### 14.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/esop-inter-svc/inter/hwEsop/ebus/queryUsedInvoices`
**方法**: POST
**调用次数**: 1

### 14.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 169
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "BUSI_INFO": {
        "servNumber": "",
        "itemId": "CMGDZSICT20230614042",
        "startDate": "202502",
        "endDate": "202507"
      }
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 14.3 响应结果
**状态码**: 200 
**响应大小**: 148 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:13 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "BODY": {
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "usedInvoicesInfoList": []
      },
      "PROMPT_MSG": "",
      "RETURN_CODE": "0",
      "RETURN_MSG": "成功",
      "USER_MSG": "OK"
    }
  }
}
```

---

## 15. saleCenterApp//projectBill/qryBackInfoByProject
**中文名称**: 按项目查询回款信息

### 15.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp//projectBill/qryBackInfoByProject`
**方法**: POST
**调用次数**: 1

### 15.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 88
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": "CMGDZSICT20230614042"
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 15.3 响应结果
**状态码**: 200 
**响应大小**: 2750 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:14 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "**************",
      "REQUEST_ID": "20250714184214554_1_1427",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": [
        {
          "BACK_DATE": "20230901",
          "BILL_MON": "202308",
          "ITEM_ID": "CMGDZSICT20230614042",
          "CUST_ID": "2001330585",
          "CUST_NAME": "江苏瑞英达通信工程有限公司",
          "SERV_NUMBER": "21301179453",
          "PROD_ID": "9101",
          "PROD_NAME": "互联网专线",
          "ACCT_ID": "13595",
          "ACCT_NAME": "互联网接入接入月租费",
          "BACK_AMOUNT": 479.2,
          "INDB_TIME": "2024-08-17 10:29:33",
          "K_KEY": "CMGDZSICT20230614042_202308_21301179453_9101_13595"
        },
        {
          "BACK_DATE": "20230918",
          "BILL_MON": "202306",
          "ITEM_ID": "CMGDZSICT20230614042",
          "CUST_ID": "2001330585",
          "CUST_NAME": "江苏瑞英达通信工程有限公司",
          "SERV_NUMBER": "53711007304",
          "PROD_ID": "9201",
          "PROD_NAME": "ICT开发集成项目",
          "ACCT_ID": "21241",
          "ACCT_NAME": "5G专网集成安装服务",
          "BACK_AMOUNT": 841583.67,
          "INDB_TIME": "2023-09-19 16:12:53",
          "K_KEY": "CMGDZSICT20230614042_202306_53711007304_9201_21241"
        },
        {
          "BACK_DATE": "20231001",
          "BILL_MON": "202309",
          "ITEM_ID": "CMGDZSICT20230614042",
          "CUST_ID": "2001330585",
          "CUST_NAME": "江苏瑞英达通信工程有限公司",
          "SERV_NUMBER": "21301179453",
          "PROD_ID": "9101",
          "PROD_NAME": "互联网专线",
          "ACCT_ID": "13595",
          "ACCT_NAME": "互联网接入接入月租费",
          "BACK_AMOUNT": 599.0,
          "INDB_TIME": "2024-08-17 10:29:33",
          "K_KEY": "CMGDZSICT20230614042_202309_21301179453_9101_13595"
        },
        {
          "BACK_DATE": 
... (响应内容过长，已截断)
```

---

## 16. saleCenterApp//projectBill/qryBillInfoByProject
**中文名称**: 按项目查询账单信息

### 16.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp//projectBill/qryBillInfoByProject`
**方法**: POST
**调用次数**: 1

### 16.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 88
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": "CMGDZSICT20230614042"
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 16.3 响应结果
**状态码**: 200 
**响应大小**: 2605 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:13 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "***************",
      "REQUEST_ID": "20250714184213108_1_1371",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": [
        {
          "BILL_MON": "202306",
          "ITEM_ID": "CMGDZSICT20230614042",
          "CUST_ID": "2001330585",
          "CUST_NAME": "江苏瑞英达通信工程有限公司",
          "SERV_NUMBER": "53711007304",
          "PROD_ID": "9201",
          "PROD_NAME": "ICT开发集成项目",
          "ACCT_ID": "21241",
          "ACCT_NAME": "5G专网集成安装服务",
          "BILL_AMOUNT": 2564337.0,
          "INDB_TIME": "2023-07-02 21:02:06",
          "K_KEY": "CMGDZSICT20230614042_202306_53711007304_9201_21241"
        },
        {
          "BILL_MON": "202306",
          "ITEM_ID": "CMGDZSICT20230614042",
          "CUST_ID": "2001330585",
          "CUST_NAME": "江苏瑞英达通信工程有限公司",
          "SERV_NUMBER": "53711007304",
          "PROD_ID": "9201",
          "PROD_NAME": "ICT开发集成项目",
          "ACCT_ID": "23650",
          "ACCT_NAME": "ICT其他工程建设费",
          "BILL_AMOUNT": 40000.0,
          "INDB_TIME": "2023-07-02 21:02:06",
          "K_KEY": "CMGDZSICT20230614042_202306_53711007304_9201_23650"
        },
        {
          "BILL_MON": "202308",
          "ITEM_ID": "CMGDZSICT20230614042",
          "CUST_ID": "2001330585",
          "CUST_NAME": "江苏瑞英达通信工程有限公司",
          "SERV_NUMBER": "21301179453",
          "PROD_ID": "9101",
          "PROD_NAME": "互联网专线",
          "ACCT_ID": "13595",
          "ACCT_NAME": "互联网接入接入月租费",
          "BILL_AMOUNT": 479.2,
          "INDB_TIME": "2024-08-17 10:38:57",
          "K_KEY": "CMGDZSICT20230614042_202308_21301179453_9101_13595"
        },
        {
          "BILL_MON": "202309",
          "ITEM_ID": "CMGDZSICT20230614042",
          "CUST_ID": "2001330585",
         
... (响应内容过长，已截断)
```

---

## 17. saleCenterApp//projectBill/queryCurMonthRealTimeIncome
**中文名称**: 查询当月实时收入

### 17.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp//projectBill/queryCurMonthRealTimeIncome`
**方法**: POST
**调用次数**: 1

### 17.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 88
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": "CMGDZSICT20230614042"
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 17.3 响应结果
**状态码**: 200 
**响应大小**: 283 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:14 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "***************",
      "REQUEST_ID": "20250714184214501_1_1345",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": [],
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 18. saleCenterApp//projectBill/qryCheckdecrDateInfoByProject
**中文名称**: 按项目查询核减日期信息

### 18.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp//projectBill/qryCheckdecrDateInfoByProject`
**方法**: POST
**调用次数**: 1

### 18.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 88
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": "CMGDZSICT20230614042"
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 18.3 响应结果
**状态码**: 200 
**响应大小**: 281 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:14 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184214511_1_1363",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": [],
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 19. saleFileApp/common/fileService/queryBusiDocListByDocType
**中文名称**: 按文档类型查询业务文档列表

### 19.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleFileApp/common/fileService/queryBusiDocListByDocType`
**方法**: POST
**调用次数**: 56

### 19.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 156
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "BUSI_ID": "CMGDZSICT20230614042",
      "BUSI_TYPE": "11",
      "DOC_TYPE": "G001",
      "IS_QRY_AI_INFO": "1"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 19.3 响应结果
**状态码**: 200 
**响应大小**: 570 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:13 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "***************",
      "REQUEST_ID": "20250714184213238_1_1374",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "OUT_PARAM": [
          {
            "DOC_ID": "ddeb16da-9fb1-44f6-9726-e423a71eb687",
            "DOC_NAME": "7.1、起湾需求书.pdf",
            "FILE_TYPE": "pdf",
            "DOC_SIZE": "599699",
            "FLOW_TASK_ID": "111111",
            "CREATE_DATE": "2024-08-14 17:03:14",
            "REL_ID": "79199695",
            "AUTHOR": "曾小敏",
            "VERSION_NUM": "1",
            "VERSION_REL_ID": "79199695"
          }
        ]
      },
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 20. esop-inter-svc/inter/hwOpp/ebus/queryOppInfoForDict
**中文名称**: 查询华为商机信息字典

### 20.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/esop-inter-svc/inter/hwOpp/ebus/queryOppInfoForDict`
**方法**: POST
**调用次数**: 2

### 20.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 124
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "BUSI_INFO": {
        "oppId": "76023060810091275",
        "region": "760"
      }
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `oppId` | saleCenterApp/projectManage/queryProjectInfo 接口返回的商机信息 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 20.3 响应结果
**状态码**: 200 
**响应大小**: 1956 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:13 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "BODY": {
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "stakeholderInfoList": [],
        "followUpProgressList": [],
        "extendInfoList": [],
        "schemeReviewList": [],
        "biddingItemList": [],
        "opportunityCustomer": {
          "unitName": "东区",
          "groupId": "2001330585",
          "valueLevelId": "03",
          "groupOwnOrg": "NETGRID",
          "custName": "江苏瑞英达通信工程有限公司",
          "vocationKind": "NS",
          "groupOwnOrgName": "网格归属集团",
          "valueLevelName": "C",
          "custId": "87600000143547",
          "unitId": "ZS0120",
          "isRegistered": "1",
          "region": "760",
          "vocationKindName": "农商行业"
        },
        "oppId": "76023060810091275",
        "oppBaseInfo": {
          "firstScene": "NS_OTHER",
          "cityCode": "760",
          "ratingList": [],
          "isInternationalBusi": "0",
          "commBudget": "281000000",
          "valueLevelId": "03",
          "sceneIndustry": "NS",
          "prodList": [],
          "oppType": "COMMONOPP",
          "sceneIndustryName": "农商",
          "isSpecialDebt": "0",
          "isOldForNew": "0",
          "oppProduct": [
            {
              "prodItemList": [],
              "commonBudget": "281000000",
              "scriptList": [],
              "prodAttrList": [],
              "oppId": "76023060810091275",
              "prodId": "ICT",
              "state": "1",
              "prodAttr": [],
              "busiOid": "23073191567919"
            }
          ],
          "subjectForm": "2",
          "stateName": "完成",
          "busiOpportNumber": "200SP2023060817495332644",
          "custId": "87600000143547",
          "belongsCityCode": "760",
          "secondSceneName": "其他",
          "secondScene": "NS_OTHER_QT",
          "firstSceneName": "其他",
          "oppName": "东区街道起湾社区三线整治服务项目",
          "state": "finish",
          "isIncludeSolution": "0",
          "vocationKindName": "农商行业
... (响应内容过长，已截断)
```

---

## 21. saleCenterApp/incomeManage/qryIncomePlanListByProject
**中文名称**: 按项目查询收入计划列表

### 21.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/incomeManage/qryIncomePlanListByProject`
**方法**: POST
**调用次数**: 1

### 21.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 126
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042",
      "PROJECT_STAGE": "1001"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 21.3 响应结果
**状态码**: 200 
**响应大小**: 8311 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:14 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184214111_1_1347",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "RESULT_LIST": [
          {
            "PROJECTEXPENSEPLAN": {
              "EXPENSE_ID": "14218225",
              "PROJECT_STAGE": "1001",
              "PROJECT_ID": "CMGDZSICT20230614042",
              "CONTRACT_ID": "14217230",
              "PLAN_TOTAL_INCOME": 2810525.0,
              "PLAN_TOTAL_VAT": 232061.7,
              "TOTAL_MONEY_EX_TAX": 2578463.3,
              "IT_PLAN_TOTAL_INCOME": 2799437.0,
              "CT_PLAN_TOTAL_INCOME": 11088.0,
              "PLAN_COMMU_OPERATOR_INCOME": 2810525.0,
              "BILL_AMOUNT": 2604337.0,
              "BILL_AMOUNT_TOTAL_VAT": 215037.0,
              "IT_BILL_AMOUNT": 2604337.0,
              "CT_BILL_AMOUNT": 0.0,
              "INCOME_CONFIRM_PROGRESS": "0.93",
              "EXPENSE_PLAN_STATUS": "1003",
              "STATUS_CD": "1000",
              "CREATE_STAFF": "1000030682",
              "CREATE_NAME": "曾小敏",
              "CREATE_DATE": "2023-06-20 15:43:02",
              "UPDATE_STAFF": "zengxiaomin3",
              "UPDATE_DATE": "2025-01-23 14:48:01",
              "COMBINATION_AMOUNT": 0.0
            },
            "EXPENSE_PLAN_DETAIL_LIST": [
              {
                "PROJECT_EXPENSE_PLAN_DETAIL": {
                  "EXPENSE_DETAIL_ID": "14218226",
                  "EXPENSE_ID": "14218225",
                  "PROD_ID": "9201",
                  "PROD_NAME": "ICT开发集成项目",
                  "CT_OR_IT": "1000",
                  "SUBJECT_CODE": "23650",
                  "SUBJECT_NAME": "ICT其他工程建设费",
                  "SUBJECT_CLASS": "9000",
                  "IS_FLAT_RATE": "20",
           
... (响应内容过长，已截断)
```

---

## 22. saleCenterApp/projectAmount/queryAdvanceAmount
**中文名称**: 查询预付款金额

### 22.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectAmount/queryAdvanceAmount`
**方法**: POST
**调用次数**: 2

### 22.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 103
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 22.3 响应结果
**状态码**: 200 
**响应大小**: 281 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:14 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "***************",
      "REQUEST_ID": "20250714184214455_1_370",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": 0,
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 23. esop-inter-svc/inter/hwCust/ebus/custQueryCustByCustId
**中文名称**: 按客户ID查询客户信息

### 23.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/esop-inter-svc/inter/hwCust/ebus/custQueryCustByCustId`
**方法**: POST
**调用次数**: 2

### 23.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 107
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "BUSI_INFO": {
        "custId": "87600000143547"
      }
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `custId` | saleCenterApp/projectManage/queryProjectInfo 接口返回的客户信息 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 23.3 响应结果
**状态码**: 200 
**响应大小**: 491 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:14 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "BODY": {
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "groupInfo": {
          "unitKind": "corporation12",
          "custmgrOperId": "TZS03E0X50",
          "valueLevelName": "C",
          "groupId": "2001330585",
          "ecCode": "200A9992001330585",
          "valueLevelId": "03",
          "unitKindName": "1.2 企业法人",
          "vocaionKind2": "NS"
        },
        "customer": {
          "custId": "87600000143547",
          "vipType": "Group",
          "ownerAreaId": "ZSCQ",
          "custName": "江苏瑞英达通信工程有限公司",
          "region": "760"
        }
      },
      "PROMPT_MSG": "",
      "RETURN_CODE": "0",
      "RETURN_MSG": "处理成功",
      "USER_MSG": "OK"
    }
  }
}
```

---

## 24. saleCenterApp/projectLabel/queryProjectLabelConf
**中文名称**: 查询项目标签配置

### 24.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectLabel/queryProjectLabelConf`
**方法**: POST
**调用次数**: 3

### 24.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 87
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_TYPE": "10"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 24.3 响应结果
**状态码**: 200 
**响应大小**: 1586 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:14 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184214739_1_1319",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": [
        {
          "LABEL_CATEGORY_CODE": "10",
          "LABEL_CATEGORY_NAME": "ICT业务-IOT"
        },
        {
          "LABEL_CATEGORY_CODE": "20",
          "LABEL_CATEGORY_NAME": "ICT业务-5G专网"
        },
        {
          "LABEL_CATEGORY_CODE": "30",
          "LABEL_CATEGORY_NAME": "ICT业务-云计算"
        },
        {
          "LABEL_CATEGORY_CODE": "40",
          "LABEL_CATEGORY_NAME": "ICT业务-大数据"
        },
        {
          "LABEL_CATEGORY_CODE": "41",
          "LABEL_CATEGORY_NAME": "ICT业务-智慧交通平台"
        },
        {
          "LABEL_CATEGORY_CODE": "42",
          "LABEL_CATEGORY_NAME": "ICT业务-行业视频"
        },
        {
          "LABEL_CATEGORY_CODE": "43",
          "LABEL_CATEGORY_NAME": "ICT业务-信创工程"
        },
        {
          "LABEL_CATEGORY_CODE": "44",
          "LABEL_CATEGORY_NAME": "ICT业务-智慧JY"
        },
        {
          "LABEL_CATEGORY_CODE": "45",
          "LABEL_CATEGORY_NAME": "ICT业务-OneHealth"
        },
        {
          "LABEL_CATEGORY_CODE": "46",
          "LABEL_CATEGORY_NAME": "ICT业务-OneCity"
        },
        {
          "LABEL_CATEGORY_CODE": "47",
          "LABEL_CATEGORY_NAME": "ICT业务-OnePark"
        },
        {
          "LABEL_CATEGORY_CODE": "48",
          "LABEL_CATEGORY_NAME": "ICT业务-OnePoint"
        },
        {
          "LABEL_CATEGORY_CODE": "49",
          "LABEL_CATEGORY_NAME": "ICT业务-OnePower"
        },
        {
          "LABEL_CATEGORY_CODE": "50",
          "LABEL_CATEGORY_NAME": "ICT业务-OneVillage"
        },
        {
          "LABEL_CATEGORY_CODE": "51",
          "LABEL_CATEGORY_NAME": "ICT业务-OneTrip"
        },
   
... (响应内容过长，已截断)
```

---

## 25. saleCenterApp/projectManage/queryMyProjectList
**中文名称**: 查询我的项目列表

### 25.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectManage/queryMyProjectList`
**方法**: POST
**调用次数**: 3

### 25.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 460
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "IN_PARAM": {
        "PROJECT_NAME": "",
        "PROJECT_NO": "",
        "BBOSS_PROJECT_ID": "",
        "PROJECT_CONTRACT_TYPE": "",
        "PROJECT_TYPE": "10",
        "SUB_PROJECT_NO": "",
        "PROJECT_PROGRESS_BEGIN": 0,
        "PROJECT_PROGRESS_END": 0,
        "PROJECT_STATUS": "1000",
        "PROJECT_SOURCE": "",
        "COOPERATION_MODE": "",
        "EXCLUDE_PROJECT_TYPE": "",
        "PROJECT_STATUS_LIST": [],
        "PROJECT_PROGRESS_LIST": [],
        "IS_CUL_PROJECT": ""
      },
      "PAGE_INFO": {
        "PAGE_NUM": 1,
        "PAGE_SIZE": 5
      }
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 25.3 响应结果
**状态码**: 200 
**响应大小**: 323 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:15 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184215499_1_1124",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "OUT_PARAM": [],
        "PAGE_INFO": {
          "ROW_COUNT": 0
        }
      },
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 26. saleCenterApp/investmentReview/queryFlowStep
**中文名称**: 查询投资评审流程步骤

### 26.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/investmentReview/queryFlowStep`
**方法**: POST
**调用次数**: 3

### 26.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 146
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "EXT_ORDER_ID": "CMGDZSICT20230614042",
      "COMODITY_ID": "GD_DICT02",
      "INV_LINE": "0"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 26.3 响应结果
**状态码**: 200 
**响应大小**: 545 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:15 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184215540_1_1578",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": [
        {
          "TASK_CODE": "2027",
          "STATE": 2,
          "NAME": "项目准备"
        },
        {
          "TASK_CODE": "2028",
          "STATE": 2,
          "NAME": "交付实施"
        },
        {
          "TASK_CODE": "2029",
          "STATE": 1,
          "NAME": "项目验收"
        },
        {
          "TASK_CODE": "2031",
          "STATE": 0,
          "NAME": "项目交维"
        },
        {
          "TASK_CODE": "2030",
          "STATE": 0,
          "NAME": "交付评估"
        }
      ],
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 27. saleCenterApp/projectProportion/queryGroupOrg
**中文名称**: 查询集团组织架构

### 27.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectProportion/queryGroupOrg`
**方法**: POST
**调用次数**: 1

### 27.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 111
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "GROUP_ID": "2001330585",
      "REGION_CODE": "760"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 27.3 响应结果
**状态码**: 200 
**响应大小**: 486 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:15 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "**************",
      "REQUEST_ID": "20250714184215540_1_1426",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "BI_ORG_TYP_CD": "1",
        "BI_ORG_CD": "ZS0120",
        "ORG_CD_NAM": "东区",
        "VERT_INDUS_TYP_NAM_CD": "9",
        "VERT_INDUS_TYP_NAM": "农商行业",
        "CMCC_CNTY_CD": "ZS01",
        "CMCC_CNTY_NAM": "城区分公司",
        "CMCC_BRANCH_CD": "ZS"
      },
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 28. saleCenterApp/deliveryAssessment/queryAllTaskByProjectId
**中文名称**: 按项目ID查询所有任务

### 28.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/deliveryAssessment/queryAllTaskByProjectId`
**方法**: POST
**调用次数**: 1

### 28.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 103
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 28.3 响应结果
**状态码**: 200 
**响应大小**: 609 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:17 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184217466_1_1533",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": [
        {
          "TASK_TYPE": "10",
          "TASK_TYPE_NAME": "设备/套软",
          "IS_INVOLVED": "20"
        },
        {
          "TASK_TYPE": "20",
          "TASK_TYPE_NAME": "安装服务、集成服务、运营服务",
          "IS_INVOLVED": "10"
        },
        {
          "TASK_TYPE": "30",
          "TASK_TYPE_NAME": "软件/平台、IT标准产品",
          "IS_INVOLVED": "20"
        },
        {
          "TASK_TYPE": "40",
          "TASK_TYPE_NAME": "安全管理",
          "IS_INVOLVED": "20"
        }
      ],
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 29. saleCenterApp/projectImplement/queryProjectJointTest
**中文名称**: 查询项目联调测试信息

### 29.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectImplement/queryProjectJointTest`
**方法**: POST
**调用次数**: 2

### 29.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 103
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 29.3 响应结果
**状态码**: 200 
**响应大小**: 314 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:17 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "***************",
      "REQUEST_ID": "20250714184217450_1_1355",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "PROJECT_JOINT_TEST_IN_LIST": []
      },
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 30. saleCenterApp/preparation/queryProjectMilestone
**中文名称**: 查询项目里程碑

### 30.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/preparation/queryProjectMilestone`
**方法**: POST
**调用次数**: 4

### 30.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 103
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 30.3 响应结果
**状态码**: 200 
**响应大小**: 3317 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:17 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184217478_1_1447",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "PROJECT_MILESTONE_LIST": [
          {
            "MILESTONE_ID": "14438580",
            "PROJECT_ID": "CMGDZSICT20230614042",
            "MILESTONE_NAME": "项目总体计划",
            "MILESTONE_TYPE": "10",
            "PLAN_START_TIME": "2023-06-01 00:00:00",
            "PLAN_FINISH_TIME": "2025-03-27 00:00:00",
            "ACT_START_TIME": "2023-06-26 11:03:12",
            "STATUS_CD": "1000",
            "STATUS_DATE": "2023-06-26 11:03:12",
            "CREATE_STAFF": "1000030682",
            "CREATE_NAME": "曾小敏",
            "CREATE_DATE": "2023-06-26 11:03:12",
            "UPDATE_STAFF": "1000030682",
            "UPDATE_NAME": "曾小敏",
            "UPDATE_DATE": "2024-07-03 18:25:01"
          },
          {
            "MILESTONE_ID": "14438575",
            "PROJECT_ID": "CMGDZSICT20230614042",
            "MILESTONE_NAME": "项目准备",
            "MILESTONE_TYPE": "20",
            "PLAN_START_TIME": "2023-06-01 00:00:00",
            "PLAN_FINISH_TIME": "2023-06-26 23:59:59",
            "ACT_START_TIME": "2023-06-26 11:03:12",
            "ACT_FINISH_TIME": "2023-06-26 11:03:12",
            "STATUS_CD": "1000",
            "STATUS_DATE": "2023-06-26 11:03:12",
            "CREATE_STAFF": "1000030682",
            "CREATE_NAME": "曾小敏",
            "CREATE_DATE": "2023-06-26 11:03:12",
            "UPDATE_STAFF": "1000030682",
            "UPDATE_NAME": "曾小敏",
            "UPDATE_DATE": "2023-06-26 11:03:12"
          },
          {
            "MILESTONE_ID": "14438576",
            "PROJECT_ID": "CMGDZSICT20230614042",
            "MILESTONE_NAME": "项目实施",
            "MILES
... (响应内容过长，已截断)
```

---

## 31. saleCenterApp/projectFinalTest/acceptanceCompletedQuery
**中文名称**: 验收完成情况查询

### 31.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectFinalTest/acceptanceCompletedQuery`
**方法**: POST
**调用次数**: 2

### 31.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 103
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 31.3 响应结果
**状态码**: 200 
**响应大小**: 1082 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:17 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "***************",
      "REQUEST_ID": "20250714184217561_1_1365",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "MILESTONE_ID": "14438578",
        "PROJECT_ID": "CMGDZSICT20230614042",
        "PLAN_START_TIME": "2025-01-31 00:00:00",
        "PLAN_FINISH_TIME": "2025-02-28 00:00:00",
        "ACT_START_TIME": "2025-07-10 17:28:13",
        "CUST_LIST": [
          {
            "ACCEPTANCE_ID": "212518354",
            "PROJECT_ID": "CMGDZSICT20230614042",
            "ACCEPTANCE_TYPE": "1000",
            "ACCEPTANCE_CLASS": "1000",
            "ACCEPTANCE_CLASS_DESC": "初验",
            "ACCEPTANCE_START_TIME": "2025-07-10 23:59:59",
            "ACCEPTANCE_END_TIME": "2025-07-10 23:59:59",
            "ACCEPTANCE_DESC": "",
            "IS_CHANGED": false,
            "IS_NEW": false
          }
        ],
        "INNE_LIST": [
          {
            "ACCEPTANCE_ID": "212518701",
            "PROJECT_ID": "CMGDZSICT20230614042",
            "ACCEPTANCE_TYPE": "1001",
            "ACCEPTANCE_CLASS": "1000",
            "ACCEPTANCE_CLASS_DESC": "初验",
            "ACCEPTANCE_START_TIME": "2025-07-10 23:59:59",
            "ACCEPTANCE_END_TIME": "2025-07-10 23:59:59",
            "ACCEPTANCE_DESC": "",
            "IS_CHANGED": false,
            "IS_NEW": false
          }
        ]
      },
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 32. saleCenterApp/deliveryAssessment/satisfactionAssessmentInit
**中文名称**: 满意度评估初始化

### 32.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/deliveryAssessment/satisfactionAssessmentInit`
**方法**: POST
**调用次数**: 1

### 32.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 124
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042",
      "EVALUATE_TYPE": "02"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 32.3 响应结果
**状态码**: 200 
**响应大小**: 268 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:17 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "**************",
      "REQUEST_ID": "20250714184217704_1_1117",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 33. saleCenterApp/deliveryAssessment/satisfactionAssessmentQuery
**中文名称**: 满意度评估查询

### 33.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/deliveryAssessment/satisfactionAssessmentQuery`
**方法**: POST
**调用次数**: 1

### 33.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 124
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042",
      "EVALUATE_TYPE": "02"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 33.3 响应结果
**状态码**: 200 
**响应大小**: 7783 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:18 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184218156_1_1167",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "EVALUATE_ID": "214272308",
        "PROJECT_ID": "CMGDZSICT20230614042",
        "EVALUATE_TYPE": "02",
        "EVALUATE_START_DATE": "2025-07-14 18:36:42",
        "EVALUATE_STATUS": "12",
        "ITEMS": [
          {
            "EVALUATE_ITEM_ID": "214272309",
            "EVALUATE_ITEM_DICT_ID": "20",
            "EVALUATE_ITEM": "START_MEETING_SUMMARY",
            "EVALUATE_ITEM_NAME": "启动会纪要",
            "EVALUATE_ID": "214272308",
            "LOWEST_SCORE": 0.0,
            "HIGHEST_SCORE": 5.0,
            "EVALUATE_TYPE": "02",
            "EVALUATE_TYPE_NAME": "交付材料评估",
            "EVALUATE_SUBCLASS": "0201",
            "EVALUATE_SUBCLASS_NAME": "项目准备评估项",
            "DOC_TYPE": "G040"
          },
          {
            "EVALUATE_ITEM_ID": "214272310",
            "EVALUATE_ITEM_DICT_ID": "21",
            "EVALUATE_ITEM": "IMPLEMENTATION_PLAN",
            "EVALUATE_ITEM_NAME": "实施方案",
            "EVALUATE_ID": "214272308",
            "LOWEST_SCORE": 0.0,
            "HIGHEST_SCORE": 5.0,
            "EVALUATE_TYPE": "02",
            "EVALUATE_TYPE_NAME": "交付材料评估",
            "EVALUATE_SUBCLASS": "0201",
            "EVALUATE_SUBCLASS_NAME": "项目准备评估项",
            "DOC_TYPE": "G042"
          },
          {
            "EVALUATE_ITEM_ID": "214272311",
            "EVALUATE_ITEM_DICT_ID": "22",
            "EVALUATE_ITEM": "PARTNER_DOCUMENT",
            "EVALUATE_ITEM_NAME": "合作商选型文件",
            "EVALUATE_ID": "214272308",
            "LOWEST_SCORE": 0.0,
            "HIGHEST_SCORE": 5.0,
            "EVALUATE_TYPE": "02",
            "EVALUATE_TYPE_NAME": 
... (响应内容过长，已截断)
```

---

## 34. bss-base-query/base/qryBulletinList
**中文名称**: 查询公告列表

### 34.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/bss-base-query/base/qryBulletinList?loginNo=liaochulin&statusCds=1000&pageInfo.pageIndex=1&pageInfo.pageSize=999&bulletinLevels=1000&`
**方法**: GET
**调用次数**: 3

### 34.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Host: dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
```

**查询参数 (Query Parameters)**:
```
loginNo: liaochulin
statusCds: 1000
pageInfo.pageIndex: 1
pageInfo.pageSize: 999
bulletinLevels: 1000
: 
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `loginNo` | 用户登录Session信息 |
| `statusCds` | 前端硬编码的查询条件 |
| `pageInfo.pageIndex` | 前端分页组件参数 |
| `pageInfo.pageSize` | 前端分页组件参数 |
| `bulletinLevels` | 前端硬编码的查询条件 |

### 34.3 响应结果
**状态码**: 200 
**响应大小**: 27063 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:19 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "contractRoot": {
    "tcpCont": {
      "rspTime": "20250714184219578"
    },
    "svcCont": {
      "resultCode": "0",
      "resultMsg": "查询公告接收列表成功",
      "respObject": {
        "pageInfo": {
          "pageIndex": 1,
          "pageSize": 999,
          "pageCount": 1,
          "rowCount": 4
        },
        "bulletin": [
          {
            "bulletinId": 8056,
            "bulletinTitle": "关于DICT项目管理平台存量历史项目数据第二、三批次割接的通知",
            "bulletinContent": "<p class=\"p\"> <span><strong>各市公司、集团客户部、财务部、内审部、信息系统部、政企客户中心、产品研发中心、中国移动系统集成公司</strong></span><strong>:</strong> </p>  <p class=\"MsoNormal\" style=\"margin-left:0pt;text-indent:0pt;\"> &nbsp; </p>  <div align=\"center\">   <table class=\"MsoNormalTable\" border=\"1\" cellspacing=\"0\" style=\"border:none;\">    <tbody>     <tr>      <td width=\"491\" valign=\"center\" style=\"border:none;\"> <p class=\"MsoNormal\" style=\"text-indent:32.05pt;\"> <span style=\"font-family:仿宋_GB2312;color:#000000;font-size:16.0000pt;\">2022年，总部下发《关于下发&lt;中国移动DICT支撑能力业务规范（省公司分册）&gt;的通知》（政企通〔2022〕67<span>&nbsp;</span><span>号））通知，对各省DICT业务支撑能力提出了更高水平的规范要求。</span></span><span style=\"font-family:Calibri;font-size:10.5000pt;\"></span> </p> <p class=\"MsoNormal\" style=\"text-indent:32.05pt;\"> <span style=\"font-family:仿宋_GB2312;color:#000000;font-size:16.0000pt;\"><span>为持续提升</span>DICT精细化管理，加强项目合规性管控，最终使得老DICT系统全量项目割接至新DICT系统进行管理，信息系统部已于11月22日对云浮、阳江的存量项目进行割接，割接后项目运行稳定，现计划继续分批次开展项目割接工作，具体安排如下：</span><span style=\"font-family:Calibri;font-size:10.5000pt;\"></span> </p> <p class=\"MsoNormal\" style=\"text-indent:32.05pt;\"> <span style=\"font-family:仿宋_GB2312;color:#000000;font-size:16.0000pt;\"><span>一、</span> <span>割接时间</span></span><span style=\"font-family:Calibri;font-size:10.5000pt;\"></span> </p> <p class=\"MsoNormal\" style=\"text-indent:32.05pt;\"> <span style=\"font-family:仿宋_GB2312;color:#000000;font-size:16.0000pt;\"><span>第二批：</span>2022年12月2日19:00至12月3日6:00。</span><span style=\"font-family:Calibri;font-siz
... (响应内容过长，已截断)
```

---

## 35. saleCenterApp/arrearage/queryArrearsDetail
**中文名称**: 查询欠费详情

### 35.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/arrearage/queryArrearsDetail`
**方法**: POST
**调用次数**: 1

### 35.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 165
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "IN_PARAM": {
        "REGION_CODE": "760",
        "CUST_ID": "2001330585"
      },
      "PAGE_INFO": {
        "PAGE_NUM": 1,
        "PAGE_SIZE": 10
      }
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `LOGIN_NO/loginNo` | 用户登录Session信息 |
| `REGION_CODE` | 用户登录信息中的地区代码 |

### 35.3 响应结果
**状态码**: 200 
**响应大小**: 829 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:30 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184230174_1_1532",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "OUT_PARAM": [
          {
            "PROJECT_NAME": "东区街道起湾社区三线整治服务项目",
            "PROJECT_ID": "CMGDZSICT20230614042",
            "PROD_ID": "9201",
            "PROD_NAME": "ICT开发集成项目",
            "ACCT_NAME": "5G专网集成安装服务",
            "BILL_MON": "202306",
            "OWE_AMOUNT": 315916.8,
            "OWE_DAY": 745
          },
          {
            "PROJECT_NAME": "东区街道起湾社区三线整治服务项目",
            "PROJECT_ID": "CMGDZSICT20230614042",
            "PROD_ID": "9201",
            "PROD_NAME": "ICT开发集成项目",
            "ACCT_NAME": "ICT其他工程建设费",
            "BILL_MON": "202306",
            "OWE_AMOUNT": 40000.0,
            "OWE_DAY": 745
          }
        ],
        "PAGE_INFO": {
          "ROW_COUNT": 2
        }
      },
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 36. saleCenterApp/arrearage/queryArrearsList
**中文名称**: 查询欠费列表

### 36.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/arrearage/queryArrearsList`
**方法**: POST
**调用次数**: 1

### 36.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 164
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "IN_PARAM": {
        "REGION_CODE": "760",
        "CUST_ID": "2001330585"
      },
      "PAGE_INFO": {
        "PAGE_NUM": 1,
        "PAGE_SIZE": 1
      }
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `LOGIN_NO/loginNo` | 用户登录Session信息 |
| `REGION_CODE` | 用户登录信息中的地区代码 |

### 36.3 响应结果
**状态码**: 200 
**响应大小**: 444 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:30 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184230175_1_1537",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "OUT_PARAM": [
          {
            "CUST_NAME": "江苏瑞英达通信工程有限公司",
            "CUST_ID": "2001330585",
            "OWE_AMOUNT": 355916.8,
            "BILL_MON": "202306"
          }
        ],
        "PAGE_INFO": {
          "ROW_COUNT": 1
        }
      },
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 37. saleCenterApp/formulation/queryMemberChangeList
**中文名称**: 查询成员变更记录

### 37.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/formulation/queryMemberChangeList`
**方法**: POST
**调用次数**: 2

### 37.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 226
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "IN_PARAM": {
        "OPER_TYPE": "",
        "STAFF_ID": "",
        "time": [],
        "DATE_START": "",
        "DATE_END": "",
        "PROJECT_ID": "CMGDZSICT20230614042"
      },
      "PAGE_INFO": {
        "PAGE_NUM": 1,
        "PAGE_SIZE": 5
      }
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 37.3 响应结果
**状态码**: 200 
**响应大小**: 2106 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:50 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184250441_1_1337",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "OUT_PARAM": [
          {
            "LOG_ID": "212283963",
            "OPER_TYPE": "ADD",
            "STAFF_ID": "xuyingying",
            "OPER_CONTENT": "新增为【售中】团队的【交付经理】。",
            "CREATE_DATE": "2025-07-10 09:24:56",
            "OPER_STAFF": "zengxiaomin3",
            "AFTER_ROLE": "交付经理",
            "AFTER_TYPE": "售中",
            "PROJECT_ID": "CMGDZSICT20230614042",
            "OPER_TYPE_NAME": "新增"
          },
          {
            "LOG_ID": "202991501",
            "OPER_TYPE": "UPDATE",
            "STAFF_ID": "xuyingying",
            "OPER_CONTENT": "团队角色从【运维经理】改为【客户经理】。",
            "CREATE_DATE": "2025-06-25 10:45:51",
            "OPER_STAFF": "honggaoliang",
            "BEFORE_ROLE": "运维经理",
            "AFTER_ROLE": "客户经理",
            "BEFORE_TYPE": "售中",
            "AFTER_TYPE": "售中",
            "BEFORE_CHIEF": "",
            "AFTER_CHIEF": "",
            "PROJECT_ID": "CMGDZSICT20230614042",
            "OPER_TYPE_NAME": "修改"
          },
          {
            "LOG_ID": "194557182",
            "OPER_TYPE": "ADD",
            "STAFF_ID": "chenguibao",
            "OPER_CONTENT": "新增为【售前】团队的【客户经理】。",
            "CREATE_DATE": "2025-06-10 11:44:15",
            "OPER_STAFF": "huyafeng",
            "AFTER_ROLE": "客户经理",
            "AFTER_TYPE": "售前",
            "PROJECT_ID": "CMGDZSICT20230614042",
            "OPER_TYPE_NAME": "新增"
          },
          {
            "LOG_ID": "194518286",
            "OPER_TYPE": "UPDATE",
            "STAFF_ID": "huyafeng",
            "OPER_CONTENT": "团队角色从【交付经理】改为【维护人员】。【非首席】改为【非首席】。",
            "CR
... (响应内容过长，已截断)
```

---

## 38. saleCenterApp/formulation/queryTeamMember
**中文名称**: 查询项目团队成员

### 38.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/formulation/queryTeamMember`
**方法**: POST
**调用次数**: 1

### 38.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 157
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "IN_PARAM": {
        "PROJECT_ID": "CMGDZSICT20230614042"
      },
      "PAGE_INFO": {
        "PAGE_NUM": 1,
        "PAGE_SIZE": 5
      }
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 38.3 响应结果
**状态码**: 200 
**响应大小**: 1706 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:42:50 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184250462_1_1428",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "OUT_PARAM": [
          {
            "TEAM_TYPE": "1001",
            "STAFF_NAME": "许莹莹",
            "POST_NAME": "交付经理",
            "DEPT_NAME": "政企客户中心",
            "PHONE": "137****2131",
            "JOIN_TIME": "2025-07-10 09:24:56.0",
            "STAFF_ID": "xuyingying",
            "MEMBER_ID": "212283962",
            "DEPT_ID": "1000009143",
            "POST_ID": "1003",
            "STAFF_TYPE": "0"
          },
          {
            "TEAM_TYPE": "1000",
            "STAFF_NAME": "陈桂宝",
            "POST_NAME": "客户经理",
            "DEPT_NAME": "政企客户中心",
            "PHONE": "139****1818",
            "JOIN_TIME": "2025-06-10 11:44:15.0",
            "STAFF_ID": "chenguibao",
            "MEMBER_ID": "194557181",
            "DEPT_ID": "1000009143",
            "POST_ID": "1001",
            "STAFF_TYPE": "0"
          },
          {
            "TEAM_TYPE": "1001",
            "STAFF_NAME": "洪高梁",
            "POST_NAME": "交付经理",
            "DEPT_NAME": "项目集成室",
            "PHONE": "139****9992",
            "JOIN_TIME": "2025-06-06 10:46:55.0",
            "STAFF_ID": "honggaoliang",
            "MEMBER_ID": "192696717",
            "DEPT_ID": "35312",
            "POST_ID": "1003",
            "STAFF_TYPE": "0",
            "IS_CHIEF": "1"
          },
          {
            "TEAM_TYPE": "1001",
            "STAFF_NAME": "许莹莹",
            "POST_NAME": "客户经理",
            "DEPT_NAME": "政企客户中心",
            "PHONE": "137****2131",
            "JOIN_TIME": "2025-04-25 09:33:26.0",
            "STAFF_ID": "xuyingying",
            "MEMBER_ID": "174682459",
       
... (响应内容过长，已截断)
```

---

## 39. esop-inter-svc/inter/common/callGet/querySelectInfoByProjectId
**中文名称**: 按项目ID查询选择信息

### 39.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/esop-inter-svc/inter/common/callGet/querySelectInfoByProjectId?projectId=CMGDZSICT20230614042&`
**方法**: GET
**调用次数**: 4

### 39.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Host: dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
```

**查询参数 (Query Parameters)**:
```
projectId: CMGDZSICT20230614042
: 
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `projectId` | 页面URL参数或项目查询接口返回 |

### 39.3 响应结果
**状态码**: 200 
**响应大小**: 226802 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:43:05 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": null,
    "BODY": {
      "RUN_IP": null,
      "RUN_PORT": null,
      "REQUEST_ID": null,
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "code": "000000",
        "busiDate": "2025-07-14 18:43:05",
        "message": null,
        "resultBody": [
          {
            "isOverTime": "1",
            "applyReviewStatusValue": "已反馈结果",
            "selectBudget": "2585600.00",
            "selectStage": "甄选完成",
            "selectResultInfoVoList": [
              {
                "decideResult": null,
                "contactsPhone": "1382***3453",
                "reviewScore": null,
                "bidMoney": null,
                "partnerName": "中检赛辰技术服务有限公司",
                "selectApplyResultId": "1669256463789244416",
                "contactsEmail": "<EMAIL>",
                "partnerMsgId": "1575325938389794816",
                "contactsName": "何妹",
                "negotiationMoney": null
              },
              {
                "decideResult": null,
                "contactsPhone": "1382***4856",
                "reviewScore": null,
                "bidMoney": null,
                "partnerName": "中数通信息有限公司",
                "selectApplyResultId": "1669256463818604544",
                "contactsEmail": "<EMAIL>",
                "partnerMsgId": "1575325938414960640",
                "contactsName": "王涛",
                "negotiationMoney": null
              },
              {
                "decideResult": null,
                "contactsPhone": "1591***4959",
                "reviewScore": null,
                "bidMoney": null,
                "partnerName": "中粤通建技术有限公司",
                "selectApplyResultId": "1669256463826993152",
                "contactsEmail": "<EMAIL>",
                "partnerMsgId": "1575325938746310656",
                "contactsName": "程力斌",
                "negotiationMoney": 
... (响应内容过长，已截断)
```

---

## 40. saleCenterApp/excle/queryDocListByProject
**中文名称**: 按项目查询文档列表

### 40.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/excle/queryDocListByProject`
**方法**: POST
**调用次数**: 2

### 40.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 248
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "IN_PARAM": {
        "SALE_OPP_ID": "CMGDZSICT20230614042",
        "DOC_NAME": "",
        "UPLOAD_STAGE": "",
        "IS_QUERY_AUDIT": "0",
        "DOC_DESC": "",
        "DOC_STATUS_CD": "1000"
      },
      "PAGE_INFO": {
        "PAGE_NUM": 1,
        "PAGE_SIZE": 5
      }
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 40.3 响应结果
**状态码**: 200 
**响应大小**: 3307 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:43:57 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "**************",
      "REQUEST_ID": "20250714184357473_1_1439",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "OUT_PARAM": [
          {
            "DOC_NAME": "下游验收报告.pdf",
            "FORMAT_TYPE": "pdf",
            "BUSI_TYPE": "11",
            "BUSI_ID": "212518701",
            "DOC_ID": "0d06ab13-0a18-4e74-8a20-859af25da1b0",
            "DOC_TYPE": "G031",
            "DOC_DESC": "内部验收报告",
            "CREATE_DATE": "2025-07-10 17:27:46",
            "AUTHOR": "许莹莹",
            "DOC_TYPE_NAME": "验收报告",
            "SALE_OPP_ID": "CMGDZSICT20230614042",
            "FLOW_TASK_ID": "0000",
            "REL_ID": "212518703",
            "CREATE_STAFF": "1000005653",
            "SHOW_ORDER": 0,
            "STAGE_TYPE": "1002",
            "STAGE_NAME": "项目验收",
            "UPLOAD_STAGE": "5422,2032",
            "UPLOAD_STAGE_NAME": "交维发起,项目验收发起",
            "FILE_TYPE": "pdf",
            "DOC_SIZE": "315900",
            "DOC_SOURCE": "PC",
            "DOC_STATUS_CD": "1000",
            "VERSION_NUM": "V1"
          },
          {
            "DOC_NAME": "上游验收报告.pdf",
            "FORMAT_TYPE": "pdf",
            "BUSI_TYPE": "11",
            "BUSI_ID": "212518354",
            "DOC_ID": "171958a7-c701-4d47-a122-7ce6cfb18298",
            "DOC_TYPE": "G031",
            "DOC_DESC": "客户验收报告",
            "CREATE_DATE": "2025-07-10 17:27:30",
            "AUTHOR": "许莹莹",
            "DOC_TYPE_NAME": "验收报告",
            "SALE_OPP_ID": "CMGDZSICT20230614042",
            "FLOW_TASK_ID": "0000",
            "REL_ID": "212518597",
            "CREATE_STAFF": "1000005653",
            "SHOW_ORDER": 0,
            "STAGE_TYPE": "1002",
            "STAGE_NAME": "项目验收",
            "UP
... (响应内容过长，已截断)
```

---

## 41. esop-inter-svc/inter/common/callPost/querySelectFileByProjectCode
**中文名称**: 按项目编码查询选择文件

### 41.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/esop-inter-svc/inter/common/callPost/querySelectFileByProjectCode`
**方法**: POST
**调用次数**: 1

### 41.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 82
Content-Type: application/json;charset=UTF-8
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
```

**请求体 (Request Body)**:
```json
{
  "demandName": "",
  "fileName": "",
  "fileStep": "",
  "projectCode": "CMGDZSICT20230614042"
}
```

### 41.3 响应结果
**状态码**: 200 
**响应大小**: 2505 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:43:58 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": null,
    "BODY": {
      "RUN_IP": null,
      "RUN_PORT": null,
      "REQUEST_ID": null,
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "code": "000000",
        "busiDate": "2025-07-14 18:43:58",
        "message": null,
        "resultBody": [
          {
            "fileStep": "发起甄选需求",
            "uploadBy": "曾小敏",
            "fileName": "起湾需求书.pdf",
            "partnerName": "-",
            "uploadPlatform": "PC",
            "businessId": "1669250334921244672",
            "busineesType": null,
            "fileCreateTime": "2023-06-15 15:51:04",
            "selectDemandName": "中山移动宜居社区改造工程",
            "selectApplyResultId": null,
            "isFileChange": "0",
            "selectType": null,
            "fileType": "客户需求文件",
            "fileId": "1669251159701766144"
          },
          {
            "fileStep": "发起甄选需求",
            "uploadBy": "曾小敏",
            "fileName": "列收列支一览表（不含税）-起湾三线.pdf",
            "partnerName": "-",
            "uploadPlatform": "PC",
            "businessId": "1669250334933827584",
            "busineesType": null,
            "fileCreateTime": "2023-06-15 15:48:55",
            "selectDemandName": "中山移动宜居社区改造工程",
            "selectApplyResultId": null,
            "isFileChange": "0",
            "selectType": null,
            "fileType": "设计院方案建议书及造价文件（盖章版）",
            "fileId": "1669250617906741248"
          },
          {
            "fileStep": "制定甄选方案",
            "uploadBy": "曾小敏",
            "fileName": "关于起湾社区5G智慧改造工程项目的立项请示.doc",
            "partnerName": "-",
            "uploadPlatform": "PC",
            "businessId": "1669253129246261248",
            "busineesType": null,
            "fileCreateTime": "2023-06-15 16:04:34",
            "selectDemandName": "中山移动宜居社区改造工程",
            "selectApplyResultId": null,
            "isFileChange": "0",
            "selectType": null,
  
... (响应内容过长，已截断)
```

---

## 42. saleCenterApp/fileManage/queryMustUploadTree
**中文名称**: 查询必须上传文件树

### 42.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/fileManage/queryMustUploadTree`
**方法**: POST
**调用次数**: 1

### 42.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 103
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 42.3 响应结果
**状态码**: 200 
**响应大小**: 1445 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:43:58 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184358135_1_1343",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": [
        {
          "FIRST_NODE": "1001",
          "FIRST_NODE_NAME": "售前阶段",
          "UPLOAD_NUM": 19,
          "NEED_TYPE_NUM": 14,
          "MUST_TYPE_NUM": 15,
          "SECOND_NODE_FILE_INFO_LIST": [
            {
              "SECOND_NODE": "2332",
              "FIRST_NODE_NAME": "项目创建",
              "UPLOAD_NUM": 1,
              "NEED_TYPE_NUM": 1,
              "MUST_TYPE_NUM": 1
            },
            {
              "SECOND_NODE": "5324",
              "FIRST_NODE_NAME": "方案初稿",
              "UPLOAD_NUM": 2,
              "NEED_TYPE_NUM": 1,
              "MUST_TYPE_NUM": 1
            },
            {
              "SECOND_NODE": "2333",
              "FIRST_NODE_NAME": "方案制定",
              "UPLOAD_NUM": 4,
              "NEED_TYPE_NUM": 3,
              "MUST_TYPE_NUM": 3
            },
            {
              "SECOND_NODE": "6349",
              "FIRST_NODE_NAME": "技术评审二阶段",
              "UPLOAD_NUM": 0,
              "NEED_TYPE_NUM": 0,
              "MUST_TYPE_NUM": 1
            },
            {
              "SECOND_NODE": "5321",
              "FIRST_NODE_NAME": "资料交底发起",
              "UPLOAD_NUM": 4,
              "NEED_TYPE_NUM": 3,
              "MUST_TYPE_NUM": 3
            },
            {
              "SECOND_NODE": "2407",
              "FIRST_NODE_NAME": "立项决策(子环节)",
              "UPLOAD_NUM": 3,
              "NEED_TYPE_NUM": 2,
              "MUST_TYPE_NUM": 2
            },
            {
              "SECOND_NODE": "2020",
              "FIRST_NODE_NAME": "项目交底发起",
              "UPLOAD_NUM": 5,
              "NEED_TYPE_NUM": 4,
          
... (响应内容过长，已截断)
```

---

## 43. saleCenterApp/projectOrder/getProjectOrderList
**中文名称**: 获取项目订单列表

### 43.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectOrder/getProjectOrderList`
**方法**: POST
**调用次数**: 1

### 43.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 117
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "BUSI_INFO": {
        "PROJECT_ID": "CMGDZSICT20230614042"
      }
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 43.3 响应结果
**状态码**: 200 
**响应大小**: 954 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:44:31 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "**************",
      "REQUEST_ID": "20250714184431374_1_1421",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "PROJECT_ORDER_LIST": [
          {
            "PROJECT_ID": "CMGDZSICT20230614042",
            "ORDER_ID": "14438602",
            "PROD_ID": "9201",
            "CREATE_DATE": "2024-09-26 20:02:18",
            "PROD_NAME": "ICT开发集成项目",
            "BUSI_ACCEPT_METHOD": "1004",
            "ORDER_RESULT": "1001",
            "RESULT_DETAIL": "失败，原因:(odchkorderinfo，返回错误,code[985555],msg[该集团下用户【53711007304】已欠费【355916.80】元超过【0】月，请缴费后再办理业务])",
            "BUSI_ACCEPT_METHOD_NAME": "自动订购",
            "BUSI_ACCEPT_SYSTEM": "1000",
            "BUSI_ACCEPT_SYSTEM_NAME": "BRM",
            "SHOW_BTN": false,
            "CUST_ID": "87600000143547",
            "CUST_NAME": "江苏瑞英达通信工程有限公司",
            "CONTRACT_ID": "14217230",
            "CONTRACT_NO": "CMGD-ZS-202300950"
          }
        ]
      },
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 44. saleCenterApp/projectOrder/queryProjectOrderInstance
**中文名称**: 查询项目订单实例

### 44.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectOrder/queryProjectOrderInstance`
**方法**: POST
**调用次数**: 1

### 44.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 117
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "BUSI_INFO": {
        "PROJECT_ID": "CMGDZSICT20230614042"
      }
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 44.3 响应结果
**状态码**: 200 
**响应大小**: 1275 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:44:31 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "**************",
      "REQUEST_ID": "20250714184431375_1_1422",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "ORDER_INSTANCE_LIST": [
          {
            "PROD_ID": "9201",
            "PROD_NAME": "ICT开发集成项目",
            "BILL_ID": "53711007304",
            "INSTANCE_ID": "87600003696858",
            "CREATE_DATE": "2023-06-26 11:05:32",
            "BUSI_ACCEPT_METHOD": "1004",
            "BUSI_ACCEPT_METHOD_NAME": "自动订购",
            "BUSI_ACCEPT_SYSTEM": "1000",
            "BUSI_ACCEPT_SYSTEM_NAME": "BRM",
            "CUST_NAME": "江苏瑞英达通信工程有限公司",
            "CONTRACT_ID": "14217230",
            "STATUS_CD": "1100",
            "INSTANCE_STATUS": "欠费销户",
            "CONTRACT_NO": "CMGD-ZS-202300950",
            "START_DATE": "2023-06-22",
            "END_DATE": "2024-06-21"
          },
          {
            "PROD_ID": "9102",
            "PROD_NAME": "电路租用",
            "BILL_ID": "21301179453",
            "CREATE_DATE": "2024-08-16 11:41:54",
            "BUSI_ACCEPT_METHOD": "1000",
            "BUSI_ACCEPT_METHOD_NAME": "订单",
            "BUSI_ACCEPT_SYSTEM": "1000",
            "BUSI_ACCEPT_SYSTEM_NAME": "BRM",
            "CUST_NAME": "江苏瑞英达通信工程有限公司",
            "CONTRACT_ID": "14217230",
            "STATUS_CD": "1100",
            "INSTANCE_STATUS": "集团预约销户",
            "CONTRACT_NO": "CMGD-ZS-202300950",
            "IS_ADDITIONAL_RECORDING": "1",
            "START_DATE": "2023-06-22",
            "END_DATE": "2024-06-21"
          }
        ]
      },
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 45. saleCenterApp//common/staff/getSystemUserBossList
**中文名称**: 获取系统用户上级列表

### 45.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp//common/staff/getSystemUserBossList`
**方法**: POST
**调用次数**: 1

### 45.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 93
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "STAFF_CODE": "liaochulin"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 45.3 响应结果
**状态码**: 200 
**响应大小**: 460 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:44:31 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184431375_1_1323",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": [
        {
          "bossUserCode": "TZS0DA20249",
          "mainUserCode": "liaochulin",
          "bak2": "",
          "mainUserName": "廖楚林",
          "bak3": "",
          "statusCd": "1000",
          "bossUserStatus": "1",
          "id": 202301171631718,
          "bak1": "BOSS"
        }
      ],
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 46. saleCenterApp/projectImplement/queryProjectChangeOutList
**中文名称**: 查询项目变更输出列表

### 46.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectImplement/queryProjectChangeOutList`
**方法**: POST
**调用次数**: 2

### 46.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 252
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "IN_PARAM": {
        "REGION_CODE": "",
        "PROJECT_NO": "CMGDZSICT20230614042",
        "PROJECT_NAME": "",
        "CUST_ID": "",
        "CHANGE_TYPE": "",
        "START_DATE": "",
        "END_DATE": ""
      },
      "PAGE_INFO": {
        "PAGE_NUM": 1,
        "PAGE_SIZE": 5
      }
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 46.3 响应结果
**状态码**: 200 
**响应大小**: 323 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:44:49 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184449352_1_1501",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "OUT_PARAM": [],
        "PAGE_INFO": {
          "ROW_COUNT": 0
        }
      },
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 47. saleCenterApp/projectWeekly/showProjectWeeklyInformationList
**中文名称**: 显示项目周报信息列表

### 47.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectWeekly/showProjectWeeklyInformationList`
**方法**: POST
**调用次数**: 1

### 47.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 157
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "IN_PARAM": {
        "PROJECT_ID": "CMGDZSICT20230614042"
      },
      "PAGE_INFO": {
        "PAGE_NUM": 1,
        "PAGE_SIZE": 5
      }
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 47.3 响应结果
**状态码**: 200 
**响应大小**: 325 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:44:54 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "***************",
      "REQUEST_ID": "20250714184454649_1_1118",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "OUT_PARAM": [],
        "PAGE_INFO": {
          "ROW_COUNT": 0
        }
      },
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 48. saleCenterApp/projectWeekly/customerVisitList
**中文名称**: 客户拜访列表

### 48.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectWeekly/customerVisitList`
**方法**: POST
**调用次数**: 1

### 48.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 154
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "IN_PARAM": {
        "BUSI_ID": "CMGDZSICT20230614042"
      },
      "PAGE_INFO": {
        "PAGE_NUM": 1,
        "PAGE_SIZE": 5
      }
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 48.3 响应结果
**状态码**: 200 
**响应大小**: 323 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:44:58 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184458324_1_1521",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "OUT_PARAM": [],
        "PAGE_INFO": {
          "ROW_COUNT": 0
        }
      },
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 49. saleCenterApp/projectSupport/querySupportInfoListByCust
**中文名称**: 按客户查询支撑信息列表

### 49.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectSupport/querySupportInfoListByCust`
**方法**: POST
**调用次数**: 2

### 49.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 197
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "IN_PARAM": {
        "WORK_ID": "",
        "WORK_NAME": "",
        "OPP_ID": "",
        "PROJECT_ID": "CMGDZSICT20230614042"
      },
      "PAGE_INFO": {
        "PAGE_NUM": 1,
        "PAGE_SIZE": 5
      }
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 49.3 响应结果
**状态码**: 200 
**响应大小**: 324 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:45:00 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "**************",
      "REQUEST_ID": "20250714184500906_1_1437",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "OUT_PARAM": [],
        "PAGE_INFO": {
          "ROW_COUNT": 0
        }
      },
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 50. saleCenterApp/projectAudit/queryProjAuditInfo
**中文名称**: 查询项目审计信息

### 50.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectAudit/queryProjAuditInfo`
**方法**: POST
**调用次数**: 6

### 50.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 103
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 50.3 响应结果
**状态码**: 200 
**响应大小**: 283 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:45:03 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "***************",
      "REQUEST_ID": "20250714184503487_1_1134",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": [],
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 51. saleCenterApp/projectIncExp/isHistoryData
**中文名称**: 判断是否历史数据

### 51.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectIncExp/isHistoryData`
**方法**: POST
**调用次数**: 2

### 51.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 103
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 51.3 响应结果
**状态码**: 200 
**响应大小**: 284 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:46:06 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "**************",
      "REQUEST_ID": "20250714184606289_1_1232",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": true,
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 52. saleCenterApp/onlinePpt/isOnlinePPTOpen
**中文名称**: 判断在线PPT是否开启

### 52.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/onlinePpt/isOnlinePPTOpen`
**方法**: POST
**调用次数**: 2

### 52.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 103
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 52.3 响应结果
**状态码**: 200 
**响应大小**: 284 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:46:06 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184606366_1_1340",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": false,
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 53. saleCenterApp/abilityGradeManage/queryAbilityGradeInfo
**中文名称**: 查询能力等级信息

### 53.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/abilityGradeManage/queryAbilityGradeInfo`
**方法**: POST
**调用次数**: 1

### 53.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 103
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 53.3 响应结果
**状态码**: 200 
**响应大小**: 716 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:46:06 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184606566_1_1138",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "ABILITY_GRADE_ID": "38668424",
        "PROJECT_ID": "CMGDZSICT20230614042",
        "CALCULATE_GRADE": "L2",
        "IS_GRADE_FOUR": "20",
        "IS_GRADE_THREE": "20",
        "IS_GRADE_TWO": "10",
        "IS_GRADE_ONE": "10",
        "STATUS_CD": "1000",
        "STATUS_DATE": "2024-03-14 23:00:40",
        "CREATE_DATE": "2024-03-14 23:00:40",
        "UPDATE_STAFF": "zengxiaomin3",
        "UPDATE_DATE": "2025-05-29 22:40:03",
        "IS_GRADE_FOUR_DESC": "否",
        "IS_GRADE_THREE_DESC": "否",
        "IS_GRADE_TWO_DESC": "是",
        "IS_GRADE_ONE_DESC": "是"
      },
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 54. saleCenterApp/formulation/judgeProjectCustIsLegionImportant
**中文名称**: 判断项目客户是否为集团重要客户

### 54.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/formulation/judgeProjectCustIsLegionImportant`
**方法**: POST
**调用次数**: 1

### 54.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 103
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 54.3 响应结果
**状态码**: 200 
**响应大小**: 294 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:46:07 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "**************",
      "REQUEST_ID": "20250714184607194_1_1422",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "FLAG": false
      },
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 55. saleCenterApp/projectGradeEvaSvc/queryLegionInfo
**中文名称**: 查询集团信息

### 55.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectGradeEvaSvc/queryLegionInfo`
**方法**: POST
**调用次数**: 1

### 55.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 88
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": "CMGDZSICT20230614042"
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 55.3 响应结果
**状态码**: 200 
**响应大小**: 282 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:46:07 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "**************",
      "REQUEST_ID": "20250714184607195_1_1429",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {},
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 56. saleCenterApp/RevokeContractService/queryContractInfoList
**中文名称**: 查询撤销合同信息列表

### 56.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/RevokeContractService/queryContractInfoList`
**方法**: POST
**调用次数**: 5

### 56.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 151
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "CONTRACT_STATUS": "07",
      "INC_EXP_TYPE": "BALANCE",
      "PROJECT_ID": "CMGDZSICT20230614042"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 56.3 响应结果
**状态码**: 200 
**响应大小**: 3347 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:46:07 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "***************",
      "REQUEST_ID": "20250714184607333_1_1086",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": [
        {
          "CONTRACT_ID": "14217230",
          "PROJECT_ID": "CMGDZSICT20230614042",
          "CONTRACT_NO": "CMGD-ZS-202300950",
          "CONTRACT_NAME": "起湾社区5G智慧改造工程项目合同书",
          "CONTRACT_SERIAL_NO": "00212023039076",
          "DRAFT_TYPE": "OUT",
          "RETROACTIVE": "N",
          "SUPERIOR_SIGNATURE": "N",
          "CONTRACT_SUBJECT": "ERP",
          "CON_SIGNED_SUBJECT": "760",
          "INC_EXP_TYPE": "BALANCE",
          "CONTACT_TEL": "18824950008",
          "SIGN_TIME": "2023-06-22 08:00:00",
          "SIGNED_START_DATE": "2023-06-22 00:00:00",
          "SIGNED_END_DATE": "2024-06-21 00:00:00",
          "CONTRACT_PERIOD": "12",
          "CONTRACT_MONEY": 2810525.0,
          "CONTRACT_MONEY_EX_TAX": 2578463.3,
          "AMOUNT_TYPE": "CONTRACT_AMOUNT",
          "AMOUNT_INCLUDING_TAX": 2810525.0,
          "AMOUNT_EXCLUDING_TAX": 2578463.3,
          "TAX_AMOUNT": 232061.7,
          "AMOUNT_IMPROVE_INCLUDING_TAX": 2810525.0,
          "AMOUNT_IMPROVE_EXCLUDING_TAX": 2578463.3,
          "TAX_AMOUNT_IMPROVE": 232061.7,
          "CURRENCY": "CNY",
          "PAYMENT_DEPOSIT": "N",
          "IS_FRAMEWORK": "0",
          "CONTRACT_CONTENT": "依据《民法典》及相关法律法规的规定，甲乙双方在平等、互利的基础上，经充分协商，就“起湾社区“三线”改造工程（第三次）”项目合作实施事项达成一致意见，订立本合同，共同信守执行。",
          "CREATE_COMPANY_NO": "00210038000000000000",
          "CREATE_COMPANY_NAME": "中国移动广东公司\\中山分公司",
          "CREATE_DEPT_CODE": "00210038051051819537",
          "CREATE_DEPT_NAME": "中国移动广东公司\\中山分公司\\城区分公司\\政企客户中心\\DICT项目拓展室",
          "CREATE_USER_NAME": "<EMAIL>",
          "CREATE_DISPLAY_NAME": "曾
... (响应内容过长，已截断)
```

---

## 57. saleCenterApp/projectManage/queryProjectFlow
**中文名称**: 查询项目流程状态

### 57.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectManage/queryProjectFlow`
**方法**: POST
**调用次数**: 5

### 57.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 126
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "TYPE": "0",
      "PROJECT_ID": "CMGDZSICT20230614042",
      "VAILD": "0"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 57.3 响应结果
**状态码**: 200 
**响应大小**: 630 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:46:08 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184608304_1_1442",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "BUSI_FLOW_REL_ID": "14208932",
        "BUSI_ID": "14208921",
        "BUSI_TYPE": "11",
        "FLOW_ORDER_ID": "GD76020230620134354440109",
        "FLOW_SYSTEM": "FLOW_CENTER",
        "FLOW_PROD_ID": "GD_FAZDHS",
        "RESULT": "1",
        "RESULT_DESC": "通过",
        "STATUS_CD": "1100",
        "CREATE_STAFF": "zengxiaomin3",
        "CREATE_NAME": "曾小敏",
        "CREATE_DATE": "2023-06-20 13:43:57",
        "UPDATE_DATE": "2023-06-20 15:22:10"
      },
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 58. saleCenterApp/projectIncExp/getProjectIncExpSwitch
**中文名称**: 获取项目收支开关

### 58.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectIncExp/getProjectIncExpSwitch`
**方法**: POST
**调用次数**: 2

### 58.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 87
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "REGION_CODE": "760"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 58.3 响应结果
**状态码**: 200 
**响应大小**: 284 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:46:08 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "***************",
      "REQUEST_ID": "20250714184608453_1_1338",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": "Y",
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 59. saleCenterApp/fuseAbilitySvc/containVisualNetwork
**中文名称**: 包含可视化网络

### 59.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/fuseAbilitySvc/containVisualNetwork`
**方法**: POST
**调用次数**: 1

### 59.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 126
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042",
      "PROJECT_STAGE": "1001"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 59.3 响应结果
**状态码**: 200 
**响应大小**: 286 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:46:09 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "***************",
      "REQUEST_ID": "20250714184609077_1_1099",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": false,
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 60. saleCenterApp/SafetyAbilityManageService/queryIsSafetyProd
**中文名称**: 查询是否安全产品

### 60.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/SafetyAbilityManageService/queryIsSafetyProd`
**方法**: POST
**调用次数**: 1

### 60.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 126
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042",
      "PROJECT_STAGE": "1001"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 60.3 响应结果
**状态码**: 200 
**响应大小**: 282 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:46:09 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184609191_1_1537",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": "0",
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 61. saleCenterApp/formulation/qryHsolution
**中文名称**: 查询华为解决方案

### 61.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/formulation/qryHsolution`
**方法**: POST
**调用次数**: 1

### 61.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 103
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 61.3 响应结果
**状态码**: 200 
**响应大小**: 268 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:46:09 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "**************",
      "REQUEST_ID": "20250714184609225_1_1402",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 62. saleCenterApp/formulation/queryProjectBenefit
**中文名称**: 查询项目效益信息

### 62.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/formulation/queryProjectBenefit`
**方法**: POST
**调用次数**: 4

### 62.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 125
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042",
      "BENEFIT_TYPE": "1000"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 62.3 响应结果
**状态码**: 200 
**响应大小**: 2033 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:46:09 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184609289_1_1435",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "BENEFIT_ID": "b14208905",
        "PROJECT_ID": "CMGDZSICT20230614042",
        "BENEFIT_TYPE": "1000",
        "PROJECT_NET_PRESENT": 151500.55,
        "PROJECT_NET_PRESENT_IT": 144051.99,
        "PROJECT_NET_PRESENT_CT": 7448.57,
        "YIELD_DOUBLE": 6.14,
        "YIELD_DOUBLE_IT": 5.84,
        "YIELD_DOUBLE_CT": 858.34,
        "YEAR": "0.91976",
        "YEAR_IT": "0.92340",
        "YEAR_CT": "0.00000",
        "DYNAMIC_YEAR": "0.91976",
        "NET_PROFIT_MARGIN": 5.48,
        "IRR": "6.789",
        "EVALUATE_GRADE": "A",
        "TOTAL_INCOME": 2810525.0,
        "TOTAL_INVESTMENT": 2585000.0,
        "COMMU_OPERATOR_INCOME": 2810525.0,
        "COMMU_OPERATOR_INVESTMENT": 2585000.0,
        "IT_INVESTMENT": 0.0,
        "BNET_SPLIT_COST": 0.0,
        "PROJECT_VAT": 232061.7,
        "STRATEGIC_VALUE_SCORE": 77.0,
        "IN_MARGIN_RATE": 8.02,
        "IN_MARGIN_RATE_IT": 7.66,
        "IN_MARGIN_RATE_CT": 100.0,
        "IS_IN_OWN_ABILITY": "1000",
        "STATUS_CD": "1000",
        "CREATE_STAFF": "1000030682",
        "CREATE_NAME": "曾小敏",
        "CREATE_DATE": "2023-06-20 13:42:24",
        "UPDATE_DATE": "2025-04-23 19:38:52",
        "TOTAL_INCOME_EX_TAX": 2578463.3,
        "TOTAL_INVESTMENT_EX_TAX": 2371559.63,
        "IT_INCOME": 2799437.0,
        "IT_INCOME_EX_TAX": 2568290.82,
        "CT_INCOME": 11088.0,
        "CT_INCOME_EX_TAX": 10172.48,
        "CO_INCOM_EX_TAX": 2578463.3,
        "CO_INVESTMENT_EX_TAX": 2371559.63,
        "IT_INPUT": 2585000.0,
        "IT_INPUT_EX_TAX": 2371559.63,
        "CT_INPUT": 0.0,
        "CT_INPUT_EX_TAX": 0.0,

... (响应内容过长，已截断)
```

---

## 63. esop-inter-svc/inter/hwOpp/ebus/queryAllOppListForDict
**中文名称**: 查询所有商机列表字典

### 63.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/esop-inter-svc/inter/hwOpp/ebus/queryAllOppListForDict`
**方法**: POST
**调用次数**: 1

### 63.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 250
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "BUSI_INFO": {
        "oppId": "76023060810091275",
        "roamRegion": "",
        "custName": "",
        "oppName": "",
        "oppMgr": "",
        "isRegistered": ""
      },
      "OPR_INFO": {
        "loginNo": ""
      },
      "PAGE_INFO": {
        "PAGE_NUM": 1,
        "PAGE_SIZE": 10
      }
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `oppId` | saleCenterApp/projectManage/queryProjectInfo 接口返回的商机信息 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 63.3 响应结果
**状态码**: 200 
**响应大小**: 2719 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:46:09 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "BODY": {
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "OUT_PARAM": [
          {
            "commonBudget": "281000000",
            "toDictState": 0,
            "firstScene": "NS_OTHER",
            "groupId": "2001330585",
            "shareScope": "city",
            "cusRequireLink": "李妙娜",
            "source": "manager",
            "oppMgrId": "TZS03E0X16",
            "prodList": [],
            "isActive": "1",
            "isSpecialDebt": "0",
            "isOldForNew": "0",
            "stateName": "完成",
            "secondSceneName": "其他",
            "oppName": "东区街道起湾社区三线整治服务项目",
            "state": "finish",
            "isIncludeSolution": "0",
            "isPushedDict": "0",
            "publicCountyCode": "20076009999",
            "buttonList": [],
            "oppMgrName": "李妙娜",
            "publicCountyName": "市管",
            "pushType": "未转化",
            "isVideoNet": "0",
            "phone": "13823960608",
            "commonOppMgr": "TZS03E0X16",
            "startupDate": "2023-07-08",
            "custType": "2",
            "isRegistered": "1",
            "bidAmt": "281000000",
            "region": "760",
            "curHandlerName": "李妙娜",
            "cityCode": "760",
            "endState": "转化成功",
            "creatorName": "李妙娜",
            "opportunityCustomer": {
              "netGridCodeName": "东区",
              "groupId": "2001330585",
              "assistServName": "李妙娜",
              "valueLevelId": "03",
              "groupOwnOrg": "NETGRID",
              "contactMan": "梁月嫦",
              "custId": "87600000143547",
              "netGridCode": "ZS0120",
              "unitId": "ZS0120",
              "currentCustPhone": "13823960608",
              "assistCustMgr": "TZS03E0X16",
              "vocationKindName": "农商行业",
              "servDept": "IniOrgCustMgr",
              "address": "广东中山市火炬开发区88号大数据中心A座6楼",
              "unitName": "东区",
              "mobileNo": "138239245
... (响应内容过长，已截断)
```

---

## 64. saleCenterApp/fuseAbilitySvc/containInternetOfThingsMoney
**中文名称**: 包含物联网资金

### 64.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/fuseAbilitySvc/containInternetOfThingsMoney`
**方法**: POST
**调用次数**: 1

### 64.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 126
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042",
      "PROJECT_STAGE": "1001"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 64.3 响应结果
**状态码**: 200 
**响应大小**: 284 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:46:09 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184609732_1_1404",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": false,
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 65. iom-app-svc/iom/api/extflowceter/queryOrderTrack
**中文名称**: 查询订单跟踪信息

### 65.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/iom-app-svc/iom/api/extflowceter/queryOrderTrack`
**方法**: POST
**调用次数**: 1

### 65.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 117
Content-Type: application/json;charset=UTF-8
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "BODY": {
      "BUSI_INFO": {
        "orderId": "GD76020230620134354440109",
        "province": "200",
        "region": "760"
      },
      "OPR_INFO": {}
    }
  }
}
```

### 65.3 响应结果
**状态码**: 200 
**响应大小**: 12315 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:46:16 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A"
    },
    "BODY": {
      "RUN_IP": "**************",
      "REQUEST_ID": "20250714184609853_1_309",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "approvalType": "GD_FAZDHS",
        "busiType": "GD_FAZDHS",
        "busiSubType": "GD_FAZDHS",
        "prodId": "GD99920220625084257120625",
        "linkType": "GD_FAZDHS",
        "titleName": "东区街道起湾社区三线整治服务项目",
        "starter": "zengxiaomin3",
        "userGroup": "DICT项目拓展室",
        "departMent": "政企客户中心",
        "flowstate": "3",
        "modeType": "0",
        "orderTrackList": [
          {
            "curReviewLevelList": [
              {
                "curReviewLevel": 0,
                "curReviewNodeList": [
                  {
                    "curReviewLevel": 0,
                    "curRiviewRoleId": "APPLYROLE",
                    "endDate": "2023-06-20 13:43:56",
                    "showCancel": "0",
                    "statetimes": 0,
                    "showConfig": "0",
                    "curEmployeeClass": "室经理",
                    "curRiviewOperName": "曾小敏",
                    "doRiviewName": "起草",
                    "curUserGroup": "综合支撑室",
                    "flowDefineId": "GD76020230329210221062590",
                    "riviewState": "1",
                    "curRiviewName": "提交申请",
                    "curRiviewId": "GD76020230329102222062510",
                    "totalSeq": 1,
                    "curRiviewOperClass": "战略客户中心",
                    "flowName": "方案会审流程",
                    "toSubmit": "欧庆枢",
                    "curRiviewOperID": "zengxiaomin3",
                    "curReviewLevelName": "提交申请",
                    "curRiviewRoleName": "提交申请",
                    "comment": "提交申请",
                    "curRiviewOperTel": "18824950008",
                    "startDate": "2023-06-20 13:43:53",
                    "taskId": 
... (响应内容过长，已截断)
```

---

## 66. saleCenterApp/fuseAbilitySvc/queryYdyTrade
**中文名称**: 查询移动云交易

### 66.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/fuseAbilitySvc/queryYdyTrade`
**方法**: POST
**调用次数**: 1

### 66.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 99
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "TRADE": "NS",
      "SALE_SOLUTION": ""
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 66.3 响应结果
**状态码**: 200 
**响应大小**: 4050 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:46:10 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184610523_1_1530",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": [
        {
          "TRADE": "NS",
          "TRADE_DESC": "农商",
          "SALE_SOLUTION": "NS_01",
          "SALE_SOLUTION_DESC": "文旅云",
          "SALE_SOLUTION_INTRODUCTION": "面向各级文旅主管部门、景区、博物馆等行业主体打造集管理、服务、营销等为一体的全栈式平台，为客户提供产业监测、行业监管、大数据分析、一机游、营销宣传等一揽子服务。",
          "STATUS_CD": "1000"
        },
        {
          "TRADE": "NS",
          "TRADE_DESC": "农商",
          "SALE_SOLUTION": "NS_02",
          "SALE_SOLUTION_DESC": "云电竞",
          "SALE_SOLUTION_INTRODUCTION": "响应文旅部上网服务行业上云政策及全国试点专项行动，面向网吧/网咖、电竞酒店等，基于移动云丰富的边缘算力资源及云电脑技术，提供存储上云、算力上云以及电竞级云电脑服务，同时联合生态数字人、数字孪生、AI等相关应用，将算力资源广泛复用于文娱互动等云渲染场景，帮助企业降本增效。",
          "STATUS_CD": "1000"
        },
        {
          "TRADE": "NS",
          "TRADE_DESC": "农商",
          "SALE_SOLUTION": "NS_03",
          "SALE_SOLUTION_DESC": "信创政务云",
          "SALE_SOLUTION_INTRODUCTION": "聚焦传统政务云信创替代、垂直委办应用适配上云以及政务信创办公等需求，通过适配主流国产软硬件生态，满足全链路自主可控，打造专/私/边/公全场景能力体系，为用户提供“一云多芯、全栈自主、安全可信、生态丰富”的云基础设施。",
          "STATUS_CD": "1000"
        },
        {
          "TRADE": "NS",
          "TRADE_DESC": "农商",
          "SALE_SOLUTION": "NS_04",
          "SALE_SOLUTION_DESC": "智算超算",
          "SALE_SOLUTION_INTRODUCTION": "基于移动云专属云服务，实现高校底层算力统一纳管，专人运维，将超算能力与智算能力结合，实现数据模型训练到模型推理及验证的无缝衔接。通过AI智算集群、超算集群、高速网络，组建高效科研环境。有效提升大学科研实训效率，降低师生的科研门槛。",
          "STATUS_CD": "1000"
        },
        {
          "TRADE": "NS",
          "TRADE_DESC": "农商",
          "SALE_SOLUTION": "NS_05",
          "SALE_SOLUTION_DESC": "基于移动云私有云底座构建容灾云解决方案",
          "SALE_SOLUTION_INTRODUCTION": "基于移动私有云底座建设容灾云，帮助用户实现业务系统云端容灾保护。当源端数据中心发生灾难时，容灾云可实时保护源端数据并快速接管源端业务，实现客户业务连续性。提供包含容
... (响应内容过长，已截断)
```

---

## 67. saleCenterApp/projectDecision/isHistoryData
**中文名称**: 项目决策历史数据判断

### 67.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectDecision/isHistoryData`
**方法**: POST
**调用次数**: 1

### 67.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 103
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 67.3 响应结果
**状态码**: 200 
**响应大小**: 284 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:46:35 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "**************",
      "REQUEST_ID": "20250714184635640_1_1381",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": true,
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 68. saleCenterApp/projectDecision/cancelOASwitch
**中文名称**: 取消OA开关

### 68.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectDecision/cancelOASwitch`
**方法**: POST
**调用次数**: 1

### 68.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 68
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {}
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 68.3 响应结果
**状态码**: 200 
**响应大小**: 235 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:46:35 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "***************",
      "REQUEST_ID": "20250714184635804_1_1076",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": "Y",
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 69. saleCenterApp/projectReview/queryProjectEstimatedExp
**中文名称**: 查询项目预估费用

### 69.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectReview/queryProjectEstimatedExp`
**方法**: POST
**调用次数**: 1

### 69.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 123
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042",
      "MONEY_UNIT": "2000"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 69.3 响应结果
**状态码**: 200 
**响应大小**: 367 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:46:35 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184635731_1_1164",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "TOTAL_PAYMENT": 0.0,
        "OUR_PAYMENT": 0.0,
        "THIRD_PARTY_PAYMENT": 0.0,
        "PAYMENT_DETAIL": []
      },
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 70. saleCenterApp/projectDecision/queryProjectDecisionInfo
**中文名称**: 查询项目决策信息

### 70.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectDecision/queryProjectDecisionInfo`
**方法**: POST
**调用次数**: 1

### 70.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 122
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042",
      "BDM_CLASS": "1000"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 70.3 响应结果
**状态码**: 200 
**响应大小**: 2237 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:46:36 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "***************",
      "REQUEST_ID": "20250714184635731_1_1328",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "SALE_OPP_ID": "",
        "PROJECT_ID": "CMGDZSICT20230614042",
        "PROJECT_NAME": "东区街道起湾社区三线整治服务项目",
        "BDM_CLASS": "1000",
        "PROJECT_OVERVIEW": {
          "PROJECT_ID": "CMGDZSICT20230614042",
          "PROJECT_NAME": "东区街道起湾社区三线整治服务项目",
          "CUST_NAME": "江苏瑞英达通信工程有限公司",
          "CONTRACT_PERIOD": "1",
          "CONTRACT_DURATION": "4",
          "ESTIMATED_AMOUNT": "281.00",
          "CUST_LEVEL": "C",
          "ELECTION_MODE": "1030",
          "ELECTION_MODE_DESC": "单一来源采购",
          "BID_FLAG": "0",
          "BID_FLAG_DESC": "否"
        },
        "PROJECT_CONSTRUCT": {
          "IT_DEMAND": [
            {
              "DELIVERY_TYPE": "1000",
              "DELIVERY_DESC": ""
            },
            {
              "DELIVERY_TYPE": "1001",
              "DELIVERY_CLASSES": [
                "1000"
              ],
              "DELIVERY_CLASSES_DESC": "安装",
              "DELIVERY_DESC": "该项目面向基层乡镇治理场景，为后期打造一体的5G数字社区综合服务应用打下基础，包括乡村安防管控、综合治理等场景需求。具体内容：前期起湾社区光缆路由管道建设，运营商融合光缆架空敷设及管道敷设施工，融合光缆交接箱建设以及广电网络光缆敷设施工，后期利用5G网络优势，为社区建立5G专网，以及5G双域专网，为社区提供惠农服务移动端入口，为政府人员提供基层治理工具，借助5G低时延、大带宽特性，提供乡村综治等服务。"
            },
            {
              "DELIVERY_TYPE": "1003",
              "DELIVERY_DESC": ""
            }
          ],
          "CT_DEMAND": [
            {
              "DELIVERY_TYPE": "1004",
              "DELIVERY_DESC": "提供5G双域专网、5G专网免费体验半年。数据专线一年。"
            }
          ]
        },
        "BUSINESS_MODEL": {
          "BUSINESS_MODEL_DETAIL": [
            {
              "EQUIPMENT_TYPE": "1002",
              "EQUIPMENT_TYPE_D
... (响应内容过长，已截断)
```

---

## 71. saleCenterApp/projectReview/queryProjectIncDetailList
**中文名称**: 查询项目收入明细列表

### 71.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectReview/queryProjectIncDetailList`
**方法**: POST
**调用次数**: 1

### 71.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 146
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042",
      "PROJECT_STAGE": "1000",
      "MONEY_UNIT": "2000"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 71.3 响应结果
**状态码**: 200 
**响应大小**: 4522 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:46:35 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184635772_1_1493",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "EXPENSE_ID": "14208874",
        "PROJECT_ID": "CMGDZSICT20230614042",
        "PLAN_TOTAL_INCOME": "281.05",
        "IT_PLAN_TOTAL_INCOME": "279.94",
        "CT_PLAN_TOTAL_INCOME": "1.11",
        "CREATE_STAFF": "1000030682",
        "CREATE_NAME": "曾小敏",
        "CREATE_DATE": "2023-06-20 13:42:23",
        "PLAN_TOTAL_VAT": "23.21",
        "TOTAL_MONEY_EX_TAX": "257.85",
        "PROJECT_STAGE": "1000",
        "PLAN_COMMU_OPERATOR_INCOME": "281.05",
        "EXPENSE_PLAN_STATUS": "1000",
        "EXPENSE_INFO_LIST": [
          {
            "PROD_ID": "9102",
            "PROD_NAME": "电路租用",
            "SUBJECT_CODE": "13598",
            "SUBJECT_NAME": "数字电路月租费",
            "SUBJECT_CLASS": "2000",
            "VAT_RATE": "0.09",
            "MONEY": "1.11",
            "PROD_COUNT": "1",
            "DISCOUNT_RATE": "1.00",
            "CYCLE_TYPE": "20",
            "CYCLE_COUNT": "12",
            "PLAN_BILL_START_TIME": "2023-06-01 00:00:00",
            "COLL_CYCLE_TYPE": "20",
            "COLL_CYCLE_COUNT": "12",
            "PLAN_COLL_START_TIME": "2023-06-01 00:00:00",
            "VAT": "0.09",
            "MONEY_EX_TAX": "1.02",
            "EXPENSE_DETAIL_ID": "14208807",
            "INCOME_CONFIRM_STATUS": "1000",
            "CT_OR_IT": "2000",
            "IS_SPEC_MARKET": "1001",
            "STATUS_CD": "1000",
            "SUBJECT_BELONG": "通服",
            "PROD_LABLES": "1001",
            "PROD_LABLES_DESC": "专线",
            "GK_PROD_ID": "120101",
            "GK_PROD_NAME": "数据及互联网接入类-数据专线-本地数据专线",
            "COA_CODE": "0",
            "BILL_IN
... (响应内容过长，已截断)
```

---

## 72. saleCenterApp/provincialCompanyProjectDecision/decisionResult
**中文名称**: 省公司项目决策结果

### 72.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/provincialCompanyProjectDecision/decisionResult`
**方法**: POST
**调用次数**: 1

### 72.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 122
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042",
      "BDM_CLASS": "1000"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 72.3 响应结果
**状态码**: 200 
**响应大小**: 483 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:46:35 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "**************",
      "REQUEST_ID": "20250714184635943_1_1379",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "DECISION_TIME": "2023-06-20 00:00:00",
        "PROJECT_DECISION_RESULT": "通过",
        "DECISION_MODE": "1000",
        "DECISION_MODE_DESC": "呈批件",
        "DECISION_DOC_NO": "[2023]第1413号",
        "CREATE_DATE": "2023-06-20 15:36:46"
      },
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 73. saleCenterApp/projectDisclosure/queryTsolution
**中文名称**: 查询技术方案

### 73.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectDisclosure/queryTsolution`
**方法**: POST
**调用次数**: 2

### 73.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 103
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 73.3 响应结果
**状态码**: 200 
**响应大小**: 281 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:46:56 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184656759_1_1323",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": [],
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 74. saleCenterApp/contractManage/qryContract
**中文名称**: 查询合同基本信息

### 74.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/contractManage/qryContract`
**方法**: POST
**调用次数**: 2

### 74.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 128
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042",
      "INC_EXP_TYPE": "BALANCE"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 74.3 响应结果
**状态码**: 200 
**响应大小**: 404 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:47:18 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "**************",
      "REQUEST_ID": "20250714184718205_1_1385",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": [
        {
          "CONTRACT_ID": "14217230",
          "INC_EXP_TYPE": "BALANCE",
          "CONTRACT_MONEY": "2810525.00",
          "CONTRACT_STATUS": "07",
          "RETROACTIVE": "N"
        }
      ],
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 75. saleCenterApp/projectDisclosure/queryProjectDisclosure
**中文名称**: 查询项目披露信息

### 75.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectDisclosure/queryProjectDisclosure`
**方法**: POST
**调用次数**: 2

### 75.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 103
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 75.3 响应结果
**状态码**: 200 
**响应大小**: 839 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:47:18 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184718209_1_1451",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "DOC_TRANSFER_ID": "14435056",
        "PROJECT_ID": "CMGDZSICT20230614042",
        "TRANSFER_TIME": "2023-06-26 10:40:12",
        "PROJECT_START_BASIS": "10",
        "DOC_TRANSFER_DESC": "已完成合同签订。",
        "CONTRACT_PERIOD": "4月",
        "DELIVERY_DATE": "2025-07-31 00:00:00",
        "DELIVERY_LIST": "G001,G002,G033,G004,G011,G022,G017",
        "DOC_TRANSFER_STATUS": "1001",
        "STATUS_CD": "1000",
        "STATUS_DATE": "2023-06-26 10:39:42",
        "CREATE_STAFF": "1000030682",
        "CREATE_NAME": "曾小敏",
        "CREATE_DATE": "2023-06-26 10:39:42",
        "UPDATE_STAFF": "zengxiaomin3",
        "UPDATE_NAME": "曾小敏",
        "UPDATE_DATE": "2023-06-26 10:40:12"
      },
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 76. saleCenterApp/autoIntegration/queryAbilityMapByProject
**中文名称**: 按项目查询能力地图

### 76.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/autoIntegration/queryAbilityMapByProject`
**方法**: POST
**调用次数**: 1

### 76.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 103
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 76.3 响应结果
**状态码**: 200 
**响应大小**: 578 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:47:43 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "**************",
      "REQUEST_ID": "20250714184743499_1_1434",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": [
        {
          "PROJECT_ID": "CMGDZSICT20230614042",
          "FIRST_ABILITY": "应用集成",
          "SECOND_ABILITY": "5G应用集成",
          "THREE_ABILITY": "5G应用集成",
          "ABILITY_MAP_ID": "10002",
          "ABILITY_INFO_ID": "10001",
          "CONTENT_LIST": [
            {
              "MAP_ID": "10002",
              "IS_REQUIRE": "1",
              "CONTENT": "5G专网所承接客户侧业务的测试。"
            }
          ]
        }
      ],
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 77. saleCenterApp/incomeManage/queryMobliePoliceInfo
**中文名称**: 查询移动警务信息

### 77.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/incomeManage/queryMobliePoliceInfo`
**方法**: POST
**调用次数**: 1

### 77.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 103
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 77.3 响应结果
**状态码**: 200 
**响应大小**: 281 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:47:43 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184743541_1_1521",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {},
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 78. saleCenterApp//preparation/queryProjectStart
**中文名称**: 查询项目启动信息

### 78.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp//preparation/queryProjectStart`
**方法**: POST
**调用次数**: 1

### 78.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 103
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 78.3 响应结果
**状态码**: 200 
**响应大小**: 434 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:47:43 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "***************",
      "REQUEST_ID": "20250714184743535_1_1335",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "START_MEETING_TIME": "2023-06-25 00:00:00",
        "PLAN_START_DATE": "2023-06-26 00:00:00",
        "DELAY_TIME": "0",
        "DELAY_RESION": "",
        "REMARK": "",
        "IS_JOINT_AUDIT": "20"
      },
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 79. saleCenterApp/preparation/queryTaskDisassemble
**中文名称**: 查询任务分解信息

### 79.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/preparation/queryTaskDisassemble`
**方法**: POST
**调用次数**: 1

### 79.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 119
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042",
      "YES_OR_NO": "N"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 79.3 响应结果
**状态码**: 200 
**响应大小**: 1213 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:47:43 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "***************",
      "REQUEST_ID": "20250714184743809_1_1346",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "INTEGRATE_IN_LIST": [],
        "OTHER_TABLE_IN_LIST": [],
        "SAFE_TABLE_LIST": [],
        "WORK_TABLE_LIST": [],
        "ITLIST": [
          {
            "PLAN_TYPE": "30",
            "PLAN_TYPE_DESC": "安装服务",
            "PLAN_NAME": "安装服务",
            "PLAN_DESC": "安装服务",
            "REQUIRE_START_DATE": "2023-06-26 00:00:00",
            "REQUIRE_FINISH_DATE": "2023-10-26 23:59:59",
            "PLAN_DEAL_STAFF": "zengxiaomin3",
            "PLAN_DEAL_NAME": "曾小敏",
            "DELIVERY_TYPE": "1",
            "PLAN_ID": "14438582",
            "PLAN_LEVEL": "1",
            "PARENT_PLAN_ID": "0",
            "CHILDRENS": [],
            "STAFF_TYPE": "0",
            "PLAN_MODE": "10",
            "SUBJECT_TYPE": "5000",
            "SUBJECT_TYPE_DESC": "ICT安装服务"
          }
        ],
        "CTLIST": [
          {
            "PROD_ID": "9102",
            "PROD_NUM": 1,
            "REQUIRE_START_DATE": "2023-07-26 00:00:00",
            "REQUIRE_FINISH_DATE": "2023-09-26 23:59:59",
            "PLAN_TYPE": "50",
            "PLAN_TYPE_DESC": "CT产品",
            "PLAN_NAME": "电路租用",
            "PLAN_DEAL_STAFF": "limiaona",
            "PLAN_DEAL_NAME": "李妙娜",
            "DELIVERY_TYPE": "0",
            "PLAN_ID": "14438581",
            "PLAN_LEVEL": "1",
            "PARENT_PLAN_ID": "0",
            "CHILDRENS": [],
            "STAFF_TYPE": "0",
            "PLAN_MODE": "10"
          }
        ]
      },
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 80. saleCenterApp//preparation/queryTotalProcess
**中文名称**: 查询总体进度

### 80.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp//preparation/queryTotalProcess`
**方法**: POST
**调用次数**: 2

### 80.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 103
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 80.3 响应结果
**状态码**: 200 
**响应大小**: 287 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:48:13 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184813593_1_1336",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": "100.00",
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 81. saleCenterApp/preparation/queryPlanAndSubjectTable
**中文名称**: 查询计划和科目表

### 81.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/preparation/queryPlanAndSubjectTable`
**方法**: POST
**调用次数**: 1

### 81.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 127
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0=?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJJU19DT05ORUNURURfT1BQIjoiMSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiQk9TU19QUk9KRUNUX0lEIjoiSTIzMzAyMTI2NjAxMTE0In0%3D
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "CMGDZSICT20230614042",
      "IS_CONFIRM_FLAG": false
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 页面URL参数或初始化参数 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 81.3 响应结果
**状态码**: 200 
**响应大小**: 1069 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:48:15 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184813817_1_1506",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": [
        {
          "SUBJECT_CLASS": "5000",
          "SUBJECT_CLASS_DESC": "ICT安装服务",
          "PLAN_PROGRESS_RATIO": 100
        },
        {
          "SUBJECT_CLASS": "3000",
          "SUBJECT_CLASS_DESC": "ICT设备（含套软）",
          "PLAN_PROGRESS_RATIO": 0
        },
        {
          "SUBJECT_CLASS": "4000",
          "SUBJECT_CLASS_DESC": "ICT集成服务",
          "PLAN_PROGRESS_RATIO": 0
        },
        {
          "SUBJECT_CLASS": "6000",
          "SUBJECT_CLASS_DESC": "运维服务",
          "PLAN_PROGRESS_RATIO": 0
        },
        {
          "SUBJECT_CLASS": "7000",
          "SUBJECT_CLASS_DESC": "运营服务",
          "PLAN_PROGRESS_RATIO": 0
        },
        {
          "SUBJECT_CLASS": "8000",
          "SUBJECT_CLASS_DESC": "软件开发",
          "PLAN_PROGRESS_RATIO": 0
        },
        {
          "SUBJECT_CLASS": "9000",
          "SUBJECT_CLASS_DESC": "其他部分",
          "PLAN_PROGRESS_RATIO": 0
        },
        {
          "SUBJECT_CLASS": "9100",
          "SUBJECT_CLASS_DESC": "自主集成服务",
          "PLAN_PROGRESS_RATIO": 0
        },
        {
          "SUBJECT_CLASS": "9200",
          "SUBJECT_CLASS_DESC": "9one产品",
          "PLAN_PROGRESS_RATIO": 0
        }
      ],
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 82. saleCenterApp/contractManage/queryContractInfo
**中文名称**: 查询合同详细信息

### 82.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/contractManage/queryContractInfo`
**方法**: POST
**调用次数**: 1

### 82.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 110
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/industryFortuneManage/contractManage/revenueContractDetail?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJDT05UUkFDVF9JRCI6IjE0MjE3MjMwIiwiSU5DX0VYUF9UWVBFIjoiQkFMQU5DRSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiUkVUUk9BQ1RJVkUiOiJOIiwiUFJPSkVDVF9DT05UUkFDVF9UWVBFIjoiMzAwMCIsInBhdGgiOiJmdWxsVmlldyJ9
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "CONTRACT_ID": "14217230",
      "RETROACTIVE": "N"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `CONTRACT_ID` | saleCenterApp/contractManage/qryContractByProject 接口返回 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 82.3 响应结果
**状态码**: 200 
**响应大小**: 3395 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:49:19 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184919814_1_1526",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "CONTRACT_ID": "14217230",
        "PROJECT_ID": "CMGDZSICT20230614042",
        "CONTRACT_NO": "CMGD-ZS-202300950",
        "CONTRACT_NAME": "起湾社区5G智慧改造工程项目合同书",
        "CONTRACT_SERIAL_NO": "00212023039076",
        "DRAFT_TYPE": "OUT",
        "RETROACTIVE": "N",
        "SUPERIOR_SIGNATURE": "N",
        "CONTRACT_SUBJECT": "ERP",
        "CON_SIGNED_SUBJECT": "760",
        "INC_EXP_TYPE": "BALANCE",
        "CONTACT_TEL": "18824950008",
        "SIGN_TIME": "2023-06-22 08:00:00",
        "SIGNED_START_DATE": "2023-06-22 00:00:00",
        "SIGNED_END_DATE": "2024-06-21 00:00:00",
        "CONTRACT_PERIOD": "12",
        "CONTRACT_MONEY": 2810525.0,
        "CONTRACT_MONEY_EX_TAX": 2578463.3,
        "AMOUNT_TYPE": "CONTRACT_AMOUNT",
        "AMOUNT_INCLUDING_TAX": 2810525.0,
        "AMOUNT_EXCLUDING_TAX": 2578463.3,
        "TAX_AMOUNT": 232061.7,
        "AMOUNT_IMPROVE_INCLUDING_TAX": 2810525.0,
        "AMOUNT_IMPROVE_EXCLUDING_TAX": 2578463.3,
        "TAX_AMOUNT_IMPROVE": 232061.7,
        "CURRENCY": "CNY",
        "PAYMENT_DEPOSIT": "N",
        "IS_FRAMEWORK": "0",
        "CONTRACT_CONTENT": "依据《民法典》及相关法律法规的规定，甲乙双方在平等、互利的基础上，经充分协商，就“起湾社区“三线”改造工程（第三次）”项目合作实施事项达成一致意见，订立本合同，共同信守执行。",
        "CREATE_COMPANY_NO": "00210038000000000000",
        "CREATE_COMPANY_NAME": "中国移动广东公司\\中山分公司",
        "CREATE_DEPT_CODE": "00210038051051819537",
        "CREATE_DEPT_NAME": "中国移动广东公司\\中山分公司\\城区分公司\\政企客户中心\\DICT项目拓展室",
        "CREATE_USER_NAME": "<EMAIL>",
        "CREATE_DISPLAY_NAME": "曾小敏",
        "ICT_PROJECT_URL": "*******",
        "CONTRACT_URL": "http://cms.hq.
... (响应内容过长，已截断)
```

---

## 83. saleCenterApp/contractManage/queryAgreementContract
**中文名称**: 查询协议合同信息

### 83.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/contractManage/queryAgreementContract`
**方法**: POST
**调用次数**: 1

### 83.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 167
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/industryFortuneManage/contractManage/revenueContractDetail?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJDT05UUkFDVF9JRCI6IjE0MjE3MjMwIiwiSU5DX0VYUF9UWVBFIjoiQkFMQU5DRSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiUkVUUk9BQ1RJVkUiOiJOIiwiUFJPSkVDVF9DT05UUkFDVF9UWVBFIjoiMzAwMCIsInBhdGgiOiJmdWxsVmlldyJ9
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "PROJECT_ID": "11111111111111111",
      "INC_EXP_TYPE": "",
      "CONTRACT_NO": "CMGD-ZS-202300950",
      "MORE_TYPE": ""
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `PROJECT_ID` | 项目查询接口返回 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 83.3 响应结果
**状态码**: 200 
**响应大小**: 281 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:49:20 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184920408_1_1099",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": [],
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 84. saleCenterApp/incomeManage/queryExpensePlanInfo
**中文名称**: 查询费用计划信息

### 84.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/incomeManage/queryExpensePlanInfo`
**方法**: POST
**调用次数**: 1

### 84.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 92
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/industryFortuneManage/contractManage/revenueContractDetail?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJDT05UUkFDVF9JRCI6IjE0MjE3MjMwIiwiSU5DX0VYUF9UWVBFIjoiQkFMQU5DRSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiUkVUUk9BQ1RJVkUiOiJOIiwiUFJPSkVDVF9DT05UUkFDVF9UWVBFIjoiMzAwMCIsInBhdGgiOiJmdWxsVmlldyJ9
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "CONTRACT_ID": "14217230"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `CONTRACT_ID` | saleCenterApp/contractManage/qryContractByProject 接口返回 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 84.3 响应结果
**状态码**: 200 
**响应大小**: 5471 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:49:20 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "***************",
      "REQUEST_ID": "20250714184920432_1_1086",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": {
        "EXPENSE_ID": "14218225",
        "CONTRACT_ID": "14217230",
        "PROJECT_ID": "CMGDZSICT20230614042",
        "PLAN_TOTAL_INCOME": "2810525.00",
        "IT_PLAN_TOTAL_INCOME": "2799437.00",
        "CT_PLAN_TOTAL_INCOME": "11088.00",
        "CREATE_STAFF": "1000030682",
        "CREATE_NAME": "曾小敏",
        "CREATE_DATE": "2023-06-20 15:43:02",
        "UPDATE_STAFF": "zengxiaomin3",
        "UPDATE_DATE": "2025-01-23 14:48:01",
        "PLAN_TOTAL_VAT": "232061.70",
        "TOTAL_MONEY_EX_TAX": "2578463.30",
        "PROJECT_STAGE": "1001",
        "PLAN_COMMU_OPERATOR_INCOME": "2810525.00",
        "EXPENSE_PLAN_STATUS": "1003",
        "EXPENSE_INFO_LIST": [
          {
            "PROD_ID": "9102",
            "PROD_NAME": "电路租用",
            "SUBJECT_CODE": "13598",
            "SUBJECT_NAME": "数字电路月租费",
            "SUBJECT_CLASS": "2000",
            "VAT_RATE": "0.09",
            "MONEY": "11088.00",
            "PROD_COUNT": "1",
            "DISCOUNT_RATE": "1.00",
            "CYCLE_TYPE": "20",
            "CYCLE_COUNT": "12",
            "PLAN_BILL_START_TIME": "2023-07-01 00:00:00",
            "COLL_CYCLE_TYPE": "20",
            "COLL_CYCLE_COUNT": "12",
            "PLAN_COLL_START_TIME": "2023-07-01 00:00:00",
            "VAT": "915.52",
            "MONEY_EX_TAX": "10172.48",
            "EXPENSE_DETAIL_ID": "14218229",
            "INCOME_CONFIRM_STATUS": "1000",
            "CT_OR_IT": "2000",
            "IS_SPEC_MARKET": "1001",
            "STATUS_CD": "1000",
            "PROD_LABLES": "1001",
            "PROD_LABLES_DESC": "专线",
            "GK_P
... (响应内容过长，已截断)
```

---

## 85. saleCenterApp/contractChange/queryExpenseChangeInfo
**中文名称**: 查询费用变更信息

### 85.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/contractChange/queryExpenseChangeInfo`
**方法**: POST
**调用次数**: 1

### 85.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 123
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/industryFortuneManage/contractManage/revenueContractDetail?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJDT05UUkFDVF9JRCI6IjE0MjE3MjMwIiwiSU5DX0VYUF9UWVBFIjoiQkFMQU5DRSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiUkVUUk9BQ1RJVkUiOiJOIiwiUFJPSkVDVF9DT05UUkFDVF9UWVBFIjoiMzAwMCIsInBhdGgiOiJmdWxsVmlldyJ9
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "CONTRACT_ID": "14217230",
      "EXPENSE_CHANGE_STATUS": "1001"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `CONTRACT_ID` | saleCenterApp/contractManage/qryContractByProject 接口返回 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 85.3 响应结果
**状态码**: 200 
**响应大小**: 267 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:49:20 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "*************",
      "REQUEST_ID": "20250714184920437_1_1354",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "PROMPT_MSG": ""
    }
  }
}
```

---

## 86. saleCenterApp/contractChange/queryExpenseChangeInfoList
**中文名称**: 查询费用变更信息列表

### 86.1 接口地址
**URL**: `https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/contractChange/queryExpenseChangeInfoList`
**方法**: POST
**调用次数**: 1

### 86.2 请求参数
**请求头 (Headers)**:
```
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cache-Control: no-cache
Connection: keep-alive
Content-Length: 92
Content-Type: application/json
Host: dict.gmcc.net:30722
Origin: https://dict.gmcc.net:30722
Pragma: no-cache
Referer: https://dict.gmcc.net:30722/dictWeb/gdydFlowPath/industryFortuneManage/contractManage/revenueContractDetail?info=eyJQUk9KRUNUX0lEIjoiQ01HRFpTSUNUMjAyMzA2MTQwNDIiLCJDT05UUkFDVF9JRCI6IjE0MjE3MjMwIiwiSU5DX0VYUF9UWVBFIjoiQkFMQU5DRSIsIlBST0pFQ1RfVFlQRSI6IjEwIiwiUkVUUk9BQ1RJVkUiOiJOIiwiUFJPSkVDVF9DT05UUkFDVF9UWVBFIjoiMzAwMCIsInBhdGgiOiJmdWxsVmlldyJ9
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
x-session-regionid: 999
x-session-staffid: 1000032328
x-session-staffname: dengyong
x-session-sysusercode: dengyong
```

**请求体 (Request Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "CONTRACT_ID": "14217230"
    }
  }
}
```

**参数来源分析**:
| 参数名称 | 来源说明 |
|---------|---------|
| `CONTRACT_ID` | saleCenterApp/contractManage/qryContractByProject 接口返回 |
| `LOGIN_NO/loginNo` | 用户登录Session信息 |

### 86.3 响应结果
**状态码**: 200 
**响应大小**: 33151 bytes
**响应头 (Response Headers)**:
```
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Connection: keep-alive
Content-Type: application/json;charset=UTF-8
Date: Mon, 14 Jul 2025 10:49:20 GMT
Expires: 0
Pragma: no-cache
Server: nginx
Transfer-Encoding: chunked
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

**响应体 (Response Body)**:
```json
{
  "ROOT": {
    "HEADER": {
      "DB_ID": "A",
      "x-session-sysusercode": "liaochulin",
      "OPR_INFO": {
        "LOGIN_NO": "liaochulin"
      }
    },
    "BODY": {
      "RUN_IP": "***************",
      "REQUEST_ID": "20250714184920558_1_1116",
      "RETURN_CODE": "0",
      "RETURN_MSG": "OK",
      "USER_MSG": "OK",
      "DETAIL_MSG": "OK",
      "OUT_DATA": [
        {
          "EXPENSE_CHANGE_ID": "139418141",
          "CONTRACT_ID": "14217230",
          "PROJECT_ID": "CMGDZSICT20230614042",
          "PLAN_TOTAL_INCOME": "2810525.00",
          "IT_PLAN_TOTAL_INCOME": "2799437.00",
          "CT_PLAN_TOTAL_INCOME": "11088.00",
          "CREATE_STAFF": "1000030682",
          "CREATE_NAME": "曾小敏",
          "CREATE_DATE": "2025-01-23 11:28:49",
          "UPDATE_STAFF": "zengxiaomin3",
          "UPDATE_DATE": "2025-01-23 14:48:01",
          "PLAN_TOTAL_VAT": "232061.70",
          "TOTAL_MONEY_EX_TAX": "2578463.30",
          "PROJECT_STAGE": "1001",
          "PLAN_COMMU_OPERATOR_INCOME": "2810525.00",
          "EXPENSE_CHANGE_STATUS": "1003",
          "EXPENSE_CHANGE_INFOS": [
            {
              "PROD_ID": "9102",
              "PROD_NAME": "电路租用",
              "SUBJECT_CODE": "13598",
              "SUBJECT_NAME": "数字电路月租费",
              "SUBJECT_CLASS": "2000",
              "VAT_RATE": "0.09",
              "MONEY": "11088.00",
              "PROD_COUNT": "1",
              "DISCOUNT_RATE": "1.00",
              "CYCLE_TYPE": "20",
              "CYCLE_COUNT": "12",
              "PLAN_BILL_START_TIME": "2023-07-01 00:00:00",
              "COLL_CYCLE_TYPE": "20",
              "COLL_CYCLE_COUNT": "12",
              "PLAN_COLL_START_TIME": "2023-07-01 00:00:00",
              "VAT": "915.52",
              "MONEY_EX_TAX": "10172.48",
              "EXPENSE_DETAIL_ID": "14218229",
              "CT_OR_IT": "2000",
              "CHANGE_DETAIL_ID": "*********",
              "OPER_TYPE": "M",
              "PROD_LABLES": "
... (响应内容过长，已截断)
```

---
