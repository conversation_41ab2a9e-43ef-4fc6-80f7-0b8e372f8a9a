# 接口调用时序分析报告

## 接口调用时间线

### 1. 10:42:12.225 - 10:42:12.226 - 并发接口调用 (2个)
- **10:42:12.225** `saleCenterApp/common/dataDictService/batchLoadCodeList` (POST) - 200 - 196.33ms
- **10:42:12.226** `iom-app-svc/iom/api/wo/getTodo` (POST) - 200 - 197.63ms

### 2. 10:42:13.243 - 10:42:17.163 - 并发接口调用 (64个)
- **10:42:13.243** `saleCenterApp/projectManage/queryProjectInfo` (POST) - 200 - 232.34ms
- **10:42:13.243** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 131.10ms
- **10:42:13.245** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 161.41ms
- **10:42:13.246** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 1447.79ms
- **10:42:13.247** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 1610.25ms
- **10:42:13.249** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 2774.05ms
- **10:42:13.250** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 574.69ms
- **10:42:13.252** `saleCenterApp/formulation/queryProjectDemand` (POST) - 200 - 637.15ms
- **10:42:13.258** `saleCenterApp/common/dataDictService/batchLoadCodeList` (POST) - 200 - 470.58ms
- **10:42:13.261** `iom-app-svc/iom/api/wo/qryWoListByProject` (POST) - 200 - 2771.00ms
- **10:42:13.262** `saleCenterApp/common/dataDictService/batchLoadCodeList` (POST) - 200 - 1755.70ms
- **10:42:13.263** `saleCenterApp/contractManage/qryContractByProject` (POST) - 200 - 622.20ms
- **10:42:13.265** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 759.70ms
- **10:42:13.267** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 1691.62ms
- **10:42:13.268** `saleCenterApp/projectImplement/queryProjectProdprcDict` (POST) - 200 - 1945.29ms
- **10:42:13.269** `saleCenterApp//preparation/queryProjectPlanWithImplement` (POST) - 200 - 909.85ms
- **10:42:13.270** `saleCenterApp/projectAmount/queryProjectAmount` (POST) - 200 - 906.57ms
- **10:42:13.272** `saleCenterApp//biddingSupport/saleBiddingInfo` (POST) - 200 - 1954.43ms
- **10:42:13.273** `saleCenterApp/formulation/queryProgram` (POST) - 200 - 2214.69ms
- **10:42:13.274** `saleCenterApp/common/dataDictService/batchLoadCodeList` (POST) - 200 - 905.34ms
- **10:42:13.276** `saleCenterApp/incomeManage/qryIncomeProgressByProject` (POST) - 200 - 2475.97ms
- **10:42:13.278** `saleCenterApp/projectImplement/queryProjectProdprcDict` (POST) - 200 - 1225.34ms
- **10:42:13.280** `saleCenterApp//preparation/queryProjectPlanWithImplement` (POST) - 200 - 1075.91ms
- **10:42:13.281** `esop-inter-svc/inter/hwEsop/ebus/queryUsedInvoices` (POST) - 200 - 1219.29ms
- **10:42:13.283** `saleCenterApp/incomeManage/qryIncomeProgressByProject` (POST) - 200 - 2469.81ms
- **10:42:13.284** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 2651.47ms
- **10:42:13.286** `saleCenterApp//projectBill/qryBackInfoByProject` (POST) - 200 - 2700.39ms
- **10:42:13.286** `saleCenterApp//projectBill/qryBillInfoByProject` (POST) - 200 - 1255.70ms
- **10:42:13.287** `saleCenterApp//projectBill/queryCurMonthRealTimeIncome` (POST) - 200 - 2610.94ms
- **10:42:13.289** `saleCenterApp//projectBill/qryCheckdecrDateInfoByProject` (POST) - 200 - 2628.20ms
- **10:42:13.297** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 1402.95ms
- **10:42:13.908** `saleCenterApp/common/dataDictService/batchLoadCodeList` (POST) - 200 - 1178.10ms
- **10:42:13.909** `saleCenterApp/projectManage/queryProjectInfo` (POST) - 200 - 1179.02ms
- **10:42:13.910** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 1182.31ms
- **10:42:13.922** `esop-inter-svc/inter/hwOpp/ebus/queryOppInfoForDict` (POST) - 200 - 1437.15ms
- **10:42:13.924** `saleCenterApp/projectManage/queryProjectInfo` (POST) - 200 - 1293.69ms
- **10:42:13.924** `saleCenterApp/common/dataDictService/batchLoadCodeList` (POST) - 200 - 1535.56ms
- **10:42:13.925** `saleCenterApp/formulation/queryProgram` (POST) - 200 - 1416.35ms
- **10:42:13.927** `saleCenterApp/projectManage/queryProjectInfo` (POST) - 200 - 1762.21ms
- **10:42:13.929** `saleCenterApp/formulation/queryProjectDemand` (POST) - 200 - 1860.47ms
- **10:42:14.076** `saleCenterApp/incomeManage/qryIncomePlanListByProject` (POST) - 200 - 1464.21ms
- **10:42:14.282** `saleCenterApp/projectAmount/queryAdvanceAmount` (POST) - 200 - 1667.00ms
- **10:42:15.048** `saleCenterApp/formulation/queryProjectDemand` (POST) - 200 - 1766.05ms
- **10:42:15.149** `saleCenterApp/projectManage/queryProjectInfo` (POST) - 200 - 1100.43ms
- **10:42:15.158** `saleCenterApp/formulation/queryProjectDemand` (POST) - 200 - 1462.87ms
- **10:42:15.162** `esop-inter-svc/inter/hwCust/ebus/custQueryCustByCustId` (POST) - 200 - 1272.44ms
- **10:42:15.370** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 935.35ms
- **10:42:15.868** `saleCenterApp/projectLabel/queryProjectLabelConf` (POST) - 200 - 549.21ms
- **10:42:15.967** `saleCenterApp/common/dataDictService/batchLoadCodeList` (POST) - 200 - 471.02ms
- **10:42:15.970** `saleCenterApp/formulation/queryProgram` (POST) - 200 - 650.94ms
- **10:42:15.972** `saleCenterApp/projectManage/queryProjectInfo` (POST) - 200 - 843.66ms
- **10:42:15.979** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 758.05ms
- **10:42:15.981** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 891.92ms
- **10:42:15.983** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 892.52ms
- **10:42:16.000** `saleCenterApp/projectManage/queryMyProjectList` (POST) - 200 - 1284.75ms
- **10:42:16.539** `saleCenterApp/investmentReview/queryFlowStep` (POST) - 200 - 750.54ms
- **10:42:16.544** `saleCenterApp/projectProportion/queryGroupOrg` (POST) - 200 - 427.14ms
- **10:42:16.998** `saleCenterApp/projectManage/queryProjectInfo` (POST) - 200 - 865.10ms
- **10:42:17.004** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 680.33ms
- **10:42:17.005** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 354.57ms
- **10:42:17.026** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 645.30ms
- **10:42:17.034** `saleCenterApp/projectManage/queryMyProjectList` (POST) - 200 - 829.31ms
- **10:42:17.038** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 823.10ms
- **10:42:17.163** `saleCenterApp/projectLabel/queryProjectLabelConf` (POST) - 200 - 699.95ms

### 3. 10:42:18.211 - 10:42:20.880 - 并发接口调用 (18个)
- **10:42:18.211** `iom-app-svc/iom/api/wo/qryWoListByProject` (POST) - 200 - 740.41ms
- **10:42:18.212** `saleCenterApp/deliveryAssessment/queryAllTaskByProjectId` (POST) - 200 - 652.81ms
- **10:42:18.216** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 651.20ms
- **10:42:18.217** `saleCenterApp/projectManage/queryProjectInfo` (POST) - 200 - 705.58ms
- **10:42:18.218** `saleCenterApp/projectImplement/queryProjectJointTest` (POST) - 200 - 617.89ms
- **10:42:18.220** `saleCenterApp/preparation/queryProjectMilestone` (POST) - 200 - 817.34ms
- **10:42:18.221** `saleCenterApp/projectFinalTest/acceptanceCompletedQuery` (POST) - 200 - 831.96ms
- **10:42:18.223** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 829.39ms
- **10:42:18.872** `saleCenterApp/deliveryAssessment/satisfactionAssessmentInit` (POST) - 200 - 255.17ms
- **10:42:18.979** `iom-app-svc/iom/api/wo/qryWoListByProject` (POST) - 200 - 199.88ms
- **10:42:19.322** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 527.09ms
- **10:42:19.323** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 179.76ms
- **10:42:19.324** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 579.96ms
- **10:42:19.325** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 594.61ms
- **10:42:19.335** `saleCenterApp/deliveryAssessment/satisfactionAssessmentQuery` (POST) - 200 - 569.00ms
- **10:42:20.174** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 128.73ms
- **10:42:20.175** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 125.89ms
- **10:42:20.880** `bss-base-query/base/qryBulletinList` (GET) - 200 - 127.35ms

### 4. 10:42:31.465 - 10:42:31.466 - 并发接口调用 (2个)
- **10:42:31.465** `saleCenterApp/arrearage/queryArrearsDetail` (POST) - 200 - 254.23ms
- **10:42:31.466** `saleCenterApp/arrearage/queryArrearsList` (POST) - 200 - 251.67ms

### 5. 10:42:51.731 - 10:42:51.733 - 并发接口调用 (4个)
- **10:42:51.731** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 121.10ms
- **10:42:51.731** `saleCenterApp/formulation/queryMemberChangeList` (POST) - 200 - 392.33ms
- **10:42:51.732** `saleCenterApp/formulation/queryTeamMember` (POST) - 200 - 134.60ms
- **10:42:51.733** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 154.59ms

### 6. 10:42:55.911 - 单个接口调用
**接口**: `saleCenterApp/formulation/queryMemberChangeList`
**方法**: POST
**状态**: 200
**耗时**: 304.20ms

### 7. 10:43:00.282 - 单个接口调用
**接口**: `saleCenterApp/preparation/queryProjectMilestone`
**方法**: POST
**状态**: 200
**耗时**: 115.04ms

### 8. 10:43:06.118 - 10:43:06.118 - 并发接口调用 (2个)
- **10:43:06.118** `esop-inter-svc/inter/common/callGet/querySelectInfoByProjectId` (GET) - 200 - 669.12ms
- **10:43:06.118** `esop-inter-svc/inter/common/callGet/querySelectInfoByProjectId` (GET) - 200 - 782.36ms

### 9. 10:43:31.677 - 单个接口调用
**接口**: `saleCenterApp//preparation/queryProjectPlanWithImplement`
**方法**: POST
**状态**: 200
**耗时**: 158.69ms

### 10. 10:43:58.601 - 10:43:59.400 - 并发接口调用 (6个)
- **10:43:58.601** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 153.18ms
- **10:43:58.602** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 153.33ms
- **10:43:58.603** `saleCenterApp/projectManage/queryProjectInfo` (POST) - 200 - 296.52ms
- **10:43:58.605** `saleCenterApp/excle/queryDocListByProject` (POST) - 200 - 275.73ms
- **10:43:58.605** `esop-inter-svc/inter/common/callPost/querySelectFileByProjectCode` (POST) - 200 - 1163.85ms
- **10:43:59.400** `saleCenterApp/fileManage/queryMustUploadTree` (POST) - 200 - 186.31ms

### 11. 10:44:26.321 - 单个接口调用
**接口**: `saleCenterApp/excle/queryDocListByProject`
**方法**: POST
**状态**: 200
**耗时**: 132.51ms

### 12. 10:44:32.659 - 10:44:32.660 - 并发接口调用 (3个)
- **10:44:32.659** `saleCenterApp/projectOrder/getProjectOrderList` (POST) - 200 - 134.39ms
- **10:44:32.659** `saleCenterApp/projectOrder/queryProjectOrderInstance` (POST) - 200 - 137.27ms
- **10:44:32.660** `saleCenterApp//common/staff/getSystemUserBossList` (POST) - 200 - 133.95ms

### 13. 10:44:50.649 - 10:44:50.654 - 并发接口调用 (2个)
- **10:44:50.649** `saleCenterApp/projectImplement/queryProjectChangeOutList` (POST) - 200 - 350.99ms
- **10:44:50.654** `saleCenterApp/projectImplement/queryProjectChangeOutList` (POST) - 200 - 355.73ms

### 14. 10:44:55.926 - 10:44:55.927 - 并发接口调用 (2个)
- **10:44:55.926** `saleCenterApp/projectWeekly/showProjectWeeklyInformationList` (POST) - 200 - 123.74ms
- **10:44:55.927** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 124.21ms

### 15. 10:44:59.574 - 单个接口调用
**接口**: `saleCenterApp/projectWeekly/customerVisitList`
**方法**: POST
**状态**: 200
**耗时**: 136.38ms

### 16. 10:45:02.192 - 单个接口调用
**接口**: `saleCenterApp/projectSupport/querySupportInfoListByCust`
**方法**: POST
**状态**: 200
**耗时**: 114.10ms

### 17. 10:45:04.752 - 10:45:04.763 - 并发接口调用 (6个)
- **10:45:04.752** `saleCenterApp/projectAudit/queryProjAuditInfo` (POST) - 200 - 134.89ms
- **10:45:04.752** `saleCenterApp/projectAudit/queryProjAuditInfo` (POST) - 200 - 135.08ms
- **10:45:04.753** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 253.84ms
- **10:45:04.753** `saleCenterApp/projectManage/queryProjectInfo` (POST) - 200 - 315.52ms
- **10:45:04.762** `saleCenterApp/projectAudit/queryProjAuditInfo` (POST) - 200 - 264.34ms
- **10:45:04.763** `saleCenterApp/projectManage/queryProjectInfo` (POST) - 200 - 387.60ms

### 18. 10:45:24.358 - 10:45:24.466 - 并发接口调用 (2个)
- **10:45:24.358** `saleCenterApp/projectManage/queryProjectInfo` (POST) - 200 - 100.76ms
- **10:45:24.466** `saleCenterApp/investmentReview/queryFlowStep` (POST) - 200 - 122.18ms

### 19. 10:46:07.441 - 10:46:11.746 - 并发接口调用 (53个)
- **10:46:07.441** `iom-app-svc/iom/api/wo/qryWoListByProject` (POST) - 200 - 135.07ms
- **10:46:07.441** `saleCenterApp/formulation/queryProgram` (POST) - 200 - 198.44ms
- **10:46:07.442** `saleCenterApp/formulation/queryProjectDemand` (POST) - 200 - 615.83ms
- **10:46:07.443** `saleCenterApp/projectIncExp/isHistoryData` (POST) - 200 - 227.38ms
- **10:46:07.444** `saleCenterApp/onlinePpt/isOnlinePPTOpen` (POST) - 200 - 310.23ms
- **10:46:07.446** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 447.03ms
- **10:46:07.810** `saleCenterApp/abilityGradeManage/queryAbilityGradeInfo` (POST) - 200 - 247.57ms
- **10:46:07.819** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 239.72ms
- **10:46:07.819** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 240.21ms
- **10:46:07.820** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 253.41ms
- **10:46:07.820** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 433.79ms
- **10:46:07.821** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 432.84ms
- **10:46:08.449** `esop-inter-svc/inter/hwCust/ebus/custQueryCustByCustId` (POST) - 200 - 282.25ms
- **10:46:08.450** `saleCenterApp/projectManage/queryProjectInfo` (POST) - 200 - 141.17ms
- **10:46:08.451** `saleCenterApp/common/dataDictService/batchLoadCodeList` (POST) - 200 - 133.85ms
- **10:46:08.452** `saleCenterApp/formulation/judgeProjectCustIsLegionImportant` (POST) - 200 - 176.19ms
- **10:46:08.453** `saleCenterApp/projectGradeEvaSvc/queryLegionInfo` (POST) - 200 - 277.16ms
- **10:46:08.470** `saleCenterApp/RevokeContractService/queryContractInfoList` (POST) - 200 - 261.51ms
- **10:46:08.472** `saleCenterApp/common/dataDictService/batchLoadCodeList` (POST) - 200 - 259.52ms
- **10:46:08.473** `saleCenterApp/RevokeContractService/queryContractInfoList` (POST) - 200 - 2397.26ms
- **10:46:08.474** `saleCenterApp/RevokeContractService/queryContractInfoList` (POST) - 200 - 316.23ms
- **10:46:08.476** `saleCenterApp/projectManage/queryProjectInfo` (POST) - 200 - 1125.59ms
- **10:46:08.477** `saleCenterApp/RevokeContractService/queryContractInfoList` (POST) - 200 - 413.63ms
- **10:46:08.478** `saleCenterApp/common/dataDictService/batchLoadCodeList` (POST) - 200 - 609.29ms
- **10:46:08.480** `saleCenterApp/formulation/queryProjectDemand` (POST) - 200 - 1129.89ms
- **10:46:08.481** `saleCenterApp/common/dataDictService/batchLoadCodeList` (POST) - 200 - 1392.89ms
- **10:46:08.482** `saleCenterApp/RevokeContractService/queryContractInfoList` (POST) - 200 - 700.74ms
- **10:46:08.484** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 613.59ms
- **10:46:08.486** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 939.58ms
- **10:46:08.487** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 788.83ms
- **10:46:08.489** `saleCenterApp/projectManage/queryMyProjectList` (POST) - 200 - 989.32ms
- **10:46:08.490** `saleCenterApp/common/dataDictService/batchLoadCodeList` (POST) - 200 - 1126.94ms
- **10:46:08.867** `saleCenterApp/projectManage/queryProjectFlow` (POST) - 200 - 1004.85ms
- **10:46:08.871** `saleCenterApp/common/dataDictService/batchLoadCodeList` (POST) - 200 - 1850.11ms
- **10:46:08.872** `saleCenterApp/projectLabel/queryProjectLabelConf` (POST) - 200 - 1016.94ms
- **10:46:08.873** `saleCenterApp/projectIncExp/getProjectIncExpSwitch` (POST) - 200 - 1649.26ms
- **10:46:08.874** `esop-inter-svc/inter/hwOpp/ebus/queryOppInfoForDict` (POST) - 200 - 1747.06ms
- **10:46:09.026** `saleCenterApp/fuseAbilitySvc/containVisualNetwork` (POST) - 200 - 1611.85ms
- **10:46:09.041** `saleCenterApp/SafetyAbilityManageService/queryIsSafetyProd` (POST) - 200 - 1680.65ms
- **10:46:09.050** `saleCenterApp/formulation/qryHsolution` (POST) - 200 - 1688.58ms
- **10:46:09.195** `saleCenterApp/formulation/queryProjectBenefit` (POST) - 200 - 1603.95ms
- **10:46:09.606** `esop-inter-svc/inter/hwOpp/ebus/queryAllOppListForDict` (POST) - 200 - 1328.78ms
- **10:46:09.914** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 956.82ms
- **10:46:10.626** `saleCenterApp/formulation/queryProjectDemand` (POST) - 200 - 617.42ms
- **10:46:10.640** `saleCenterApp/formulation/queryProjectDemand` (POST) - 200 - 686.06ms
- **10:46:10.872** `esop-inter-svc/inter/common/callGet/querySelectInfoByProjectId` (GET) - 200 - 1115.96ms
- **10:46:10.929** `saleCenterApp/formulation/queryProjectDemand` (POST) - 200 - 476.67ms
- **10:46:10.935** `saleCenterApp/fuseAbilitySvc/containInternetOfThingsMoney` (POST) - 200 - 197.88ms
- **10:46:10.940** `saleCenterApp/formulation/queryProjectDemand` (POST) - 200 - 782.61ms
- **10:46:10.943** `iom-app-svc/iom/api/extflowceter/queryOrderTrack` (POST) - 200 - 6985.44ms
- **10:46:11.003** `saleCenterApp/formulation/queryProjectDemand` (POST) - 200 - 736.51ms
- **10:46:11.136** `saleCenterApp/formulation/queryProjectDemand` (POST) - 200 - 699.30ms
- **10:46:11.746** `saleCenterApp/fuseAbilitySvc/queryYdyTrade` (POST) - 200 - 209.34ms

### 20. 10:46:36.634 - 10:46:37.350 - 并发接口调用 (20个)
- **10:46:36.634** `iom-app-svc/iom/api/wo/qryWoListByProject` (POST) - 200 - 339.91ms
- **10:46:36.635** `saleCenterApp/common/dataDictService/batchLoadCodeList` (POST) - 200 - 269.60ms
- **10:46:36.636** `saleCenterApp/projectManage/queryProjectFlow` (POST) - 200 - 265.34ms
- **10:46:36.637** `saleCenterApp/projectManage/queryProjectFlow` (POST) - 200 - 269.86ms
- **10:46:36.638** `saleCenterApp/formulation/queryProgram` (POST) - 200 - 360.47ms
- **10:46:36.638** `saleCenterApp/projectManage/queryProjectInfo` (POST) - 200 - 358.31ms
- **10:46:36.639** `saleCenterApp/projectDecision/isHistoryData` (POST) - 200 - 415.28ms
- **10:46:36.639** `saleCenterApp/projectDecision/cancelOASwitch` (POST) - 200 - 574.24ms
- **10:46:36.641** `saleCenterApp/formulation/queryProjectBenefit` (POST) - 200 - 416.41ms
- **10:46:36.641** `saleCenterApp/projectReview/queryProjectEstimatedExp` (POST) - 200 - 505.61ms
- **10:46:36.642** `saleCenterApp/projectDecision/queryProjectDecisionInfo` (POST) - 200 - 1005.77ms
- **10:46:36.644** `saleCenterApp/onlinePpt/isOnlinePPTOpen` (POST) - 200 - 494.93ms
- **10:46:36.645** `saleCenterApp/projectReview/queryProjectIncDetailList` (POST) - 200 - 609.21ms
- **10:46:36.646** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 764.58ms
- **10:46:36.647** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 617.73ms
- **10:46:36.648** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 610.96ms
- **10:46:37.011** `saleCenterApp/provincialCompanyProjectDecision/decisionResult` (POST) - 200 - 377.27ms
- **10:46:37.252** `esop-inter-svc/inter/common/callGet/querySelectInfoByProjectId` (GET) - 200 - 904.07ms
- **10:46:37.349** `saleCenterApp/projectIncExp/isHistoryData` (POST) - 200 - 124.06ms
- **10:46:37.350** `saleCenterApp/projectIncExp/getProjectIncExpSwitch` (POST) - 200 - 192.34ms

### 21. 10:46:57.074 - 10:46:58.023 - 并发接口调用 (10个)
- **10:46:57.074** `saleCenterApp/formulation/queryProjectDemand` (POST) - 200 - 699.63ms
- **10:46:57.075** `iom-app-svc/iom/api/wo/qryWoListByProject` (POST) - 200 - 131.15ms
- **10:46:57.076** `iom-app-svc/iom/api/wo/qryWoListByProject` (POST) - 200 - 121.10ms
- **10:46:57.076** `iom-app-svc/iom/api/wo/qryWoListByProject` (POST) - 200 - 115.01ms
- **10:46:57.078** `saleCenterApp/contractManage/qryContractByProject` (POST) - 200 - 362.86ms
- **10:46:57.080** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 357.57ms
- **10:46:57.081** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 361.79ms
- **10:46:57.081** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 692.26ms
- **10:46:57.778** `saleCenterApp/formulation/queryProjectBenefit` (POST) - 200 - 239.50ms
- **10:46:58.023** `saleCenterApp/projectDisclosure/queryTsolution` (POST) - 200 - 472.58ms

### 22. 10:47:18.482 - 10:47:21.574 - 并发接口调用 (39个)
- **10:47:18.482** `saleCenterApp/contractManage/qryContractByProject` (POST) - 200 - 97.63ms
- **10:47:18.482** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 92.25ms
- **10:47:18.486** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 91.13ms
- **10:47:19.110** `saleCenterApp/projectManage/queryProjectInfo` (POST) - 200 - 446.19ms
- **10:47:19.111** `saleCenterApp/contractManage/qryContract` (POST) - 200 - 482.47ms
- **10:47:19.112** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 484.26ms
- **10:47:19.113** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 563.36ms
- **10:47:19.114** `saleCenterApp/projectDisclosure/queryProjectDisclosure` (POST) - 200 - 579.11ms
- **10:47:19.115** `saleCenterApp/formulation/queryProjectDemand` (POST) - 200 - 854.69ms
- **10:47:19.116** `iom-app-svc/iom/api/wo/qryWoListByProject` (POST) - 200 - 643.70ms
- **10:47:19.117** `saleCenterApp/contractManage/qryContract` (POST) - 200 - 675.30ms
- **10:47:19.118** `saleCenterApp/projectDisclosure/queryProjectDisclosure` (POST) - 200 - 609.21ms
- **10:47:19.873** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 217.02ms
- **10:47:19.874** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 210.58ms
- **10:47:19.876** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 245.20ms
- **10:47:19.877** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 217.78ms
- **10:47:19.878** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 233.78ms
- **10:47:19.879** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 263.74ms
- **10:47:19.881** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 524.38ms
- **10:47:19.881** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 1331.54ms
- **10:47:19.882** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 572.58ms
- **10:47:19.883** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 585.49ms
- **10:47:19.884** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 523.39ms
- **10:47:19.885** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 525.70ms
- **10:47:19.886** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 633.49ms
- **10:47:19.888** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 1102.41ms
- **10:47:19.890** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 663.32ms
- **10:47:19.891** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 744.32ms
- **10:47:19.892** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 985.05ms
- **10:47:19.893** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 1311.00ms
- **10:47:19.894** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 1297.30ms
- **10:47:19.895** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 1321.08ms
- **10:47:19.896** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 1318.84ms
- **10:47:19.897** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 1319.51ms
- **10:47:19.899** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 1401.97ms
- **10:47:19.901** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 1446.58ms
- **10:47:20.176** `saleCenterApp/formulation/queryProjectBenefit` (POST) - 200 - 1173.97ms
- **10:47:21.026** `bss-base-query/base/qryBulletinList` (GET) - 200 - 322.98ms
- **10:47:21.574** `saleCenterApp/projectDisclosure/queryTsolution` (POST) - 200 - 182.69ms

### 23. 10:47:38.771 - 10:47:38.904 - 并发接口调用 (2个)
- **10:47:38.771** `saleCenterApp/projectManage/queryProjectInfo` (POST) - 200 - 129.93ms
- **10:47:38.904** `saleCenterApp/investmentReview/queryFlowStep` (POST) - 200 - 219.28ms

### 24. 10:47:44.785 - 10:47:45.077 - 并发接口调用 (16个)
- **10:47:44.785** `saleCenterApp/projectManage/queryProjectInfo` (POST) - 200 - 136.74ms
- **10:47:44.786** `saleCenterApp/autoIntegration/queryAbilityMapByProject` (POST) - 200 - 121.07ms
- **10:47:44.787** `saleCenterApp/incomeManage/queryMobliePoliceInfo` (POST) - 200 - 194.75ms
- **10:47:44.788** `iom-app-svc/iom/api/wo/qryWoListByProject` (POST) - 200 - 249.03ms
- **10:47:44.791** `saleCenterApp//preparation/queryProjectStart` (POST) - 200 - 192.13ms
- **10:47:44.794** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 187.39ms
- **10:47:44.795** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 245.93ms
- **10:47:44.797** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 272.44ms
- **10:47:44.798** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 381.18ms
- **10:47:44.799** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 383.02ms
- **10:47:44.801** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 383.63ms
- **10:47:44.803** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 383.79ms
- **10:47:45.062** `saleCenterApp/preparation/queryProjectMilestone` (POST) - 200 - 179.69ms
- **10:47:45.064** `saleCenterApp/preparation/queryTaskDisassemble` (POST) - 200 - 179.58ms
- **10:47:45.066** `saleCenterApp/projectManage/queryProjectFlow` (POST) - 200 - 263.23ms
- **10:47:45.077** `iom-app-svc/iom/api/wo/qryWoListByProject` (POST) - 200 - 272.64ms

### 25. 10:48:14.858 - 10:48:15.136 - 并发接口调用 (20个)
- **10:48:14.858** `saleCenterApp/projectImplement/queryProjectJointTest` (POST) - 200 - 89.87ms
- **10:48:14.859** `saleCenterApp/projectFinalTest/acceptanceCompletedQuery` (POST) - 200 - 138.77ms
- **10:48:14.860** `saleCenterApp/preparation/queryProjectMilestone` (POST) - 200 - 121.79ms
- **10:48:14.861** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 125.52ms
- **10:48:14.863** `saleCenterApp//preparation/queryTotalProcess` (POST) - 200 - 123.29ms
- **10:48:14.865** `saleCenterApp//preparation/queryProjectPlanWithImplement` (POST) - 200 - 245.03ms
- **10:48:14.867** `iom-app-svc/iom/api/wo/qryWoListByProject` (POST) - 200 - 234.20ms
- **10:48:14.869** `iom-app-svc/iom/api/wo/qryWoListByProject` (POST) - 200 - 217.41ms
- **10:48:14.870** `iom-app-svc/iom/api/wo/qryWoListByProject` (POST) - 200 - 515.80ms
- **10:48:14.872** `iom-app-svc/iom/api/wo/qryWoListByProject` (POST) - 200 - 299.36ms
- **10:48:14.874** `iom-app-svc/iom/api/wo/qryWoListByProject` (POST) - 200 - 314.35ms
- **10:48:14.875** `iom-app-svc/iom/api/wo/qryWoListByProject` (POST) - 200 - 545.39ms
- **10:48:14.876** `saleCenterApp/preparation/queryPlanAndSubjectTable` (POST) - 200 - 2155.52ms
- **10:48:14.878** `saleCenterApp//preparation/queryTotalProcess` (POST) - 200 - 544.33ms
- **10:48:14.879** `saleCenterApp/projectSupport/querySupportInfoListByCust` (POST) - 200 - 1027.81ms
- **10:48:14.882** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 542.33ms
- **10:48:15.123** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 669.74ms
- **10:48:15.125** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 425.40ms
- **10:48:15.135** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 418.56ms
- **10:48:15.136** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 414.35ms

### 26. 10:49:20.404 - 10:49:22.053 - 并发接口调用 (24个)
- **10:49:20.404** `saleCenterApp/common/dataDictService/batchLoadCodeList` (POST) - 200 - 117.99ms
- **10:49:20.407** `iom-app-svc/iom/api/wo/getTodo` (POST) - 200 - 151.50ms
- **10:49:20.410** `saleCenterApp/projectAudit/queryProjAuditInfo` (POST) - 200 - 137.39ms
- **10:49:21.038** `saleCenterApp/common/dataDictService/batchLoadCodeList` (POST) - 200 - 334.16ms
- **10:49:21.039** `saleCenterApp/contractManage/queryContractInfo` (POST) - 200 - 314.34ms
- **10:49:21.039** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 150.46ms
- **10:49:21.040** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 254.08ms
- **10:49:21.040** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 336.34ms
- **10:49:21.041** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 343.30ms
- **10:49:21.042** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 665.53ms
- **10:49:21.043** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 383.04ms
- **10:49:21.043** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 650.29ms
- **10:49:21.045** `saleCenterApp/common/dataDictService/loadCodeList` (POST) - 200 - 659.91ms
- **10:49:21.047** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 644.20ms
- **10:49:21.457** `saleCenterApp/projectManage/queryProjectInfo` (POST) - 200 - 428.09ms
- **10:49:21.459** `saleCenterApp/contractManage/queryAgreementContract` (POST) - 200 - 389.83ms
- **10:49:21.474** `saleFileApp/common/fileService/queryBusiDocListByDocType` (POST) - 200 - 386.48ms
- **10:49:21.565** `saleCenterApp/incomeManage/queryExpensePlanInfo` (POST) - 200 - 293.92ms
- **10:49:21.567** `saleCenterApp/contractChange/queryExpenseChangeInfo` (POST) - 200 - 293.08ms
- **10:49:21.568** `saleCenterApp/contractChange/queryExpenseChangeInfoList` (POST) - 200 - 1022.88ms
- **10:49:21.888** `saleCenterApp//biddingSupport/saleBiddingInfo` (POST) - 200 - 572.34ms
- **10:49:21.888** `saleCenterApp/formulation/queryProgram` (POST) - 200 - 573.23ms
- **10:49:22.049** `saleCenterApp/projectManage/queryProjectFlow` (POST) - 200 - 505.65ms
- **10:49:22.053** `saleCenterApp/common/dataDictService/batchLoadCodeList` (POST) - 200 - 538.41ms

### 27. 10:50:26.123 - 10:50:26.926 - 并发接口调用 (10个)
- **10:50:26.123** `saleCenterApp/common/dataDictService/batchLoadCodeList` (POST) - 200 - 131.08ms
- **10:50:26.123** `iom-app-svc/iom/api/wo/getTodo` (POST) - 200 - 200.08ms
- **10:50:26.126** `saleCenterApp/projectAudit/queryProjAuditInfo` (POST) - 200 - 127.90ms
- **10:50:26.658** `saleCenterApp/projectAudit/queryProjAuditInfo` (POST) - 200 - 133.71ms
- **10:50:26.658** `saleCenterApp/projectManage/queryProjectInfo` (POST) - 200 - 157.81ms
- **10:50:26.659** `saleCenterApp/common/dataDictService/batchLoadCodeList` (POST) - 200 - 155.97ms
- **10:50:26.660** `saleCenterApp/incomeManage/qryIncomeProgressByProject` (POST) - 200 - 177.74ms
- **10:50:26.660** `saleCenterApp/projectImplement/queryProjectProdprcDict` (POST) - 200 - 258.34ms
- **10:50:26.663** `saleCenterApp//preparation/queryProjectPlanWithImplement` (POST) - 200 - 314.81ms
- **10:50:26.926** `saleCenterApp/projectAmount/queryAdvanceAmount` (POST) - 200 - 217.25ms

### 28. 10:52:22.156 - 单个接口调用
**接口**: `bss-base-query/base/qryBulletinList`
**方法**: GET
**状态**: 200
**耗时**: 390.87ms

## 调用模式分析

### 接口调用频率统计
| 接口名称 | 调用次数 | 占比 |
|---------|---------|------|
| `saleFileApp/common/fileService/queryBusiDocListByDocType` | 56 | 17.8% |
| `saleCenterApp/common/dataDictService/loadCodeList` | 44 | 14.0% |
| `saleCenterApp/projectManage/queryProjectInfo` | 20 | 6.4% |
| `saleCenterApp/common/dataDictService/batchLoadCodeList` | 19 | 6.1% |
| `iom-app-svc/iom/api/wo/qryWoListByProject` | 17 | 5.4% |
| `saleCenterApp/formulation/queryProjectDemand` | 14 | 4.5% |
| `saleCenterApp/formulation/queryProgram` | 6 | 1.9% |
| `saleCenterApp/projectAudit/queryProjAuditInfo` | 6 | 1.9% |
| `saleCenterApp//preparation/queryProjectPlanWithImplement` | 5 | 1.6% |
| `saleCenterApp/RevokeContractService/queryContractInfoList` | 5 | 1.6% |
| `saleCenterApp/projectManage/queryProjectFlow` | 5 | 1.6% |
| `saleCenterApp/preparation/queryProjectMilestone` | 4 | 1.3% |
| `esop-inter-svc/inter/common/callGet/querySelectInfoByProjectId` | 4 | 1.3% |
| `saleCenterApp/formulation/queryProjectBenefit` | 4 | 1.3% |
| `iom-app-svc/iom/api/wo/getTodo` | 3 | 1.0% |
| `saleCenterApp/contractManage/qryContractByProject` | 3 | 1.0% |
| `saleCenterApp/projectImplement/queryProjectProdprcDict` | 3 | 1.0% |
| `saleCenterApp/incomeManage/qryIncomeProgressByProject` | 3 | 1.0% |
| `saleCenterApp/projectLabel/queryProjectLabelConf` | 3 | 1.0% |
| `saleCenterApp/projectManage/queryMyProjectList` | 3 | 1.0% |

### 典型调用链分析

**常见的三步调用序列**:
- `saleFileApp/common/fileService/queryBusiDocListByDocType -> saleFileApp/common/fileService/queryBusiDocListByDocType -> saleFileApp/common/fileService/queryBusiDocListByDocType` (出现36次)
- `saleCenterApp/common/dataDictService/loadCodeList -> saleCenterApp/common/dataDictService/loadCodeList -> saleCenterApp/common/dataDictService/loadCodeList` (出现14次)
- `iom-app-svc/iom/api/wo/qryWoListByProject -> iom-app-svc/iom/api/wo/qryWoListByProject -> iom-app-svc/iom/api/wo/qryWoListByProject` (出现5次)
- `saleCenterApp/projectManage/queryProjectInfo -> saleCenterApp/common/dataDictService/loadCodeList -> saleCenterApp/common/dataDictService/loadCodeList` (出现3次)
- `saleCenterApp/contractManage/qryContractByProject -> saleCenterApp/common/dataDictService/loadCodeList -> saleCenterApp/common/dataDictService/loadCodeList` (出现3次)
- `saleCenterApp/common/dataDictService/loadCodeList -> saleCenterApp/common/dataDictService/loadCodeList -> saleCenterApp/projectManage/queryMyProjectList` (出现3次)
- `saleCenterApp/common/dataDictService/batchLoadCodeList -> saleCenterApp/incomeManage/qryIncomeProgressByProject -> saleCenterApp/projectImplement/queryProjectProdprcDict` (出现2次)
- `saleCenterApp/incomeManage/qryIncomeProgressByProject -> saleCenterApp/projectImplement/queryProjectProdprcDict -> saleCenterApp//preparation/queryProjectPlanWithImplement` (出现2次)
- `saleCenterApp/common/dataDictService/batchLoadCodeList -> saleCenterApp/formulation/queryProgram -> saleCenterApp/projectManage/queryProjectInfo` (出现2次)
- `saleCenterApp/common/dataDictService/loadCodeList -> saleCenterApp/common/dataDictService/loadCodeList -> saleCenterApp/projectManage/queryProjectInfo` (出现2次)

### 页面加载阶段分析

**初始化阶段**: 1 个接口
**数据加载阶段**: 289 个接口
**用户交互阶段**: 24 个接口