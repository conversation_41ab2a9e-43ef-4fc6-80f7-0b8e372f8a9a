# 宽表文件自动归档功能说明

## 📋 功能概述

已成功为 `DICT_download_local_kuanbiao_from_db2email.py` 添加了自动文件归档功能。现在该脚本在完成邮件发送后，会自动将生成的"宽表***.xlsx"文件移动到"已处理"文件夹中。

## ✅ 修改内容

### 1. 新增导入模块
```python
# 从配置文件导入所有配置
from config import get_db_config, EMAIL_CONFIG, get_email_recipients_string, FILE_CONFIG
# 导入文件管理器
from file_manager import FileManager
```

### 2. 新增文件移动函数
```python
def move_kuanbiao_file_to_processed(filename):
    """将宽表文件移动到已处理文件夹"""
    try:
        # 初始化文件管理器
        file_manager = FileManager()
        
        # 移动文件到已处理文件夹
        success, new_path = file_manager.move_file_to_processed(filename, "宽表文件")
        
        if success:
            print(f"[成功] 宽表文件已移动到: {new_path}")
            return True
        else:
            print(f"[警告] 宽表文件移动失败: {filename}")
            return False
            
    except Exception as e:
        print(f"[错误] 移动宽表文件时出错: {e}")
        import traceback
        print(traceback.format_exc())
        return False
```

### 3. 修改主流程逻辑
在 `main()` 函数中，邮件发送成功后自动调用文件移动功能：

```python
if success:
    print("[完成] 邮件发送成功，正在移动文件...")
    # 邮件发送成功后，移动宽表文件到已处理文件夹
    move_success = move_kuanbiao_file_to_processed(attachment_path)
    
    if move_success:
        print("[完成] 任务完成：宽表数据已下载、发送并归档")
    else:
        print("[警告] 任务基本完成：宽表数据已下载并发送，但文件归档失败")
else:
    print("[失败] 任务失败：邮件发送失败")
    print("[信息] 宽表文件保留在当前目录，可手动处理")
```

## 🔄 工作流程

1. **数据下载**: 从数据库下载宽表数据，生成 `宽表YYYYMMDD.xlsx` 文件
2. **邮件发送**: 将宽表文件作为附件发送给指定收件人
3. **文件归档**: 邮件发送成功后，自动将宽表文件移动到"已处理"文件夹
4. **状态反馈**: 提供详细的执行状态信息

## 📁 文件组织结构

```
dict_romte/
├── DICT_download_local_kuanbiao_from_db2email.py  # 主程序
├── file_manager.py                                # 文件管理器
├── config.py                                      # 统一配置文件
├── 已处理/                                        # 归档文件夹
│   └── 宽表YYYYMMDD.xlsx                          # 已处理的宽表文件
└── 宽表YYYYMMDD.xlsx                              # 临时生成的宽表文件（发送后移动）
```

## 🎯 执行结果示例

### 成功执行的日志输出：
```
[开始] 开始从数据库下载宽表数据并发送邮件 - 2025-07-09 22:30:00
[信息] 正在连接数据库...
[信息] 正在查询宽表视图数据...
[成功] 宽表数据已保存为 宽表20250709.xlsx
[信息] 开始发送邮件 - 2025-07-09 22:30:15
[信息] 正在连接到SMTP服务器: smtp.126.com...
[信息] 正在发送邮件到: <EMAIL>,<EMAIL>...
[成功] 邮件已发送到: <EMAIL>,<EMAIL>
[完成] 邮件发送成功，正在移动文件...
[成功] 宽表文件已移动到: .\已处理\宽表20250709.xlsx
[完成] 任务完成：宽表数据已下载、发送并归档
```

## 🛡️ 错误处理

### 1. 邮件发送失败
- 宽表文件保留在当前目录
- 提示用户可手动处理
- 不执行文件移动操作

### 2. 文件移动失败
- 邮件已成功发送
- 文件归档失败，但不影响主要功能
- 提供警告信息，用户可手动移动文件

### 3. 数据库连接失败
- 整个流程终止
- 不生成宽表文件
- 不执行后续操作

## ✅ 测试验证

已通过完整的功能测试：

1. **导入功能测试**: ✅ 通过
   - 配置文件导入正常
   - 文件管理器导入正常
   - 模块初始化成功

2. **文件移动测试**: ✅ 通过
   - 创建测试文件成功
   - 文件移动到已处理文件夹成功
   - 原文件删除，新文件存在
   - 清理测试文件成功

## 🔧 配置说明

该功能使用统一配置文件 `config.py` 中的设置：

- **数据库配置**: `get_db_config('remote')`
- **邮件配置**: `EMAIL_CONFIG`
- **文件路径配置**: `FILE_CONFIG`

## 📝 使用建议

1. **定期清理**: 建议定期清理"已处理"文件夹中的旧文件
2. **监控日志**: 关注执行日志，及时发现和处理异常
3. **备份重要**: 重要的宽表文件建议额外备份
4. **权限检查**: 确保程序对"已处理"文件夹有读写权限

## 🎉 功能优势

1. **自动化**: 无需手动移动文件，减少人工操作
2. **整洁性**: 保持工作目录整洁，已处理文件自动归档
3. **可追溯**: 通过文件时间戳可以追溯处理历史
4. **容错性**: 即使文件移动失败，也不影响核心邮件发送功能
5. **统一管理**: 使用统一的文件管理器，保持代码一致性

该功能已完全集成到现有工作流程中，提升了系统的自动化程度和文件管理效率。
