# 疑似拆分立项检测系统说明

## 📋 系统概述

疑似拆分立项检测系统是基于kuanbiao视图数据，自动检测可能存在拆分立项风险的项目组合的智能分析工具。

### 🎯 检测逻辑

**疑似拆分立项定义：**
在3个月内（签约上报日期），同一个上游客户的不超过400万的那部分项目的`效益评估_概算__IT收入`的累加值超过400万，那么涉及到的项目就判断为疑似拆分立项。

### 🔍 检测条件

1. **时间窗口**：3个月（90天）
2. **客户维度**：按收入侧客户分组
3. **金额阈值**：
   - 单个项目IT收入 ≤ 400万元
   - 累加IT收入 > 400万元
4. **风险等级**：
   - 高风险：累加IT收入 > 1000万元
   - 中风险：累加IT收入 600-1000万元
   - 低风险：累加IT收入 400-600万元

## 📁 文件结构

```
dict_romte/
├── detect_yisi_chaifen_kuanbiao.py      # kuanbiao视图检测程序（推荐）
├── detect_yisi_chaifen_projects.py      # sign_data_detail表检测程序
├── query_yisi_chaifen_results.py        # 结果查询工具
├── compare_detection_results.py         # 检测结果对比分析
├── show_high_risk_cases.py              # 高风险案例展示
├── check_sign_data_detail.py            # 数据表检查工具
├── dict_prj_yisi_chaifen_queries.sql    # SQL查询脚本
└── 疑似拆分立项检测系统说明.md          # 本说明文档
```

## 🚀 使用方法

### 1. 运行检测程序

#### 方案一：kuanbiao视图检测（推荐）
```bash
# 进入项目目录
cd dict_romte

# 运行kuanbiao视图检测程序
python detect_yisi_chaifen_kuanbiao.py
```

#### 方案二：sign_data_detail表检测
```bash
# 运行sign_data_detail表检测程序
python detect_yisi_chaifen_projects.py
```

**程序执行流程：**
1. 连接远程数据库
2. 创建结果表 `dict_prj_yisi_chaifen`
3. 从 `kuanbiao` 表获取数据
4. 执行疑似拆分立项检测算法
5. 将检测结果保存到数据库
6. 输出统计信息

### 2. 查询检测结果

```bash
# 运行查询工具
python query_yisi_chaifen_results.py
```

**查询功能：**
- 查看所有检测结果
- 查看统计信息
- 按客户名称查询
- 导出到Excel文件

### 3. 使用SQL查询

```sql
-- 查看所有高风险检测结果
SELECT * FROM dict_prj_yisi_chaifen 
WHERE 风险等级 = '高' 
ORDER BY IT收入累加值 DESC;

-- 查看统计信息
SELECT 风险等级, COUNT(*) as 数量, AVG(IT收入累加值) as 平均IT收入
FROM dict_prj_yisi_chaifen 
GROUP BY 风险等级;
```

## 📊 结果表结构

### dict_prj_yisi_chaifen 表字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INT | 自增主键 |
| 客户名称 | VARCHAR(500) | 上游客户名称 |
| 时间窗口开始 | DATE | 3个月时间窗口开始日期 |
| 时间窗口结束 | DATE | 3个月时间窗口结束日期 |
| 涉及项目数量 | INT | 涉及的项目数量 |
| 项目编码列表 | TEXT | 涉及的项目编码（逗号分隔） |
| 项目名称列表 | TEXT | 涉及的项目名称（逗号分隔） |
| IT收入累加值 | DECIMAL(15,2) | 效益评估_概算__IT收入累加值（万元） |
| 单项目最大IT收入 | DECIMAL(15,2) | 单个项目最大IT收入（万元） |
| 风险等级 | VARCHAR(20) | 风险等级：高/中/低 |
| 检测时间 | TIMESTAMP | 检测时间 |
| 备注 | TEXT | 备注信息 |

## 📈 检测结果示例

### 最新检测统计（2025-07-30 双数据源版）

#### kuanbiao视图检测结果（推荐）
- **总检测结果数量**：21个
- **涉及客户数量**：11个
- **平均IT收入累加值**：653.31万元
- **最大IT收入累加值**：1039.74万元
- **风险等级分布**：
  - 高风险：3个（平均IT收入1033.49万元）
  - 中风险：7个（平均IT收入748.00万元）
  - 低风险：11个（平均IT收入489.37万元）

#### sign_data_detail表检测结果
- **总检测结果数量**：647个
- **涉及客户数量**：423个
- **平均IT收入累加值**：5095.99万元
- **最大IT收入累加值**：56107.72万元
- **风险等级分布**：
  - 高风险：479个（平均IT收入6652.04万元）
  - 中风险：98个（平均IT收入785.14万元）
  - 低风险：70个（平均IT收入483.28万元）

### 双数据源对比分析

| 特征 | kuanbiao视图 | sign_data_detail表 |
|------|-------------|-------------------|
| 数据量 | 1,055条 | 54,619条 |
| 检测精度 | 高（精准检测） | 中（广泛筛查） |
| 检测结果数 | 21个 | 647个 |
| 字段完整性 | 完整（17个字段） | 基础（主要字段） |
| 多维分析 | 支持（行业、区域、阶段） | 有限 |
| 数据去重 | 智能去重 | 基础去重 |
| 适用场景 | 精准分析、深度调研 | 大规模初筛 |

### 高风险案例（kuanbiao视图）

1. **中山市公安局**
   - IT收入累加值：1039.74万元
   - 涉及项目：6个
   - 项目阶段：售后阶段(4), 售中阶段(2)
   - 所属行业：执法(6)
   - 风险等级：高

2. **中山市公安局横栏分局**
   - IT收入累加值：842.82万元
   - 涉及项目：4个
   - 项目阶段：售中阶段(4)
   - 所属行业：执法(3), 交通(1)
   - 风险等级：中

## ⚠️ 注意事项

### 数据质量要求

1. **必需字段**：
   - 收入侧客户（不能为空）
   - 签约上报日期（不能为空）
   - 效益评估_概算__IT收入（不能为空）

2. **日期格式支持**：
   - YYYY-MM-DD
   - YYYY/MM/DD
   - YYYY年MM月DD日
   - 带时间的格式

3. **金额格式处理**：
   - 自动清理非数字字符
   - 支持小数和负数
   - 无效金额按0处理

### 检测算法说明

1. **时间窗口滑动**：
   - 以每个项目的签约日期为起点
   - 向后延伸90天作为时间窗口
   - 检查窗口内的所有项目

2. **项目筛选**：
   - 只考虑单个项目IT收入 ≤ 400万的项目
   - 至少需要2个项目才可能构成拆分立项

3. **重复检测处理**：
   - 同一客户可能在不同时间窗口被多次检测
   - 这是正常现象，反映了持续的风险状态

## 🔧 维护和优化

### 定期维护建议

1. **数据更新**：
   - 建议每周运行一次检测程序
   - 确保kuanbiao表数据及时更新

2. **结果清理**：
   - 定期清理过期的检测结果
   - 保留最近6个月的检测记录

3. **阈值调整**：
   - 根据业务需要调整400万元阈值
   - 可修改风险等级划分标准

### 性能优化

1. **数据库索引**：
   ```sql
   -- 为查询性能添加索引
   CREATE INDEX idx_customer ON dict_prj_yisi_chaifen(客户名称);
   CREATE INDEX idx_risk_level ON dict_prj_yisi_chaifen(风险等级);
   CREATE INDEX idx_it_income ON dict_prj_yisi_chaifen(IT收入累加值);
   ```

2. **批量处理**：
   - 大数据量时可考虑分批处理
   - 按客户或时间段分组处理

## 📞 技术支持

如有问题或建议，请联系系统维护人员。

---

*本文档最后更新时间：2025-07-30*
