@echo off
:: Set console encoding to UTF-8
chcp 65001 >nul 2>&1

:: Set Python encoding environment variables
set PYTHON<PERSON>ENCODING=utf-8
set LANG=zh_CN.UTF-8
set PYTH<PERSON><PERSON>GACYWINDOWSSTDIO=utf-8

:: Clear screen
cls

:: Show header (using English to avoid encoding issues)
echo ========================================
echo DICT Spider Main Program Launcher
echo Auto set UTF-8 encoding
echo ========================================
echo.

:: Check if main program exists
if not exist "main_dict_local.py" (
    echo ERROR: main_dict_local.py not found
    echo Please run this batch file in the correct directory
    pause
    exit /b 1
)

:: Run the main program
echo Starting main program...
echo Current directory: %CD%
echo.

python -u main_dict_local.py

:: Show completion status
echo.
echo ========================================
if errorlevel 1 (
    echo Program execution failed
) else (
    echo Program execution completed successfully
)
echo ========================================
echo.
pause
