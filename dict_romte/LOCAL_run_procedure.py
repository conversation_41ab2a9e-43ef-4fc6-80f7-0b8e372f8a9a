#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LOCAL_run_procedure.py - 运行本地数据库存储过程脚本
功能：连接本地数据库并执行指定的存储过程

使用方法：
1. 直接运行：python LOCAL_run_procedure.py
2. 指定存储过程：python LOCAL_run_procedure.py procedure_name
"""

# 自动设置UTF-8编码，解决中文乱码问题
try:
    import utf8_setup
except ImportError:
    # 如果utf8_setup模块不存在，使用内置设置
    import sys, os
    if sys.platform.startswith('win'):
        os.system('chcp 65001 >nul 2>&1')
        os.environ['PYTHONIOENCODING'] = 'utf-8'

import pymysql
from datetime import datetime
import sys
import traceback
import time

# 从配置文件导入数据库配置
from config import get_db_config

# 本地数据库配置
LOCAL_DB_CONFIG = get_db_config('default')

def connect_to_database(config, db_type="数据库"):
    """连接到数据库"""
    try:
        print(f"[信息] 正在连接{db_type} {config['host']}:{config['port']}...")
        conn = pymysql.connect(**config)
        print(f"[成功] 已连接到{db_type}: {config['database']}")
        return conn
    except Exception as e:
        print(f"[错误] 连接{db_type}失败: {e}")
        return None

def list_procedures(conn):
    """列出数据库中所有的存储过程"""
    try:
        cursor = conn.cursor()
        cursor.execute("SHOW PROCEDURE STATUS WHERE Db = %s", (LOCAL_DB_CONFIG['database'],))
        procedures = cursor.fetchall()
        cursor.close()
        
        if procedures:
            print(f"[信息] 数据库 {LOCAL_DB_CONFIG['database']} 中的存储过程:")
            for i, proc in enumerate(procedures, 1):
                proc_name = proc[1]  # 存储过程名称在第二列
                created = proc[4]    # 创建时间在第五列
                print(f"  {i}. {proc_name} (创建时间: {created})")
            return [proc[1] for proc in procedures]
        else:
            print(f"[信息] 数据库 {LOCAL_DB_CONFIG['database']} 中没有找到存储过程")
            return []
    except Exception as e:
        print(f"[错误] 获取存储过程列表失败: {e}")
        return []

def check_procedure_exists(conn, procedure_name):
    """检查存储过程是否存在"""
    try:
        cursor = conn.cursor()
        cursor.execute("SHOW PROCEDURE STATUS WHERE Name = %s AND Db = %s", 
                      (procedure_name, LOCAL_DB_CONFIG['database']))
        result = cursor.fetchone()
        cursor.close()
        
        if result:
            print(f"[信息] 存储过程 {procedure_name} 存在")
            return True
        else:
            print(f"[错误] 存储过程 {procedure_name} 不存在")
            return False
    except Exception as e:
        print(f"[错误] 检查存储过程存在性失败: {e}")
        return False

def get_table_record_count(conn, table_name):
    """获取表记录数"""
    try:
        cursor = conn.cursor()
        cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
        count = cursor.fetchone()[0]
        cursor.close()
        print(f"[信息] 表 {table_name} 当前记录数: {count}")
        return count
    except Exception as e:
        print(f"[错误] 获取表 {table_name} 记录数失败: {e}")
        return 0

def check_table_exists(conn, table_name):
    """检查表是否存在"""
    try:
        cursor = conn.cursor()
        cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
        result = cursor.fetchone()
        cursor.close()
        
        if result:
            print(f"[信息] 表 {table_name} 存在")
            return True
        else:
            print(f"[警告] 表 {table_name} 不存在")
            return False
    except Exception as e:
        print(f"[错误] 检查表存在性失败: {e}")
        return False

def run_procedure(conn, procedure_name, *args):
    """执行存储过程"""
    try:
        print(f"[信息] 开始执行存储过程 {procedure_name}...")
        if args:
            print(f"[信息] 传入参数: {args}")
        
        start_time = time.time()
        
        cursor = conn.cursor()
        
        # 执行存储过程
        if args:
            cursor.callproc(procedure_name, args)
        else:
            cursor.callproc(procedure_name)
        
        # 获取结果（如果有）
        results = []
        try:
            # 尝试获取结果集
            while True:
                result = cursor.fetchall()
                if result:
                    results.append(result)
                if not cursor.nextset():
                    break
        except:
            pass  # 如果没有结果集，忽略错误
        
        # 提交事务
        conn.commit()
        cursor.close()
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"[成功] 存储过程 {procedure_name} 执行完成")
        print(f"[统计] 执行耗时: {execution_time:.2f}秒")
        
        # 显示结果（如果有）
        if results:
            print(f"[结果] 存储过程返回了 {len(results)} 个结果集")
            for i, result_set in enumerate(results, 1):
                print(f"  结果集 {i}: {len(result_set)} 行数据")
        
        return True, results
    except Exception as e:
        print(f"[错误] 执行存储过程失败: {e}")
        print(traceback.format_exc())
        conn.rollback()
        return False, []

def interactive_mode(conn):
    """交互模式：让用户选择要执行的存储过程"""
    print("\n" + "="*60)
    print("[交互] 交互模式 - 选择要执行的存储过程")
    print("="*60)
    
    # 获取所有存储过程
    procedures = list_procedures(conn)
    
    if not procedures:
        print("[信息] 没有可用的存储过程")
        return False
    
    print(f"\n请选择要执行的存储过程 (1-{len(procedures)}):")
    try:
        choice = int(input("请输入序号: "))
        if 1 <= choice <= len(procedures):
            selected_procedure = procedures[choice - 1]
            print(f"\n[选择] 将执行存储过程: {selected_procedure}")
            
            # 询问是否需要参数
            params_input = input("是否需要传入参数? (输入参数用逗号分隔，直接回车表示无参数): ").strip()
            
            if params_input:
                params = [param.strip() for param in params_input.split(',')]
                success, results = run_procedure(conn, selected_procedure, *params)
            else:
                success, results = run_procedure(conn, selected_procedure)
            
            return success
        else:
            print("[错误] 无效的选择")
            return False
    except ValueError:
        print("[错误] 请输入有效的数字")
        return False
    except KeyboardInterrupt:
        print("\n[信息] 用户取消操作")
        return False

def main():
    """主函数"""
    print("=" * 80)
    print("LOCAL_run_procedure.py - 执行本地数据库存储过程")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)

    # 连接本地数据库
    print("\n1. 连接本地数据库...")
    local_conn = connect_to_database(LOCAL_DB_CONFIG, "本地数据库")
    if not local_conn:
        print("[错误] 无法连接本地数据库，程序退出")
        sys.exit(1)

    try:
        # 检查命令行参数
        if len(sys.argv) > 1:
            # 命令行模式：直接执行指定的存储过程
            procedure_name = sys.argv[1]
            procedure_args = sys.argv[2:] if len(sys.argv) > 2 else []
            
            print(f"\n2. 检查存储过程 {procedure_name}...")
            if not check_procedure_exists(local_conn, procedure_name):
                print("[错误] 指定的存储过程不存在，程序退出")
                return False

            print(f"\n3. 执行存储过程 {procedure_name}...")
            success, results = run_procedure(local_conn, procedure_name, *procedure_args)
            
            if success:
                print("\n" + "=" * 80)
                print("[成功] 存储过程执行成功!")
                print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                print("=" * 80)
                return True
            else:
                print("\n" + "=" * 80)
                print("[失败] 存储过程执行失败!")
                print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                print("=" * 80)
                return False
        else:
            # 交互模式：让用户选择存储过程
            success = interactive_mode(local_conn)
            
            if success:
                print("\n" + "=" * 80)
                print("[完成] 存储过程执行完成!")
                print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                print("=" * 80)
                return True
            else:
                print("\n" + "=" * 80)
                print("[失败] 操作未完成或失败!")
                print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                print("=" * 80)
                return False

    except Exception as e:
        print(f"\n[错误] 程序执行过程中出现异常: {e}")
        print(traceback.format_exc())
        return False
    finally:
        # 关闭数据库连接
        if local_conn:
            local_conn.close()
            print("[信息] 本地数据库连接已关闭")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
