@echo off
:: Set console code page to UTF-8
chcp 65001 >nul 2>&1

:: Set Python encoding environment variables
set PYTHONIOENCODING=utf-8
set LANG=zh_CN.UTF-8
set PYTHONLEGACYWINDOWSSTDIO=utf-8

:: Clear screen and show header
cls
echo ========================================
echo DICT Spider Main Program Launcher
echo Auto set UTF-8 encoding
echo ========================================
echo.

:: Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python and add it to PATH
    pause
    exit /b 1
)

:: Check if main_dict_local.py exists
if not exist "main_dict_local.py" (
    echo ERROR: main_dict_local.py not found
    echo Please make sure you are in the correct directory
    pause
    exit /b 1
)

:: Show current directory
echo Current directory: %CD%
echo.

:: Run the main program
echo Starting main program...
echo ========================================
python main_dict_local.py

:: Check exit code
if errorlevel 1 (
    echo.
    echo ========================================
    echo ERROR: Program execution failed
    echo Exit code: %errorlevel%
    echo ========================================
) else (
    echo.
    echo ========================================
    echo Program execution completed successfully
    echo ========================================
)

echo.
echo Press any key to exit...
pause >nul
