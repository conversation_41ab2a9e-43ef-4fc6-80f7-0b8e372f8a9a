#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 自动设置UTF-8编码，解决中文乱码问题
try:
    import utf8_setup
except ImportError:
    # 如果utf8_setup模块不存在，使用内置设置
    import sys, os
    if sys.platform.startswith('win'):
        os.system('chcp 65001 >nul 2>&1')
        os.environ['PYTHONIOENCODING'] = 'utf-8'

import pandas as pd
import pymysql
import warnings
import sys
import os
import json
import getpass
from sqlalchemy import create_engine
from pathlib import Path
from file_manager import move_merged_header_file

# 忽略pandas的警告
warnings.filterwarnings('ignore')

# 从配置文件导入数据库配置
from config import get_db_config

# 默认数据库连接信息
DEFAULT_DB_CONFIG = get_db_config('default')

# 尝试加载数据库配置文件
def load_db_config():
    config_file = Path('db_config.json')
    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"[信息] 已从{config_file}加载数据库配置")
            return config
        except Exception as e:
            print(f"[警告] 加载数据库配置文件失败: {e}")
    return DEFAULT_DB_CONFIG

# 保存数据库配置到文件
def save_db_config(config):
    config_file = Path('db_config.json')
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
        print(f"[信息] 数据库配置已保存到{config_file}")
        return True
    except Exception as e:
        print(f"[警告] 保存数据库配置文件失败: {e}")
        return False

# 交互式输入数据库配置
def input_db_config():
    print("\n请输入MySQL数据库连接信息:")
    config = {}
    config['host'] = input(f"MySQL主机地址 [默认: {DEFAULT_DB_CONFIG['host']}]: ") or DEFAULT_DB_CONFIG['host']

    # 尝试将端口转换为整数
    port_str = input(f"MySQL端口 [默认: {DEFAULT_DB_CONFIG['port']}]: ") or str(DEFAULT_DB_CONFIG['port'])
    try:
        config['port'] = int(port_str)
    except ValueError:
        print(f"[警告] 端口必须是数字，使用默认值{DEFAULT_DB_CONFIG['port']}")
        config['port'] = DEFAULT_DB_CONFIG['port']

    config['user'] = input(f"MySQL用户名 [默认: {DEFAULT_DB_CONFIG['user']}]: ") or DEFAULT_DB_CONFIG['user']
    config['password'] = getpass.getpass(f"MySQL密码 [按Enter使用默认密码]: ") or DEFAULT_DB_CONFIG['password']
    config['database'] = input(f"MySQL数据库名 [默认: {DEFAULT_DB_CONFIG['database']}]: ") or DEFAULT_DB_CONFIG['database']
    config['charset'] = input(f"MySQL字符集 [默认: {DEFAULT_DB_CONFIG['charset']}]: ") or DEFAULT_DB_CONFIG['charset']

    save_config = input("\n是否保存这个配置以便下次使用？(y/n) [默认: y]: ").lower() or 'y'
    if save_config == 'y':
        save_db_config(config)

    return config

# 加载数据库配置
DB_CONFIG = load_db_config()

def test_db_connection(config=None):
    """测试数据库连接"""
    if config is None:
        config = DB_CONFIG

    try:
        print(f"[信息] 测试连接到MySQL数据库 {config['host']}:{config['port']}...")
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()
        cursor.close()
        conn.close()
        print(f"[成功] 数据库连接成功，MySQL版本: {version[0]}")
        return True
    except Exception as e:
        print(f"[错误] 数据库连接失败: {e}")
        return False

def import_to_mysql(input_file=None, db_config=None):
    # 使用提供的数据库配置或默认配置
    if db_config is None:
        db_config = DB_CONFIG

    # 如果没有提供输入文件，尝试使用命令行参数
    if input_file is None:
        if len(sys.argv) > 1:
            input_file = sys.argv[1]
        else:
            # 查找最新的merged_header_文件
            files = [f for f in os.listdir('.') if f.startswith('merged_header_') and f.endswith('.xlsx')]
            if not files:
                print("[错误] 未找到合并表头后的文件，请先运行merge_headers.py")
                return False
            # 按修改时间排序，获取最新的文件
            input_file = max(files, key=os.path.getmtime)
            print(f"[信息] 自动选择最新的文件: {input_file}")

    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"[错误] 文件不存在: {input_file}")
        return False

    # 读取Excel文件
    print(f"[信息] 正在读取Excel文件: {input_file}")
    df = pd.read_excel(input_file)

    # 检查数据
    print(f"[信息] 读取到 {len(df)} 行数据")
    print("[信息] 表头列名:", df.columns.tolist()[:5], "...(等共{len(df.columns)}列)")

    # 测试数据库连接
    if not test_db_connection(db_config):
        # 检查是否在交互式环境中运行
        if sys.stdout.isatty():
            # 在终端中运行，可以进行交互
            retry = input("\n是否要重新输入数据库配置？(y/n) [默认: y]: ").lower() or 'y'
            if retry == 'y':
                db_config = input_db_config()
                if not test_db_connection(db_config):
                    print("[错误] 使用新配置仍然无法连接到数据库，操作终止")
                    return False
            else:
                print("[错误] 无法连接到数据库，操作终止")
                return False
        else:
            # 在非交互式环境中运行，直接返回错误
            print("[错误] 无法连接到数据库，请先运行 'python import_to_mysql.py --config' 配置数据库")
            return False

    # 连接数据库
    print("[信息] 正在连接到MySQL数据库...")
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
    except Exception as e:
        print(f"[错误] 连接数据库失败: {e}")
        return False

    try:
        # 检查表是否存在，如果不存在则创建
        print("[信息] 检查数据表是否存在...")
        cursor.execute("SHOW TABLES LIKE 'sign_data_detail'")
        if not cursor.fetchone():
            print("[信息] 数据表不存在，正在创建...")
            # 使用TEXT类型而不是VARCHAR(255)来避免行大小限制
            columns = []
            for col in df.columns:
                # 替换特殊字符，确保列名有效
                safe_col = col.replace(' ', '_').replace('-', '_').replace('(', '').replace(')', '')
                columns.append(f"`{safe_col}` TEXT")

            create_table_sql = """
            CREATE TABLE `sign_data_detail` (
                id INT AUTO_INCREMENT PRIMARY KEY,
                {0},
                import_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """.format(', '.join(columns))

            cursor.execute(create_table_sql)
            conn.commit()
            print("[成功] 数据表创建成功")

        # 使用pandas的to_sql方法直接导入数据
        print("[信息] 正在使用pandas to_sql方法插入数据...")

        # 处理列名，确保有效
        df_copy = df.copy()
        df_copy.columns = [col.replace(' ', '_').replace('-', '_').replace('(', '').replace(')', '') for col in df.columns]

        # 创建SQLAlchemy引擎
        engine_url = f"mysql+pymysql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}?charset={db_config['charset']}"
        engine = create_engine(engine_url)

        # 使用to_sql方法将数据导入到MySQL
        df_copy.to_sql('sign_data_detail', engine, if_exists='append', index=False, chunksize=10)

        print(f"[成功] 成功导入 {len(df_copy)} 行数据到 dict_spider.sign_data_detail 表")

        # 导入成功后，将merged_header文件移动到"已处理"文件夹
        try:
            success, new_path = move_merged_header_file(os.path.basename(input_file))
            if success:
                print(f"[迁移] 已处理文件已迁移到已处理文件夹")
            else:
                print(f"[警告] 已处理文件迁移失败")
        except Exception as e:
            print(f"[警告] 迁移已处理文件时出错: {e}")

        return True

    except Exception as e:
        conn.rollback()
        print(f"[错误] 导入数据时出错: {e}")
        return False
    finally:
        cursor.close()
        conn.close()
        print("[信息] 数据库连接已关闭")

def main():
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == '--config':
        # 重新配置数据库连接
        print("\n开始配置数据库连接...")
        new_config = input_db_config()
        if test_db_connection(new_config):
            print("[成功] 数据库配置已更新")
        else:
            print("[错误] 数据库配置测试失败")
    else:
        # 正常导入数据
        import_to_mysql()

if __name__ == "__main__":
    main()