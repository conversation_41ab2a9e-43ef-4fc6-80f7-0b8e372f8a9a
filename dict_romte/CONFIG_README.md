# 配置文件使用说明

## 概述

为了方便维护和管理系统配置，我们将所有配置信息统一抽取到 `config.py` 文件中。这包括：

- 登录账号信息
- 数据库配置信息  
- 发送邮件的账号信息
- 接收邮件的邮箱信息
- API地址配置
- 文件路径配置
- 系统参数配置

## 配置文件结构

### 1. 登录账号配置 (LOGIN_CONFIG)
```python
LOGIN_CONFIG = {
    'username': 'zheng<PERSON><PERSON>',           # 主要登录用户名
    'password': 'Dewen@428',            # 主要登录密码
    'login_url': 'https://dict.gmcc.net:30722/dictWeb/login',
    'backup_username': 'liaochulin',    # 备用用户名
    'backup_password': 'Liaochulin147!', # 备用密码
}
```

### 2. 数据库配置 (DB_CONFIG)
```python
# 远程数据库配置（主要使用）
REMOTE_DB_CONFIG = {
    'host': '*************',
    'port': 12136,
    'user': 'root',
    'password': '5eb9a11916e3a66d',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

# 本地数据库配置（备用）
LOCAL_DB_CONFIG = {
    'host': 'localhost',
    'port': 12136,
    'user': 'root', 
    'password': '5eb9a11916e3a66d',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}
```

### 3. 邮件配置 (EMAIL_CONFIG)
```python
EMAIL_CONFIG = {
    'sender_email': '<EMAIL>',          # 发送邮箱
    'sender_password': 'XBbyfQEf2PpqUif5',      # 邮箱授权密码
    'smtp_server': 'smtp.126.com',              # SMTP服务器
    'smtp_port': 465,                           # SMTP端口
    'recipients': [                             # 收件人列表
        '<EMAIL>',
        '<EMAIL>'
    ],
    'subject_prefix': '[数据_DICT_系统]',          # 邮件主题前缀
}
```

### 4. API配置 (API_CONFIG)
```python
API_CONFIG = {
    'sign_detail_url': 'http://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp//preparation/queryProjectStart',
    'contract_url': 'http://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/contract/queryContractList',
    'base_url': 'http://dict.gmcc.net:30722/dictWeb',
    'gateway_url': 'http://dict.gmcc.net:30722/dictWeb/gatewayService',
}
```

### 5. 文件路径配置 (FILE_CONFIG)
```python
FILE_CONFIG = {
    'cookie_file': 'cookies.txt',               # Cookie文件路径
    'captcha_dir': 'captcha_images',            # 验证码图片目录
    'processed_dir': '已处理',                  # 已处理文件目录
    'log_file': 'main_bc_local_runlog.txt',     # 日志文件
    'temp_file_prefix': 'temp_',                # 临时文件前缀
    'merged_file_prefix': 'merged_header_temp_', # 合并文件前缀
    'contract_file_prefix': '合同信息列表_全部_',  # 合同文件前缀
}
```

## 使用方法

### 1. 导入配置
```python
# 导入所有配置
from config import LOGIN_CONFIG, EMAIL_CONFIG, API_CONFIG

# 导入辅助函数
from config import get_db_config, get_email_recipients_string, get_login_credentials
```

### 2. 获取数据库配置
```python
# 获取默认数据库配置
db_config = get_db_config()

# 获取远程数据库配置
remote_db = get_db_config('remote')

# 获取本地数据库配置  
local_db = get_db_config('local')
```

### 3. 获取登录信息
```python
# 获取主要登录凭据
username, password, login_url = get_login_credentials()

# 获取备用登录凭据
backup_username, backup_password, login_url = get_backup_login_credentials()
```

### 4. 获取邮件收件人
```python
# 获取收件人字符串（逗号分隔）
recipients = get_email_recipients_string()
# 结果: "<EMAIL>,<EMAIL>"
```

## 修改配置

### 1. 修改登录信息
编辑 `config.py` 文件中的 `LOGIN_CONFIG` 部分：
```python
LOGIN_CONFIG = {
    'username': '新用户名',
    'password': '新密码',
    # ... 其他配置保持不变
}
```

### 2. 修改数据库配置
编辑相应的数据库配置：
```python
REMOTE_DB_CONFIG = {
    'host': '新的数据库地址',
    'password': '新的数据库密码',
    # ... 其他配置
}
```

### 3. 修改邮件配置
```python
EMAIL_CONFIG = {
    'sender_email': '新的发送邮箱',
    'sender_password': '新的邮箱密码',
    'recipients': [
        '新的收件人*************',
        '新的收件人*************'
    ],
    # ... 其他配置
}
```

## 已修改的文件

以下文件已经修改为使用统一配置：

1. **login2cookie.py** - 使用 LOGIN_CONFIG 和 FILE_CONFIG
2. **import_to_mysql.py** - 使用 get_db_config()
3. **import_hetong_to_mysql.py** - 使用 get_db_config()
4. **LOCAL_run_procedure.py** - 使用 get_db_config()
5. **开工日期处理project_start_date_oldcookie.py** - 使用 get_db_config() 和 API_CONFIG
6. **DICT_download_local_kuanbiao_from_db2email.py** - 使用 EMAIL_CONFIG 和 get_db_config()
7. **NEW_sign_get_jsessionid.py** - 使用 FILE_CONFIG
8. **get_sign_detail.py** - 使用 FILE_CONFIG
9. **ne76_get_hetong_all.py** - 使用 FILE_CONFIG

## 配置验证

配置文件包含验证功能，可以检查配置的完整性：

```python
from config import validate_config

# 验证配置
if validate_config():
    print("配置验证通过")
else:
    print("配置验证失败，请检查配置文件")
```

## 注意事项

1. **安全性**：配置文件包含敏感信息（密码等），请确保文件权限设置正确
2. **备份**：修改配置前请备份原始配置文件
3. **测试**：修改配置后请测试相关功能是否正常工作
4. **版本控制**：如果使用版本控制，考虑将敏感配置信息放在单独的文件中，不提交到版本库

## 故障排除

### 1. 导入错误
如果出现 `ImportError: No module named 'config'`，请确保：
- config.py 文件在正确的目录中
- Python 路径设置正确

### 2. 配置验证失败
运行 `python config.py` 查看具体的验证错误信息

### 3. 数据库连接失败
检查数据库配置信息是否正确，网络是否可达

### 4. 邮件发送失败
检查邮箱配置和授权密码是否正确
