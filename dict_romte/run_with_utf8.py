#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
run_with_utf8.py - 使用UTF-8编码运行main_dict_local.py
解决Windows控制台中文乱码问题
"""

import os
import sys
import subprocess

def set_console_utf8():
    """设置控制台为UTF-8编码"""
    if sys.platform.startswith('win'):
        # 在Windows上设置控制台代码页为UTF-8
        try:
            # 设置控制台代码页为65001 (UTF-8)
            os.system('chcp 65001 >nul 2>&1')
        except:
            pass
        
        # 设置环境变量
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        os.environ['LANG'] = 'zh_CN.UTF-8'

def main():
    """主函数"""
    print("设置控制台编码为UTF-8...")
    set_console_utf8()
    
    print("启动main_dict_local.py...")
    
    # 设置环境变量
    env = os.environ.copy()
    env['PYTHONIOENCODING'] = 'utf-8'
    env['LANG'] = 'zh_CN.UTF-8'
    
    # 运行主程序
    try:
        result = subprocess.run([
            sys.executable, 'main_dict_local.py'
        ], env=env, cwd=os.path.dirname(os.path.abspath(__file__)))
        
        return result.returncode
    except Exception as e:
        print(f"运行出错: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
