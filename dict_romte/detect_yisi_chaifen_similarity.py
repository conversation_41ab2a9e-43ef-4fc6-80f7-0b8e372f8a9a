#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
detect_yisi_chaifen_similarity.py - 疑似拆分立项检测程序（增加相似度分析）
功能：根据kuanbiao视图数据，结合项目建设内容相似度分析，检测疑似拆分立项的项目

疑似拆分立项定义：
在3个月内（签约上报日期），同一个上游客户的不超过400万的那部分项目的
`效益评估_概算__IT收入`的累加值超过400万，并且（`项目建设内容及方案简介（CT）`的相似度在80%以上、
并且`项目建设内容及方案简介（IT）`的相似度在80%以上），那么涉及到的项目就判断为疑似拆分立项。

数据源：kuanbiao视图，包含完整的项目信息字段，并进行数据去重和相似度分析
作者：系统自动生成
创建时间：2025-07-30
更新时间：2025-07-30（增加相似度分析功能）
"""

# 自动设置UTF-8编码，解决中文乱码问题
try:
    import utf8_setup
except ImportError:
    # 如果utf8_setup模块不存在，使用内置设置
    import sys, os
    if sys.platform.startswith('win'):
        os.system('chcp 65001 >nul 2>&1')
        os.environ['PYTHONIOENCODING'] = 'utf-8'

import pymysql
import pandas as pd
from datetime import datetime, timedelta
import traceback
import re
import hashlib
import jieba
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
from config import get_db_config

def install_required_packages():
    """安装必需的包"""
    try:
        import jieba
        from sklearn.feature_extraction.text import TfidfVectorizer
        from sklearn.metrics.pairwise import cosine_similarity
        print("[信息] 所需包已安装")
        return True
    except ImportError as e:
        print(f"[警告] 缺少必需的包: {e}")
        print("[信息] 正在尝试安装...")
        try:
            import subprocess
            import sys
            
            # 安装jieba
            subprocess.check_call([sys.executable, "-m", "pip", "install", "jieba"])
            # 安装scikit-learn
            subprocess.check_call([sys.executable, "-m", "pip", "install", "scikit-learn"])
            
            print("[成功] 包安装完成")
            return True
        except Exception as install_error:
            print(f"[错误] 包安装失败: {install_error}")
            print("[建议] 请手动运行: pip install jieba scikit-learn")
            return False

def create_yisi_chaifen_table(conn):
    """创建dict_prj_yisi_chaifen表（增加相似度字段）"""
    cursor = conn.cursor()
    try:
        # 删除已存在的表
        cursor.execute("DROP TABLE IF EXISTS dict_prj_yisi_chaifen")
        
        # 创建新表，增加相似度分析字段
        create_table_sql = """
        CREATE TABLE dict_prj_yisi_chaifen (
            id INT AUTO_INCREMENT PRIMARY KEY,
            客户名称 VARCHAR(500) COMMENT '收入侧客户名称',
            时间窗口开始 DATE COMMENT '3个月时间窗口开始日期',
            时间窗口结束 DATE COMMENT '3个月时间窗口结束日期',
            涉及项目数量 INT COMMENT '涉及的项目数量',
            项目编码列表 TEXT COMMENT '涉及的项目编码列表（逗号分隔）',
            项目名称列表 TEXT COMMENT '涉及的项目名称列表（逗号分隔）',
            收入侧合同编码列表 TEXT COMMENT '收入侧合同编码列表（逗号分隔）',
            IT收入累加值 DECIMAL(15,2) COMMENT '效益评估_概算__IT收入累加值（万元）',
            单项目最大IT收入 DECIMAL(15,2) COMMENT '单个项目最大IT收入（万元）',
            合同含税金额累加值 DECIMAL(15,2) COMMENT '合同含税金额累加值（万元）',
            CT内容平均相似度 DECIMAL(5,2) COMMENT 'CT项目建设内容平均相似度（%）',
            IT内容平均相似度 DECIMAL(5,2) COMMENT 'IT项目建设内容平均相似度（%）',
            CT内容最高相似度 DECIMAL(5,2) COMMENT 'CT项目建设内容最高相似度（%）',
            IT内容最高相似度 DECIMAL(5,2) COMMENT 'IT项目建设内容最高相似度（%）',
            相似度分析详情 TEXT COMMENT '相似度分析详细信息',
            项目阶段分布 TEXT COMMENT '项目阶段分布统计',
            所属行业分布 TEXT COMMENT '所属行业分布统计',
            归属区县分布 TEXT COMMENT '归属区县分布统计',
            项目经理列表 TEXT COMMENT '项目经理列表（逗号分隔）',
            风险等级 VARCHAR(20) COMMENT '风险等级：高/中/低',
            检测时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '检测时间',
            数据来源 VARCHAR(50) DEFAULT 'kuanbiao视图+相似度' COMMENT '数据来源',
            备注 TEXT COMMENT '备注信息'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='疑似拆分立项检测结果表（相似度版）'
        """
        
        cursor.execute(create_table_sql)
        conn.commit()
        print("[成功] dict_prj_yisi_chaifen表创建完成")
        return True
        
    except Exception as e:
        print(f"[错误] 创建dict_prj_yisi_chaifen表失败: {e}")
        traceback.print_exc()
        return False
    finally:
        cursor.close()

def get_kuanbiao_data(conn):
    """获取kuanbiao视图数据"""
    try:
        print("[信息] 正在获取kuanbiao视图数据...")

        query = """
        SELECT
            `签约上报日期`,
            `项目编码`,
            `项目名称`,
            `合同含税金额（万元）`,
            `收入侧客户`,
            `收入侧合同编码`,
            `项目阶段`,
            `所属行业`,
            `归属区县`,
            `项目经理`,
            `前向合同签约时间`,
            `项目进度`,
            `效益评估_概算__IT收入利润率`,
            `效益评估_概算__IT收入`,
            `效益评估_概算__CT不含税收入（万元）`,
            `项目建设内容及方案简介（CT）`,
            `项目建设内容及方案简介（IT）`
        FROM kuanbiao
        WHERE `收入侧客户` IS NOT NULL
        AND `收入侧客户` != ''
        AND `签约上报日期` IS NOT NULL
        AND `签约上报日期` != ''
        AND `效益评估_概算__IT收入` IS NOT NULL
        AND `效益评估_概算__IT收入` != ''
        AND (`项目建设内容及方案简介（CT）` IS NOT NULL OR `项目建设内容及方案简介（IT）` IS NOT NULL)
        """

        df = pd.read_sql(query, conn)
        print(f"[信息] 获取到 {len(df)} 条原始数据")
        
        # 数据去重
        print("[信息] 正在进行数据去重...")
        
        # 创建去重标识：基于项目编码+收入侧客户+签约上报日期
        df['去重标识'] = df.apply(lambda row: hashlib.md5(
            f"{row['项目编码']}_{row['收入侧客户']}_{row['签约上报日期']}".encode('utf-8')
        ).hexdigest(), axis=1)
        
        # 去重，保留第一条记录
        df_dedup = df.drop_duplicates(subset=['去重标识'], keep='first')
        print(f"[信息] 去重后剩余 {len(df_dedup)} 条有效数据")
        
        # 删除去重标识列
        df_dedup = df_dedup.drop('去重标识', axis=1)
        
        return df_dedup

    except Exception as e:
        print(f"[错误] 获取kuanbiao数据失败: {e}")
        traceback.print_exc()
        return None

def preprocess_text(text):
    """预处理文本"""
    if pd.isna(text) or text == '' or text is None:
        return ""
    
    text = str(text).strip()
    
    # 移除特殊字符，保留中文、英文、数字
    text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', ' ', text)
    
    # 使用jieba分词
    words = jieba.cut(text)
    
    # 过滤停用词和短词
    stop_words = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'}
    filtered_words = [word.strip() for word in words if len(word.strip()) > 1 and word.strip() not in stop_words]
    
    return ' '.join(filtered_words)

def calculate_text_similarity(texts):
    """计算文本相似度矩阵"""
    if len(texts) < 2:
        return np.array([[1.0]])
    
    # 预处理文本
    processed_texts = [preprocess_text(text) for text in texts]
    
    # 过滤空文本
    non_empty_indices = [i for i, text in enumerate(processed_texts) if text.strip()]
    
    if len(non_empty_indices) < 2:
        return np.eye(len(texts))  # 返回单位矩阵
    
    non_empty_texts = [processed_texts[i] for i in non_empty_indices]
    
    try:
        # 使用TF-IDF向量化
        vectorizer = TfidfVectorizer(max_features=1000, ngram_range=(1, 2))
        tfidf_matrix = vectorizer.fit_transform(non_empty_texts)
        
        # 计算余弦相似度
        similarity_matrix = cosine_similarity(tfidf_matrix)
        
        # 创建完整的相似度矩阵
        full_similarity_matrix = np.eye(len(texts))
        for i, idx_i in enumerate(non_empty_indices):
            for j, idx_j in enumerate(non_empty_indices):
                full_similarity_matrix[idx_i][idx_j] = similarity_matrix[i][j]
        
        return full_similarity_matrix
        
    except Exception as e:
        print(f"[警告] 相似度计算失败: {e}")
        return np.eye(len(texts))

def analyze_similarity(projects_df):
    """分析项目组合的相似度"""
    if len(projects_df) < 2:
        return {
            'ct_avg_similarity': 0,
            'it_avg_similarity': 0,
            'ct_max_similarity': 0,
            'it_max_similarity': 0,
            'similarity_details': "项目数量不足，无法计算相似度"
        }
    
    # 获取CT和IT内容
    ct_contents = projects_df['项目建设内容及方案简介（CT）'].fillna('').tolist()
    it_contents = projects_df['项目建设内容及方案简介（IT）'].fillna('').tolist()
    
    # 计算CT相似度
    ct_similarity_matrix = calculate_text_similarity(ct_contents)
    ct_similarities = []
    for i in range(len(ct_similarity_matrix)):
        for j in range(i+1, len(ct_similarity_matrix)):
            ct_similarities.append(ct_similarity_matrix[i][j])
    
    # 计算IT相似度
    it_similarity_matrix = calculate_text_similarity(it_contents)
    it_similarities = []
    for i in range(len(it_similarity_matrix)):
        for j in range(i+1, len(it_similarity_matrix)):
            it_similarities.append(it_similarity_matrix[i][j])
    
    # 统计结果
    ct_avg = np.mean(ct_similarities) * 100 if ct_similarities else 0
    it_avg = np.mean(it_similarities) * 100 if it_similarities else 0
    ct_max = np.max(ct_similarities) * 100 if ct_similarities else 0
    it_max = np.max(it_similarities) * 100 if it_similarities else 0
    
    # 生成详细信息
    details = f"CT平均相似度:{ct_avg:.1f}%,最高:{ct_max:.1f}%; IT平均相似度:{it_avg:.1f}%,最高:{it_max:.1f}%"
    
    return {
        'ct_avg_similarity': ct_avg,
        'it_avg_similarity': it_avg,
        'ct_max_similarity': ct_max,
        'it_max_similarity': it_max,
        'similarity_details': details
    }

def parse_date(date_str):
    """解析日期字符串，支持多种格式"""
    if pd.isna(date_str) or date_str == '' or date_str is None:
        return None
    
    date_str = str(date_str).strip()
    
    # 支持的日期格式
    date_formats = [
        '%Y-%m-%d',
        '%Y/%m/%d', 
        '%Y年%m月%d日',
        '%Y-%m-%d %H:%M:%S',
        '%Y/%m/%d %H:%M:%S'
    ]
    
    for fmt in date_formats:
        try:
            return datetime.strptime(date_str, fmt).date()
        except ValueError:
            continue
    
    print(f"[警告] 无法解析日期格式: {date_str}")
    return None

def parse_amount(amount_str):
    """解析金额字符串，转换为数值"""
    if pd.isna(amount_str) or amount_str == '' or amount_str is None:
        return 0.0
    
    amount_str = str(amount_str).strip()
    
    # 移除非数字字符（保留小数点和负号）
    amount_str = re.sub(r'[^\d.-]', '', amount_str)
    
    try:
        return float(amount_str)
    except ValueError:
        print(f"[警告] 无法解析金额: {amount_str}")
        return 0.0

def get_distribution_stats(series):
    """获取分布统计信息"""
    if len(series) == 0:
        return ""
    
    # 统计各值的出现次数
    value_counts = series.value_counts()
    
    # 格式化为字符串
    stats = []
    for value, count in value_counts.head(5).items():  # 只显示前5个
        if pd.notna(value) and value != '':
            stats.append(f"{value}({count})")
    
    return ', '.join(stats)

def detect_yisi_chaifen_projects(df):
    """检测疑似拆分立项的项目（增加相似度分析）"""
    results = []
    processed_combinations = set()  # 用于避免重复检测

    print("[信息] 开始检测疑似拆分立项（包含相似度分析）...")

    # 数据预处理
    df['签约日期'] = df['签约上报日期'].apply(parse_date)
    df['IT收入'] = df['效益评估_概算__IT收入'].apply(parse_amount)
    df['合同含税金额'] = df['合同含税金额（万元）'].apply(parse_amount)

    # 过滤掉无效数据
    df = df[df['签约日期'].notna() & (df['IT收入'] > 0)]

    if len(df) == 0:
        print("[警告] 没有有效的数据进行分析")
        return results

    print(f"[信息] 有效数据条数: {len(df)}")

    # 按客户分组
    customer_groups = df.groupby('收入侧客户')

    for customer, customer_df in customer_groups:
        if len(customer_df) < 2:  # 至少需要2个项目才可能是拆分立项
            continue

        print(f"[信息] 分析客户: {customer} ({len(customer_df)}个项目)")

        # 按签约日期排序
        customer_df = customer_df.sort_values('签约日期').reset_index(drop=True)

        # 筛选单个项目IT收入不超过400万的项目
        small_projects_df = customer_df[customer_df['IT收入'] <= 400]

        if len(small_projects_df) < 2:
            continue

        # 使用滑动窗口检测，避免重复
        for i in range(len(small_projects_df)):
            start_date = small_projects_df.iloc[i]['签约日期']
            end_date = start_date + timedelta(days=90)  # 3个月

            # 获取时间窗口内的项目
            window_projects = small_projects_df[
                (small_projects_df['签约日期'] >= start_date) &
                (small_projects_df['签约日期'] <= end_date)
            ]

            if len(window_projects) < 2:
                continue

            # 计算IT收入累加值
            total_it_income = window_projects['IT收入'].sum()

            # 判断是否超过400万
            if total_it_income > 400:
                # 进行相似度分析
                print(f"[分析] 正在分析客户 {customer} 的项目相似度...")
                similarity_result = analyze_similarity(window_projects)

                # 判断相似度是否满足条件（CT和IT都要80%以上）
                ct_similarity_ok = similarity_result['ct_avg_similarity'] >= 80
                it_similarity_ok = similarity_result['it_avg_similarity'] >= 80

                if not (ct_similarity_ok and it_similarity_ok):
                    print(f"[跳过] 客户 {customer} 相似度不满足条件: CT={similarity_result['ct_avg_similarity']:.1f}%, IT={similarity_result['it_avg_similarity']:.1f}%")
                    continue

                # 创建项目组合的唯一标识，避免重复
                project_codes = sorted(window_projects['项目编码'].astype(str).tolist())
                combination_key = f"{customer}_{start_date}_{','.join(project_codes)}"

                if combination_key in processed_combinations:
                    continue

                processed_combinations.add(combination_key)

                # 计算风险等级
                if total_it_income > 1000:
                    risk_level = "高"
                elif total_it_income > 600:
                    risk_level = "中"
                else:
                    risk_level = "低"

                # 统计信息
                total_contract_amount = window_projects['合同含税金额'].sum()

                result = {
                    '客户名称': customer,
                    '时间窗口开始': start_date,
                    '时间窗口结束': end_date,
                    '涉及项目数量': len(window_projects),
                    '项目编码列表': ','.join(window_projects['项目编码'].astype(str)),
                    '项目名称列表': ','.join(window_projects['项目名称'].astype(str)),
                    '收入侧合同编码列表': ','.join(window_projects['收入侧合同编码'].fillna('').astype(str)),
                    'IT收入累加值': total_it_income,
                    '单项目最大IT收入': window_projects['IT收入'].max(),
                    '合同含税金额累加值': total_contract_amount,
                    'CT内容平均相似度': similarity_result['ct_avg_similarity'],
                    'IT内容平均相似度': similarity_result['it_avg_similarity'],
                    'CT内容最高相似度': similarity_result['ct_max_similarity'],
                    'IT内容最高相似度': similarity_result['it_max_similarity'],
                    '相似度分析详情': similarity_result['similarity_details'],
                    '项目阶段分布': get_distribution_stats(window_projects['项目阶段']),
                    '所属行业分布': get_distribution_stats(window_projects['所属行业']),
                    '归属区县分布': get_distribution_stats(window_projects['归属区县']),
                    '项目经理列表': ','.join(window_projects['项目经理'].fillna('').astype(str)),
                    '风险等级': risk_level,
                    '备注': f"时间窗口内{len(window_projects)}个项目IT收入累计{total_it_income:.2f}万元，CT相似度{similarity_result['ct_avg_similarity']:.1f}%，IT相似度{similarity_result['it_avg_similarity']:.1f}%"
                }

                results.append(result)
                print(f"[发现] 疑似拆分立项: {customer} - {len(window_projects)}个项目，累计IT收入{total_it_income:.2f}万元，CT相似度{similarity_result['ct_avg_similarity']:.1f}%，IT相似度{similarity_result['it_avg_similarity']:.1f}%")

    return results

def save_results_to_db(conn, results):
    """将检测结果保存到数据库"""
    if not results:
        print("[信息] 没有检测到疑似拆分立项，无需保存")
        return True

    cursor = conn.cursor()
    try:
        print(f"[信息] 正在保存 {len(results)} 条检测结果...")

        insert_sql = """
        INSERT INTO dict_prj_yisi_chaifen (
            客户名称, 时间窗口开始, 时间窗口结束, 涉及项目数量,
            项目编码列表, 项目名称列表, 收入侧合同编码列表, IT收入累加值,
            单项目最大IT收入, 合同含税金额累加值, CT内容平均相似度, IT内容平均相似度,
            CT内容最高相似度, IT内容最高相似度, 相似度分析详情, 项目阶段分布,
            所属行业分布, 归属区县分布, 项目经理列表, 风险等级, 备注
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """

        for result in results:
            cursor.execute(insert_sql, (
                result['客户名称'],
                result['时间窗口开始'],
                result['时间窗口结束'],
                result['涉及项目数量'],
                result['项目编码列表'],
                result['项目名称列表'],
                result['收入侧合同编码列表'],
                result['IT收入累加值'],
                result['单项目最大IT收入'],
                result['合同含税金额累加值'],
                result['CT内容平均相似度'],
                result['IT内容平均相似度'],
                result['CT内容最高相似度'],
                result['IT内容最高相似度'],
                result['相似度分析详情'],
                result['项目阶段分布'],
                result['所属行业分布'],
                result['归属区县分布'],
                result['项目经理列表'],
                result['风险等级'],
                result['备注']
            ))

        conn.commit()
        print(f"[成功] 已保存 {len(results)} 条检测结果到数据库")
        return True

    except Exception as e:
        print(f"[错误] 保存检测结果失败: {e}")
        traceback.print_exc()
        conn.rollback()
        return False
    finally:
        cursor.close()

def main():
    """主函数"""
    print("=" * 70)
    print("疑似拆分立项检测程序（相似度分析版）")
    print("=" * 70)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 检查并安装必需的包
    if not install_required_packages():
        print("[错误] 无法安装必需的包，程序退出")
        return False

    try:
        # 连接数据库
        print("\n1. 连接数据库...")
        db_config = get_db_config('remote')
        conn = pymysql.connect(**db_config)
        print(f"[成功] 已连接到数据库: {db_config['host']}:{db_config['port']}")

        # 创建结果表
        print("\n2. 创建结果表...")
        if not create_yisi_chaifen_table(conn):
            print("[错误] 创建结果表失败，程序退出")
            return False

        # 获取数据
        print("\n3. 获取kuanbiao视图数据...")
        df = get_kuanbiao_data(conn)
        if df is None or len(df) == 0:
            print("[错误] 获取数据失败或数据为空，程序退出")
            return False

        # 检测疑似拆分立项
        print("\n4. 检测疑似拆分立项（包含相似度分析）...")
        results = detect_yisi_chaifen_projects(df)

        # 保存结果
        print("\n5. 保存检测结果...")
        if not save_results_to_db(conn, results):
            print("[错误] 保存结果失败")
            return False

        # 输出统计信息
        print("\n" + "=" * 70)
        print("检测完成统计:")
        print(f"  - 总数据条数: {len(df)}")
        print(f"  - 检测到疑似拆分立项: {len(results)} 个")
        if results:
            high_risk = len([r for r in results if r['风险等级'] == '高'])
            medium_risk = len([r for r in results if r['风险等级'] == '中'])
            low_risk = len([r for r in results if r['风险等级'] == '低'])
            print(f"  - 风险等级分布: 高({high_risk}) 中({medium_risk}) 低({low_risk})")

            # 统计涉及客户数
            customers = set([r['客户名称'] for r in results])
            print(f"  - 涉及客户数量: {len(customers)} 个")

            # 统计相似度范围
            ct_similarities = [r['CT内容平均相似度'] for r in results]
            it_similarities = [r['IT内容平均相似度'] for r in results]
            print(f"  - CT相似度范围: {min(ct_similarities):.1f}% - {max(ct_similarities):.1f}%")
            print(f"  - IT相似度范围: {min(it_similarities):.1f}% - {max(it_similarities):.1f}%")

            # 统计IT收入范围
            it_incomes = [r['IT收入累加值'] for r in results]
            print(f"  - IT收入累加值范围: {min(it_incomes):.2f} - {max(it_incomes):.2f} 万元")
            print(f"  - 平均IT收入累加值: {sum(it_incomes)/len(it_incomes):.2f} 万元")

        print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 70)

        return True

    except Exception as e:
        print(f"[错误] 程序执行失败: {e}")
        traceback.print_exc()
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    success = main()
    if success:
        print("\n[成功] 疑似拆分立项检测完成（相似度分析版）")
    else:
        print("\n[失败] 疑似拆分立项检测失败")
