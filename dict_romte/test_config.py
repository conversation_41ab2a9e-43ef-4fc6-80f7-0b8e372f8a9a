#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
test_config.py - 配置文件测试脚本
用于验证配置文件的正确性和各个模块的配置导入
"""

# 自动设置UTF-8编码，解决中文乱码问题
try:
    import utf8_setup
except ImportError:
    # 如果utf8_setup模块不存在，使用内置设置
    import sys, os
    if sys.platform.startswith('win'):
        os.system('chcp 65001 >nul 2>&1')
        os.environ['PYTHONIOENCODING'] = 'utf-8'

def test_config_import():
    """测试配置文件导入"""
    print("=" * 60)
    print("测试配置文件导入...")
    print("=" * 60)
    
    try:
        from config import (
            LOGIN_CONFIG, 
            REMOTE_DB_CONFIG, 
            LOCAL_DB_CONFIG,
            EMAIL_CONFIG, 
            API_CONFIG, 
            FILE_CONFIG,
            SYSTEM_CONFIG
        )
        print("✅ 配置模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 配置模块导入失败: {e}")
        return False

def test_helper_functions():
    """测试辅助函数"""
    print("\n" + "=" * 60)
    print("测试辅助函数...")
    print("=" * 60)
    
    try:
        from config import (
            get_db_config,
            get_email_recipients_string,
            get_login_credentials,
            get_backup_login_credentials,
            validate_config
        )
        
        # 测试数据库配置获取
        print("\n1. 测试数据库配置获取:")
        default_db = get_db_config()
        remote_db = get_db_config('remote')
        local_db = get_db_config('local')
        
        print(f"   默认数据库: {default_db['host']}:{default_db['port']}")
        print(f"   远程数据库: {remote_db['host']}:{remote_db['port']}")
        print(f"   本地数据库: {local_db['host']}:{local_db['port']}")
        
        # 测试邮件收件人获取
        print("\n2. 测试邮件收件人获取:")
        recipients = get_email_recipients_string()
        print(f"   收件人: {recipients}")
        
        # 测试登录凭据获取
        print("\n3. 测试登录凭据获取:")
        username, password, login_url = get_login_credentials()
        print(f"   主要用户: {username}")
        print(f"   登录地址: {login_url}")
        
        backup_username, backup_password, backup_url = get_backup_login_credentials()
        print(f"   备用用户: {backup_username}")
        
        # 测试配置验证
        print("\n4. 测试配置验证:")
        is_valid = validate_config()
        print(f"   配置验证结果: {'通过' if is_valid else '失败'}")
        
        print("✅ 辅助函数测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 辅助函数测试失败: {e}")
        return False

def test_config_content():
    """测试配置内容"""
    print("\n" + "=" * 60)
    print("测试配置内容...")
    print("=" * 60)
    
    try:
        from config import LOGIN_CONFIG, EMAIL_CONFIG, API_CONFIG, FILE_CONFIG
        
        print("\n1. 登录配置:")
        print(f"   用户名: {LOGIN_CONFIG['username']}")
        print(f"   登录URL: {LOGIN_CONFIG['login_url']}")
        
        print("\n2. 邮件配置:")
        print(f"   发送邮箱: {EMAIL_CONFIG['sender_email']}")
        print(f"   SMTP服务器: {EMAIL_CONFIG['smtp_server']}:{EMAIL_CONFIG['smtp_port']}")
        print(f"   收件人数量: {len(EMAIL_CONFIG['recipients'])}")
        
        print("\n3. API配置:")
        print(f"   基础URL: {API_CONFIG['base_url']}")
        print(f"   签约明细API: {API_CONFIG['sign_detail_url']}")
        
        print("\n4. 文件配置:")
        print(f"   Cookie文件: {FILE_CONFIG['cookie_file']}")
        print(f"   验证码目录: {FILE_CONFIG['captcha_dir']}")
        print(f"   已处理目录: {FILE_CONFIG['processed_dir']}")
        
        print("✅ 配置内容测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 配置内容测试失败: {e}")
        return False

def test_file_imports():
    """测试各个文件的配置导入"""
    print("\n" + "=" * 60)
    print("测试各个文件的配置导入...")
    print("=" * 60)
    
    test_files = [
        'login2cookie.py',
        'import_to_mysql.py', 
        'import_hetong_to_mysql.py',
        'LOCAL_run_procedure.py',
        'DICT_download_local_kuanbiao_from_db2email.py',
        'NEW_sign_get_jsessionid.py',
        'get_sign_detail.py',
        'ne76_get_hetong_all.py'
    ]
    
    success_count = 0
    
    for file_name in test_files:
        try:
            # 简单的语法检查
            file_path = file_name  # 当前目录中的文件
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 检查配置导入的多种方式
            config_patterns = [
                'from config import',
                'import config',
                'get_db_config',
                'EMAIL_CONFIG',
                'LOGIN_CONFIG',
                'FILE_CONFIG',
                'API_CONFIG'
            ]

            found_config = False
            for pattern in config_patterns:
                if pattern in content:
                    found_config = True
                    break

            if found_config:
                print(f"   ✅ {file_name} - 已使用统一配置")
                success_count += 1
            else:
                print(f"   ⚠️  {file_name} - 未检测到配置导入")

        except FileNotFoundError:
            print(f"   ❌ {file_name} - 文件不存在")
        except Exception as e:
            print(f"   ❌ {file_name} - 检查失败: {e}")
    
    print(f"\n配置导入统计: {success_count}/{len(test_files)} 个文件已使用统一配置")
    return success_count > 0

def main():
    """主测试函数"""
    print("🚀 开始配置文件测试...")
    
    # 执行各项测试
    tests = [
        ("配置文件导入", test_config_import),
        ("辅助函数", test_helper_functions),
        ("配置内容", test_config_content),
        ("文件配置导入", test_file_imports)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！配置文件工作正常。")
    else:
        print("⚠️  部分测试失败，请检查配置文件。")
    
    return passed == len(results)

if __name__ == "__main__":
    main()
