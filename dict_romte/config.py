#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
config.py - 统一配置文件
包含所有系统配置信息：登录账号、数据库连接、邮件配置等
"""

# ================================
# 登录账号配置
# ================================
LOGIN_CONFIG = {
    # _DICT_网站登录信息
    'username': 'zhengdewen',
    'password': 'Dewen@428',
    'login_url': 'https://dict.gmcc.net:30722/dictWeb/login',
    
    # 备用账号（如需要）
    'backup_username': 'liaochulin',
    'backup_password': 'Liaochulin147!',
}

# ================================
# 数据库配置
# ================================
# 远程数据库配置（主要使用）
REMOTE_DB_CONFIG = {
    'host': '*************',
    'port': 12136,
    'user': 'root',
    'password': '5eb9a11916e3a66d',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

# 35本地数据库配置（主要使用）
LOCAL35_DB_CONFIG = {
    'host': 'localhost',
    'port': 12136,
    'user': 'root',
    'password': '5eb9a11916e3a66d',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

# 本地数据库配置（备用）
LOCAL_DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'cmcc12345',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

# 默认使用的数据库配置
DEFAULT_DB_CONFIG = REMOTE_DB_CONFIG

# ================================
# 邮件配置
# ================================
EMAIL_CONFIG = {
    # 发送邮箱配置
    'sender_email': '<EMAIL>',
    'sender_password': 'XBbyfQEf2PpqUif5',  # 授权密码
    'smtp_server': 'smtp.126.com',
    'smtp_port': 465,
    
    # 收件人配置
    'recipients': [
         # '<EMAIL>'
        '<EMAIL>',
        '<EMAIL>'
    ],
    
    # 邮件主题前缀
    'subject_prefix': '[数据_DICT_系统]',
}

# ================================
# API配置
# ================================
API_CONFIG = {
    # 签约项目明细API
    'sign_detail_url': 'http://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp//preparation/queryProjectStart',
    
    # 合同信息API
    'contract_url': 'http://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/contract/queryContractList',
    
    # 其他API地址
    'base_url': 'http://dict.gmcc.net:30722/dictWeb',
    'gateway_url': 'http://dict.gmcc.net:30722/dictWeb/gatewayService',
}

# ================================
# 文件路径配置
# ================================
FILE_CONFIG = {
    # Cookie文件路径
    'cookie_file': 'cookies.txt',
    
    # 验证码图片保存目录
    'captcha_dir': 'captcha_images',
    
    # 已处理文件目录
    'processed_dir': '已处理',
    
    # 日志文件
    'log_file': 'main_bc_local_runlog.txt',
    
    # 临时文件前缀
    'temp_file_prefix': 'temp_',
    'merged_file_prefix': 'merged_header_temp_',
    'contract_file_prefix': '合同信息列表_全部_',
}

# ================================
# 系统配置
# ================================
SYSTEM_CONFIG = {
    # 重试次数
    'max_login_attempts': 3,
    'max_api_retries': 3,
    
    # 超时设置（秒）
    'request_timeout': 30,
    'login_timeout': 60,
    
    # 延迟设置（秒）
    'retry_delay': 2,
    'captcha_delay': 1,
    
    # 编码设置
    'default_encoding': 'utf-8',
    'file_encoding': 'utf-8',
}

# ================================
# 辅助函数
# ================================
def get_db_config(config_type='default'):
    """
    获取数据库配置
    
    Args:
        config_type (str): 配置类型 ('default', 'remote', 'local')
        
    Returns:
        dict: 数据库配置_DICT_
    """
    if config_type == 'remote':
        return REMOTE_DB_CONFIG.copy()
    elif config_type == 'local35':
        return LOCAL35_DB_CONFIG.copy()
    elif config_type == 'local':
        return LOCAL_DB_CONFIG.copy()
    else:
        return DEFAULT_DB_CONFIG.copy()

def get_email_recipients_string():
    """
    获取收件人邮箱字符串（逗号分隔）
    
    Returns:
        str: 收件人邮箱字符串
    """
    return ','.join(EMAIL_CONFIG['recipients'])

def get_login_credentials():
    """
    获取登录凭据
    
    Returns:
        tuple: (username, password, login_url)
    """
    return (
        LOGIN_CONFIG['username'],
        LOGIN_CONFIG['password'],
        LOGIN_CONFIG['login_url']
    )

def get_backup_login_credentials():
    """
    获取备用登录凭据
    
    Returns:
        tuple: (username, password, login_url)
    """
    return (
        LOGIN_CONFIG['backup_username'],
        LOGIN_CONFIG['backup_password'],
        LOGIN_CONFIG['login_url']
    )

# ================================
# 配置验证
# ================================
def validate_config():
    """验证配置文件的完整性"""
    errors = []
    
    # 检查必要的配置项
    if not LOGIN_CONFIG.get('username') or not LOGIN_CONFIG.get('password'):
        errors.append("登录配置不完整：缺少用户名或密码")
    
    if not REMOTE_DB_CONFIG.get('host') or not REMOTE_DB_CONFIG.get('password'):
        errors.append("数据库配置不完整：缺少主机或密码")
    
    if not EMAIL_CONFIG.get('sender_email') or not EMAIL_CONFIG.get('sender_password'):
        errors.append("邮件配置不完整：缺少发送邮箱或密码")
    
    if not EMAIL_CONFIG.get('recipients'):
        errors.append("邮件配置不完整：缺少收件人")
    
    if errors:
        print("配置验证失败：")
        for error in errors:
            print(f"  - {error}")
        return False
    
    print("配置验证通过")
    return True

# 在导入时自动验证配置
if __name__ == "__main__":
    validate_config()
