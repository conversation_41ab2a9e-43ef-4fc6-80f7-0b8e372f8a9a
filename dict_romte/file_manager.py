#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
file_manager.py - 文件管理模块
功能：自动管理已处理的Excel文件，将其迁移到"已处理"文件夹
"""

# 自动设置UTF-8编码，解决中文乱码问题
try:
    import utf8_setup
except ImportError:
    # 如果utf8_setup模块不存在，使用内置设置
    import sys, os
    if sys.platform.startswith('win'):
        os.system('chcp 65001 >nul 2>&1')
        os.environ['PYTHONIOENCODING'] = 'utf-8'

import os
import shutil
from datetime import datetime
import glob

class FileManager:
    """文件管理器类"""
    
    def __init__(self, base_dir="."):
        """
        初始化文件管理器
        
        Args:
            base_dir (str): 基础目录，默认为当前目录
        """
        self.base_dir = base_dir
        self.processed_dir = os.path.join(base_dir, "已处理")
        self._ensure_processed_dir()
    
    def _ensure_processed_dir(self):
        """确保"已处理"目录存在"""
        if not os.path.exists(self.processed_dir):
            os.makedirs(self.processed_dir)
            print(f"[创建] 创建目录: {self.processed_dir}")
    
    def move_file_to_processed(self, file_path, description="文件"):
        """
        将文件移动到"已处理"目录
        
        Args:
            file_path (str): 要移动的文件路径
            description (str): 文件描述，用于日志输出
            
        Returns:
            bool: 移动是否成功
            str: 新的文件路径
        """
        try:
            if not os.path.exists(file_path):
                print(f"[警告] {description}不存在: {file_path}")
                return False, None
            
            # 获取文件名
            filename = os.path.basename(file_path)
            
            # 目标路径
            target_path = os.path.join(self.processed_dir, filename)
            
            # 如果目标文件已存在，添加时间戳
            if os.path.exists(target_path):
                name, ext = os.path.splitext(filename)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{name}_{timestamp}{ext}"
                target_path = os.path.join(self.processed_dir, filename)
            
            # 移动文件
            shutil.move(file_path, target_path)
            print(f"[迁移] {description}已迁移到: {target_path}")
            
            return True, target_path
            
        except Exception as e:
            print(f"[错误] 迁移{description}失败: {e}")
            return False, None
    
    def move_temp_sign_files(self):
        """移动temp_签约项目明细_*.xlsx文件"""
        pattern = os.path.join(self.base_dir, "temp_签约项目明细_*.xlsx")
        files = glob.glob(pattern)
        
        moved_files = []
        for file_path in files:
            success, new_path = self.move_file_to_processed(file_path, "签约项目明细文件")
            if success:
                moved_files.append(new_path)
        
        return moved_files
    
    def move_merged_header_files(self):
        """移动merged_header_temp_签约项目明细_*.xlsx文件"""
        pattern = os.path.join(self.base_dir, "merged_header_temp_签约项目明细_*.xlsx")
        files = glob.glob(pattern)
        
        moved_files = []
        for file_path in files:
            success, new_path = self.move_file_to_processed(file_path, "合并表头签约明细文件")
            if success:
                moved_files.append(new_path)
        
        return moved_files
    
    def move_contract_files(self):
        """移动合同信息列表_全部_*.xlsx文件"""
        pattern = os.path.join(self.base_dir, "合同信息列表_全部_*.xlsx")
        files = glob.glob(pattern)
        
        moved_files = []
        for file_path in files:
            success, new_path = self.move_file_to_processed(file_path, "合同信息列表文件")
            if success:
                moved_files.append(new_path)
        
        return moved_files
    
    def move_specific_file(self, filename, description=None):
        """
        移动指定的文件
        
        Args:
            filename (str): 文件名
            description (str): 文件描述
            
        Returns:
            bool: 移动是否成功
            str: 新的文件路径
        """
        file_path = os.path.join(self.base_dir, filename)
        if description is None:
            description = f"文件 {filename}"
        
        return self.move_file_to_processed(file_path, description)
    
    def cleanup_old_files(self, pattern, keep_latest=1):
        """
        清理旧文件，保留最新的几个
        
        Args:
            pattern (str): 文件匹配模式
            keep_latest (int): 保留最新文件的数量
        """
        files = glob.glob(os.path.join(self.base_dir, pattern))
        if len(files) <= keep_latest:
            return
        
        # 按修改时间排序
        files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
        
        # 移动旧文件
        for file_path in files[keep_latest:]:
            filename = os.path.basename(file_path)
            self.move_file_to_processed(file_path, f"旧文件 {filename}")

# 创建全局文件管理器实例
file_manager = FileManager()

def move_temp_sign_file(filename):
    """移动temp_签约项目明细文件的便捷函数"""
    return file_manager.move_specific_file(filename, "签约项目明细文件")

def move_merged_header_file(filename):
    """移动merged_header文件的便捷函数"""
    return file_manager.move_specific_file(filename, "合并表头签约明细文件")

def move_contract_file(filename):
    """移动合同信息文件的便捷函数"""
    return file_manager.move_specific_file(filename, "合同信息列表文件")

if __name__ == "__main__":
    # 测试文件管理器
    fm = FileManager()
    print("文件管理器测试完成")
