#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析既是客户又是供应商的企业
"""

import pymysql
import pandas as pd

DB_CONFIG = {
    'host': '*************',
    'port': 12136,
    'user': 'root',
    'password': '5eb9a11916e3a66d',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

try:
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    print('=== 分析既是客户又是供应商的企业 ===')
    
    # 获取有效数据
    query = """
    SELECT DISTINCT
        `项目编码`,
        `收入侧客户`,
        `成本侧供应商`,
        `一级场景`,
        `二级场景`,
        `项目名称`
    FROM kuanbiao 
    WHERE `收入侧客户` IS NOT NULL 
    AND `收入侧客户` != '' 
    AND `成本侧供应商` IS NOT NULL 
    AND `成本侧供应商` != ''
    """
    
    df = pd.read_sql(query, conn)
    print(f'获取到 {len(df)} 条有效数据')
    
    # 找出既是客户又是供应商的企业
    all_customers = set(df['收入侧客户'].dropna())
    all_suppliers = set(df['成本侧供应商'].dropna())
    overlap_companies = all_customers.intersection(all_suppliers)
    
    print(f'\\n既是客户又是供应商的企业数量: {len(overlap_companies)}')
    
    for company in overlap_companies:
        print(f'\\n企业: {company}')
        
        # 作为客户的项目
        as_customer = df[df['收入侧客户'] == company]
        print(f'  作为客户的项目数: {len(as_customer)}')
        for _, row in as_customer.iterrows():
            print(f'    项目: {row["项目编码"]} - 供应商: {row["成本侧供应商"]} - 场景: {row["一级场景"]}-{row["二级场景"]}')
        
        # 作为供应商的项目
        as_supplier = df[df['成本侧供应商'] == company]
        print(f'  作为供应商的项目数: {len(as_supplier)}')
        for _, row in as_supplier.iterrows():
            print(f'    项目: {row["项目编码"]} - 客户: {row["收入侧客户"]} - 场景: {row["一级场景"]}-{row["二级场景"]}')
        
        # 检查是否与其他重叠企业有直接交易关系
        direct_relations = []
        for other_company in overlap_companies:
            if other_company != company:
                # 检查company作为客户，other_company作为供应商
                relation1 = as_customer[as_customer['成本侧供应商'] == other_company]
                if len(relation1) > 0:
                    direct_relations.append(f'{company}(客户) → {other_company}(供应商): {len(relation1)}个项目')
                
                # 检查other_company作为客户，company作为供应商
                relation2 = as_supplier[as_supplier['收入侧客户'] == other_company]
                if len(relation2) > 0:
                    direct_relations.append(f'{other_company}(客户) → {company}(供应商): {len(relation2)}个项目')
        
        if direct_relations:
            print(f'  直接交易关系:')
            for relation in direct_relations:
                print(f'    {relation}')
        else:
            print(f'  无与其他重叠企业的直接交易关系')
    
    # 总结分析
    print('\\n=== 总结分析 ===')
    print('基于以上分析，可能的原因：')
    print('1. 这些企业虽然既是客户又是供应商，但它们之间没有直接的相互交易关系')
    print('2. 它们可能在不同的项目中扮演不同的角色，但不是与同一个企业进行相互交易')
    print('3. 真正的ABBA关系在当前数据中可能确实不存在或非常少见')
    
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f'错误: {e}')
