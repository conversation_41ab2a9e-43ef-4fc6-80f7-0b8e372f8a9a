#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Threads对话记录管理系统
功能：
1. 存储和管理对话记录
2. 按时间顺序输出对话记录为Markdown文件
3. 支持对话记录的增删改查
"""

import os
import json
import sqlite3
from datetime import datetime
from typing import List, Dict, Optional
import uuid

class ThreadsManager:
    """Threads对话记录管理器"""
    
    def __init__(self, db_path: str = "threads.db"):
        """
        初始化对话记录管理器
        
        Args:
            db_path: SQLite数据库文件路径
        """
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库表结构"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建对话记录表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS conversations (
            id TEXT PRIMARY KEY,
            thread_id TEXT NOT NULL,
            user_message TEXT,
            assistant_message TEXT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            metadata TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # 创建线程表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS threads (
            thread_id TEXT PRIMARY KEY,
            title TEXT,
            description TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_thread_id ON conversations(thread_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_timestamp ON conversations(timestamp)')
        
        conn.commit()
        conn.close()
    
    def create_thread(self, title: str = None, description: str = None) -> str:
        """
        创建新的对话线程
        
        Args:
            title: 线程标题
            description: 线程描述
            
        Returns:
            thread_id: 新创建的线程ID
        """
        thread_id = str(uuid.uuid4())
        if not title:
            title = f"对话线程 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
        INSERT INTO threads (thread_id, title, description)
        VALUES (?, ?, ?)
        ''', (thread_id, title, description))
        
        conn.commit()
        conn.close()
        
        return thread_id
    
    def add_conversation(self, 
                        thread_id: str,
                        user_message: str = None,
                        assistant_message: str = None,
                        metadata: Dict = None) -> str:
        """
        添加对话记录
        
        Args:
            thread_id: 线程ID
            user_message: 用户消息
            assistant_message: 助手回复
            metadata: 元数据（JSON格式）
            
        Returns:
            conversation_id: 对话记录ID
        """
        conversation_id = str(uuid.uuid4())
        metadata_json = json.dumps(metadata, ensure_ascii=False) if metadata else None
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 检查线程是否存在，不存在则创建
        cursor.execute('SELECT thread_id FROM threads WHERE thread_id = ?', (thread_id,))
        if not cursor.fetchone():
            self.create_thread(f"自动创建线程 {thread_id[:8]}")
        
        cursor.execute('''
        INSERT INTO conversations (id, thread_id, user_message, assistant_message, metadata)
        VALUES (?, ?, ?, ?, ?)
        ''', (conversation_id, thread_id, user_message, assistant_message, metadata_json))
        
        # 更新线程的最后更新时间
        cursor.execute('''
        UPDATE threads SET updated_at = CURRENT_TIMESTAMP WHERE thread_id = ?
        ''', (thread_id,))
        
        conn.commit()
        conn.close()
        
        return conversation_id
    
    def get_conversations(self, 
                         thread_id: str = None,
                         start_date: str = None,
                         end_date: str = None,
                         limit: int = None) -> List[Dict]:
        """
        获取对话记录
        
        Args:
            thread_id: 线程ID（可选）
            start_date: 开始日期（可选）
            end_date: 结束日期（可选）
            limit: 限制返回数量（可选）
            
        Returns:
            对话记录列表
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        query = '''
        SELECT c.*, t.title as thread_title
        FROM conversations c
        LEFT JOIN threads t ON c.thread_id = t.thread_id
        WHERE 1=1
        '''
        params = []
        
        if thread_id:
            query += ' AND c.thread_id = ?'
            params.append(thread_id)
        
        if start_date:
            query += ' AND c.timestamp >= ?'
            params.append(start_date)
        
        if end_date:
            query += ' AND c.timestamp <= ?'
            params.append(end_date)
        
        query += ' ORDER BY c.timestamp ASC'
        
        if limit:
            query += ' LIMIT ?'
            params.append(limit)
        
        cursor.execute(query, params)
        rows = cursor.fetchall()
        
        # 转换为字典列表
        columns = [description[0] for description in cursor.description]
        conversations = []
        for row in rows:
            conv_dict = dict(zip(columns, row))
            if conv_dict['metadata']:
                conv_dict['metadata'] = json.loads(conv_dict['metadata'])
            conversations.append(conv_dict)
        
        conn.close()
        return conversations
    
    def get_threads(self) -> List[Dict]:
        """获取所有线程列表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT t.*, COUNT(c.id) as conversation_count
        FROM threads t
        LEFT JOIN conversations c ON t.thread_id = c.thread_id
        GROUP BY t.thread_id
        ORDER BY t.updated_at DESC
        ''')
        
        rows = cursor.fetchall()
        columns = [description[0] for description in cursor.description]
        threads = [dict(zip(columns, row)) for row in rows]
        
        conn.close()
        return threads
    
    def export_to_markdown(self, 
                          output_file: str = None,
                          thread_id: str = None,
                          start_date: str = None,
                          end_date: str = None) -> str:
        """
        将对话记录导出为Markdown文件
        
        Args:
            output_file: 输出文件路径
            thread_id: 线程ID（可选）
            start_date: 开始日期（可选）
            end_date: 结束日期（可选）
            
        Returns:
            输出文件路径
        """
        if not output_file:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"threads_export_{timestamp}.md"
        
        conversations = self.get_conversations(thread_id, start_date, end_date)
        
        # 生成Markdown内容
        md_content = []
        md_content.append("# Threads对话记录")
        md_content.append("")
        md_content.append(f"**导出时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        md_content.append(f"**记录数量**: {len(conversations)}条")
        
        if thread_id:
            md_content.append(f"**线程ID**: {thread_id}")
        if start_date:
            md_content.append(f"**开始时间**: {start_date}")
        if end_date:
            md_content.append(f"**结束时间**: {end_date}")
        
        md_content.append("")
        md_content.append("---")
        md_content.append("")
        
        # 按线程分组
        current_thread = None
        for conv in conversations:
            if conv['thread_id'] != current_thread:
                current_thread = conv['thread_id']
                thread_title = conv['thread_title'] or f"线程 {current_thread[:8]}"
                md_content.append(f"## 🧵 {thread_title}")
                md_content.append("")
                md_content.append(f"**线程ID**: `{current_thread}`")
                md_content.append("")
            
            # 添加时间戳
            timestamp = conv['timestamp']
            md_content.append(f"### 📅 {timestamp}")
            md_content.append("")
            
            # 添加用户消息
            if conv['user_message']:
                md_content.append("**👤 用户**:")
                md_content.append("")
                md_content.append(conv['user_message'])
                md_content.append("")
            
            # 添加助手回复
            if conv['assistant_message']:
                md_content.append("**🤖 助手**:")
                md_content.append("")
                md_content.append(conv['assistant_message'])
                md_content.append("")
            
            # 添加元数据
            if conv['metadata']:
                md_content.append("**📋 元数据**:")
                md_content.append("```json")
                md_content.append(json.dumps(conv['metadata'], ensure_ascii=False, indent=2))
                md_content.append("```")
                md_content.append("")
            
            md_content.append("---")
            md_content.append("")
        
        # 写入文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(md_content))
        
        print(f"[成功] 对话记录已导出到: {output_file}")
        return output_file

def main():
    """主函数 - 演示用法"""
    manager = ThreadsManager()
    
    # 创建示例数据
    thread1 = manager.create_thread("项目讨论", "关于dict爬虫项目的技术讨论")
    thread2 = manager.create_thread("数据分析", "数据字典和表结构分析")
    
    # 添加示例对话
    manager.add_conversation(
        thread1,
        user_message="你好，我需要整理项目的数据字典",
        assistant_message="好的，我来帮你整理项目中的数据库表结构和字段定义。",
        metadata={"type": "greeting", "topic": "data_dictionary"}
    )
    
    manager.add_conversation(
        thread1,
        user_message="请分析sign_data_detail表的结构",
        assistant_message="sign_data_detail表是签约项目明细表，包含项目编码、项目名称、客户信息等字段...",
        metadata={"type": "analysis", "table": "sign_data_detail"}
    )
    
    manager.add_conversation(
        thread2,
        user_message="如何按时间顺序输出对话记录？",
        assistant_message="可以使用ThreadsManager的export_to_markdown方法，它会按时间顺序生成MD文件。",
        metadata={"type": "question", "topic": "export"}
    )
    
    # 导出所有对话记录
    output_file = manager.export_to_markdown()
    print(f"对话记录已导出到: {output_file}")
    
    # 导出特定线程的对话记录
    thread_file = manager.export_to_markdown(f"thread_{thread1[:8]}.md", thread_id=thread1)
    print(f"线程对话记录已导出到: {thread_file}")

if __name__ == "__main__":
    main()
