-- 创建综合查询项目计划及实施情况表
-- 对应接口：saleCenterApp//preparation/queryProjectPlanWithImplement

DROP TABLE IF EXISTS `dict_zonghe_queryProjectPlanWithImplement`;

CREATE TABLE IF NOT EXISTS `dict_zonghe_queryProjectPlanWithImplement` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  
  -- 入参字段（来源于源数据表）
  `INPUT_LOGIN_NO` varchar(100) DEFAULT NULL COMMENT '入参-登录用户名',
  `INPUT_PROJECT_ID` varchar(100) DEFAULT NULL COMMENT '入参-项目ID（来源：v_distinct_project_id视图或sign_data_detail表项目编码字段）',
  
  -- 响应数据字段（基于接口返回的JSON结构）
  `IMPLEMENT_PLAN_ID` varchar(100) DEFAULT NULL COMMENT '实施计划ID',
  `PLAN_ID` varchar(100) DEFAULT NULL COMMENT '计划ID',
  `PLAN_TYPE` varchar(50) DEFAULT NULL COMMENT '计划类型',
  `PLAN_NAME` text DEFAULT NULL COMMENT '计划名称',
  `PLAN_DESC` text DEFAULT NULL COMMENT '计划描述',
  `PLAN_DEAL_STAFF` varchar(100) DEFAULT NULL COMMENT '计划处理员工',
  `PLAN_DEAL_NAME` varchar(200) DEFAULT NULL COMMENT '计划处理员工姓名',
  `REQUIRE_START_DATE` varchar(50) DEFAULT NULL COMMENT '要求开始日期',
  `REQUIRE_FINISH_DATE` varchar(50) DEFAULT NULL COMMENT '要求完成日期',
  `IMPLEMENT_STATUS` varchar(50) DEFAULT NULL COMMENT '实施状态',
  `IMPLEMENT_STATUS_DESC` varchar(200) DEFAULT NULL COMMENT '实施状态描述',
  `ACT_FINSH_TIME` varchar(50) DEFAULT NULL COMMENT '实际完成时间',
  `PROGRESS_RATE` varchar(20) DEFAULT NULL COMMENT '进度比例',
  `DELIVERY_TYPE` varchar(50) DEFAULT NULL COMMENT '交付类型',
  `DELIVERY_TYPE_DESC` varchar(200) DEFAULT NULL COMMENT '交付类型描述',
  `CREATE_DATE` varchar(50) DEFAULT NULL COMMENT '创建日期',
  `PLAN_TYPE_DESC` varchar(200) DEFAULT NULL COMMENT '计划类型描述',
  `PLAN_LEVEL` varchar(20) DEFAULT NULL COMMENT '计划级别',
  `PARENT_IMPLEMENT_PLAN_ID` varchar(100) DEFAULT NULL COMMENT '父实施计划ID',
  `STAFF_TYPE` varchar(20) DEFAULT NULL COMMENT '员工类型',
  `PLAN_MODE` varchar(50) DEFAULT NULL COMMENT '计划模式',
  `SUBJECT_TYPE` varchar(50) DEFAULT NULL COMMENT '主题类型',
  `SUBJECT_TYPE_DESC` varchar(200) DEFAULT NULL COMMENT '主题类型描述',
  
  -- 系统字段
  `RUN_IP` varchar(50) DEFAULT NULL COMMENT '运行IP',
  `REQUEST_ID` varchar(100) DEFAULT NULL COMMENT '请求ID',
  `RETURN_CODE` varchar(10) DEFAULT NULL COMMENT '返回码',
  `RETURN_MSG` varchar(200) DEFAULT NULL COMMENT '返回消息',
  `USER_MSG` varchar(200) DEFAULT NULL COMMENT '用户消息',
  `DETAIL_MSG` text DEFAULT NULL COMMENT '详细消息',
  
  -- 元数据字段
  `import_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_input_project_id` (`INPUT_PROJECT_ID`),
  KEY `idx_implement_plan_id` (`IMPLEMENT_PLAN_ID`),
  KEY `idx_plan_id` (`PLAN_ID`),
  KEY `idx_plan_type` (`PLAN_TYPE`),
  KEY `idx_implement_status` (`IMPLEMENT_STATUS`),
  KEY `idx_import_time` (`import_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='综合查询项目计划及实施情况表';
