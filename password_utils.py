#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
密码加密工具模块
用于在其他脚本中导入使用

使用示例:
    from password_utils import encrypt_password
    
    encrypted = encrypt_password("123456", "login_key_from_server")
    print(encrypted)
"""

import hashlib
import base64
from urllib.parse import quote
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5, DES
from Crypto.Util.Padding import pad


# RSA 公钥常量
RSA_PUBLIC_KEY = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvEr5to90Z5w5+vZ+TIJw
uNExLBuYBKgCvKZll85RAJmbwCMuDTBU18XB5RBerd6c/CUqnrYoxoRjaHGdpOIA
VsHOlxloR3s9Y9/0EUpjpKKfzSsLlUp9N5bHbsoImmJo5+3Hgxpquv/6MCdjTqZ8
P7Uwjdzg7XYMSeBZzEzJ2vIFctjUF4kQWX03ljclLHPpn0mZYQ4Ue5afMv/xuBj5
8BnKl3LLW2mTIMohNkfIqivNnWn3fF6/TJLaI0GMxDBese3QRaJWSDnjSRrMlRJF
NnvB5+AGoPHMfpekqde1t02Zj+MiwFupe/Pm4JkqC5TYEU+Dq7LIZ7LT4imafZR2
0QIDAQAB
-----END PUBLIC KEY-----"""

# 全局 RSA 加密器
_rsa_key = RSA.import_key(RSA_PUBLIC_KEY)
_rsa_cipher = PKCS1_v1_5.new(_rsa_key)


def md5_hash(text: str) -> str:
    """
    计算文本的 MD5 哈希值
    
    Args:
        text: 要哈希的文本
        
    Returns:
        MD5 哈希值（32位十六进制字符串）
    """
    return hashlib.md5(text.encode('utf-8')).hexdigest()


def rsa_encrypt(text: str) -> bytes:
    """
    使用 RSA 公钥加密文本

    Args:
        text: 要加密的文本

    Returns:
        RSA 加密后的字节数据
    """
    text_bytes = text.encode('utf-8')
    encrypted = _rsa_cipher.encrypt(text_bytes)
    return encrypted


def des_encrypt(data: bytes, key: str) -> str:
    """
    使用 DES 加密数据

    Args:
        data: 要加密的数据（字节）
        key: DES 密钥

    Returns:
        DES 加密后的 base64 字符串
    """
    # 确保密钥长度为 8 字节
    key_bytes = key.encode('utf-8')[:8].ljust(8, b'\x00')

    # 创建 DES 加密器（ECB 模式）
    cipher = DES.new(key_bytes, DES.MODE_ECB)

    # 对数据进行 PKCS7 填充
    padded_data = pad(data, DES.block_size)

    # 加密并返回 base64 编码
    encrypted = cipher.encrypt(padded_data)
    return base64.b64encode(encrypted).decode('utf-8')


def encrypt_password(password: str, login_key: str, debug: bool = False) -> str:
    """
    完整的密码加密函数
    对应 JavaScript 中的 password(k()(t.password), o) 函数

    注意：由于 RSA 加密的随机性，每次加密结果都会不同，这是正常的安全特性。

    Args:
        password: 用户输入的原始密码
        login_key: 从服务器获取的登录密钥
        debug: 是否打印调试信息

    Returns:
        加密后的密码字符串（URL 编码）
    """
    if debug:
        print(f"🔐 开始加密密码...")
        print(f"原始密码: {password}")
        print(f"登录密钥: {login_key}")

    # 第一步：MD5 哈希
    md5_result = md5_hash(password)
    if debug:
        print(f"1️⃣ MD5 哈希: {md5_result}")

    # 第二步：RSA 公钥加密（返回字节数据）
    rsa_result = rsa_encrypt(md5_result)
    if debug:
        print(f"2️⃣ RSA 加密长度: {len(rsa_result)} 字节")

    # 第三步：DES 加密（直接加密 RSA 字节数据）
    des_result = des_encrypt(rsa_result, login_key)
    if debug:
        print(f"3️⃣ DES 加密: {des_result[:50]}...")

    # 第四步：URL 编码
    final_result = quote(des_result)
    if debug:
        print(f"4️⃣ URL 编码: {final_result[:50]}...")
        print(f"✅ 加密完成，结果长度: {len(final_result)}")
        print(f"💡 注意：由于 RSA 随机性，每次结果都不同")

    return final_result


def test_encryption():
    """测试加密函数"""
    print("=== 密码加密测试 ===")
    
    test_cases = [
        ("123456", "testkey123"),
        ("admin", "loginkey456"),
        ("password", "secretkey789"),
    ]
    
    for password, login_key in test_cases:
        print(f"\n测试用例: 密码='{password}', 密钥='{login_key}'")
        result = encrypt_password(password, login_key, debug=True)
        print(f"最终结果: {result}")
        print("-" * 80)


if __name__ == "__main__":
    test_encryption()
