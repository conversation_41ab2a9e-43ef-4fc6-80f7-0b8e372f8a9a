-- 创建临时表存储要保留的最小id
CREATE TEMPORARY TABLE min_ids AS
SELECT MIN(id) as min_id
FROM dict_spider.sign_data_detail
GROUP BY 
    `签约上报日期`, `项目类型`, `项目编码`, `项目名称`, `项目主流程`, 
    `归属区县`, `是否跨区县分成`, `分成区县名称`, `分成占比`, `项目阶段`, 
    `项目进度`, `父项目编码`, `立项方式`, `商机编码`, `集团编码`, 
    `客户名称`, `项目状态`, `所属行业`, `一级场景`, `二级场景`, 
    `能力成熟度（系统测算）`, `能力成熟度（自评）`, `分公司/省政企室`, `项目经理`, 
    `项目创建时间`, `中标时间`, `竞标方式`, `合同类型`, `合同状态`, 
    `合同编码`, `签约时间`, `合同年限（年）`, `合同开始时间`, `合同结束时间`, 
    -- 其他所有字段，除了id和import_time
    `状态`;

-- 删除不在min_ids中的记录（即重复记录中id较大的记录）
DELETE FROM dict_spider.sign_data_detail
WHERE id NOT IN (SELECT min_id FROM min_ids);

-- 删除临时表
DROP TEMPORARY TABLE IF EXISTS min_ids;

-- 显示删除后的记录数
SELECT COUNT(*) AS remaining_records FROM dict_spider.sign_data_detail;