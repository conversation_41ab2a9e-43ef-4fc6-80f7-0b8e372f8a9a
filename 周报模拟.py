import requests
import json

url = "http://dict.gmcc.net:30722/gdyddict/selfReport/exportTableBeta"

payload = {
  "pageNumber": 1,
  "pageSize": 10,
  "repCode": "R1202305251232",
  "ids": "v47:v47:v49:v50:v53:v1:v3:v13:v14:v14:v16:v16:v11:v11:",
  "coltypesize": "151:152:2:8:8:3:3:3:151:152:151:152:151:152:",
  "os": "Win32",
  "bv": "5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "v471": "20250501",
  "v472": "20250511",
  "v49": "760",
  "v50": "",
  "v53": "",
  "v1": "",
  "v3": "",
  "v13": "",
  "v141": "",
  "v142": "",
  "v161": "",
  "v162": "",
  "v111": "",
  "v112": ""
}

headers = {
  'Host': "dict.gmcc.net:30722",
  'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  'Accept': "application/json, text/plain, */*",
  'Accept-Encoding': "gzip, deflate",
  'Content-Type': "application/json",
  'Pragma': "no-cache",
  'Cache-Control': "no-cache",
  'Origin': "http://dict.gmcc.net:30722",
  'Referer': "http://dict.gmcc.net:30722/analysis/selfReport?repcode=R1202305251232",
  'Accept-Language': "zh-CN,zh;q=0.9",
  'Cookie': "BSS-SESSION=OWMzZDE4ZDYtNmZjOC00NzhmLWIwNDktOWEyMDhlMzU1MDgy; isLogin=ImlzTG9naW4i; requestId=0b06cd00-2eec-11f0-be60-77c423123b30; systemUserCode=InpoZW5nZGV3ZW4i; jsession_id_4_boss=n92D26B966550FDEFD6718DE452E7E433-1; JSESSIONID=048950EFA4246190C7D9F60176B15787; userCode=zhengdewen; regionId=760; userName=6YOR5b635paH; provinceCode=100"
}

response = requests.post(url, data=json.dumps(payload), headers=headers)

print(response.text)