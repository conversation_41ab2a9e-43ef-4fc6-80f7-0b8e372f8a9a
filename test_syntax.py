#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试BC_update_jianzhen_status.py的语法
"""

try:
    import ast
    with open('BC_update_jianzhen_status.py', 'r', encoding='utf-8') as f:
        source_code = f.read()
    
    # 尝试解析语法
    ast.parse(source_code)
    print("✅ 语法检查通过")
    
except SyntaxError as e:
    print(f"❌ 语法错误: {e}")
    print(f"   行号: {e.lineno}")
    print(f"   位置: {e.offset}")
    print(f"   文本: {e.text}")
except Exception as e:
    print(f"❌ 其他错误: {e}")
