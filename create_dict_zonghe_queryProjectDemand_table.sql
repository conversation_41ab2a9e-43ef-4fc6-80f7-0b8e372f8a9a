-- 创建综合查询项目需求信息表
-- 对应接口：saleCenterApp/formulation/queryProjectDemand

DROP TABLE IF EXISTS `dict_zonghe_queryProjectDemand`;

CREATE TABLE IF NOT EXISTS `dict_zonghe_queryProjectDemand` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  
  -- 入参字段（来源于源数据表）
  `INPUT_LOGIN_NO` varchar(100) DEFAULT NULL COMMENT '入参-登录用户名',
  `INPUT_PROJECT_ID` varchar(100) DEFAULT NULL COMMENT '入参-项目ID（来源：v_distinct_project_id视图或sign_data_detail表项目编码字段）',
  
  -- 响应数据字段（基于接口返回的JSON结构）
  `SUPPORT_APPLICATION_TIME` varchar(50) DEFAULT NULL COMMENT '支撑申请时间',
  `REGION_CODE` varchar(50) DEFAULT NULL COMMENT '区域编码',
  `GOV_ENTER_CENTER` text DEFAULT NULL COMMENT '政企中心',
  `GRID_MEMBER` text DEFAULT NULL COMMENT '网格成员',
  `MAIN_PROCESS_TYPE` varchar(50) DEFAULT NULL COMMENT '主流程类型编码',
  `MAIN_PROCESS_TYPE_DESC` varchar(200) DEFAULT NULL COMMENT '主流程类型描述',
  `PROJECT_CONTRACT_TYPE` varchar(50) DEFAULT NULL COMMENT '项目合同类型编码',
  `PROJECT_CONTRACT_TYPE_NAME` varchar(200) DEFAULT NULL COMMENT '项目合同类型名称',
  `ELECTION_MODE` varchar(50) DEFAULT NULL COMMENT '选举模式编码',
  `ELECTION_MODE_NAME` varchar(200) DEFAULT NULL COMMENT '选举模式名称',
  `CONTRACT_PERIOD` varchar(50) DEFAULT NULL COMMENT '合同期限',
  `CONTRACT_DURATION` varchar(50) DEFAULT NULL COMMENT '合同持续时间',
  `IT_REQUIREMENTS_DESC` text DEFAULT NULL COMMENT 'IT需求描述',
  `CT_REQUIREMENTS_DESC` text DEFAULT NULL COMMENT 'CT需求描述',
  `IS_CONNECTED_OPP` varchar(10) DEFAULT NULL COMMENT '是否关联机会',
  `CUST_ID` varchar(100) DEFAULT NULL COMMENT '客户ID',
  `ESTIMATED_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT '预计金额',
  `PROJECT_NAME` text DEFAULT NULL COMMENT '项目名称',
  `REGION_CODE_DESC` varchar(200) DEFAULT NULL COMMENT '区域描述',
  `REQUIREMENTS_TITEL` text DEFAULT NULL COMMENT '需求标题',
  `ORG_DESC` text DEFAULT NULL COMMENT '组织描述',
  `SALE_OPP_ID` varchar(100) DEFAULT NULL COMMENT '销售机会ID',
  `THE_DEGREE_OF_URGENCY` varchar(50) DEFAULT NULL COMMENT '紧急程度',
  `BID_FLAG` varchar(10) DEFAULT NULL COMMENT '投标标志',
  `BID_FLAG_NAME` varchar(100) DEFAULT NULL COMMENT '投标标志名称',
  `YS_FLAG` varchar(50) DEFAULT NULL COMMENT 'YS标志',
  `GW_NAME` varchar(200) DEFAULT NULL COMMENT 'GW名称',
  `GW_NO` varchar(100) DEFAULT NULL COMMENT 'GW编号',
  `PROJECT_TYPE` varchar(50) DEFAULT NULL COMMENT '项目类型',
  `PRE_SOLUTION_FINISH_TIME` varchar(50) DEFAULT NULL COMMENT '预方案完成时间',
  `DISCLOSURE_START_TIME` varchar(50) DEFAULT NULL COMMENT '交底开始时间',
  `DISCLOSURE_FINISH_TIME` varchar(50) DEFAULT NULL COMMENT '交底完成时间',
  
  -- PM_PUBM_COUNTY_INFO_OUT 嵌套对象字段
  `PUBM_COUNTY_ID` varchar(100) DEFAULT NULL COMMENT '公司县ID',
  `PUBM_COUNTY_NAME` varchar(200) DEFAULT NULL COMMENT '公司县名称',
  `REGION_ID` varchar(100) DEFAULT NULL COMMENT '区域ID',
  `ONEDICT_REGION_ID` varchar(100) DEFAULT NULL COMMENT '一级字典区域ID',
  
  -- 系统字段
  `RUN_IP` varchar(50) DEFAULT NULL COMMENT '运行IP',
  `REQUEST_ID` varchar(100) DEFAULT NULL COMMENT '请求ID',
  `RETURN_CODE` varchar(10) DEFAULT NULL COMMENT '返回码',
  `RETURN_MSG` varchar(200) DEFAULT NULL COMMENT '返回消息',
  `USER_MSG` varchar(200) DEFAULT NULL COMMENT '用户消息',
  `DETAIL_MSG` text DEFAULT NULL COMMENT '详细消息',
  
  -- 元数据字段
  `import_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_id` (`INPUT_PROJECT_ID`),
  KEY `idx_project_name` (`PROJECT_NAME`(100)),
  KEY `idx_region_code` (`REGION_CODE`),
  KEY `idx_project_type` (`PROJECT_TYPE`),
  KEY `idx_main_process_type` (`MAIN_PROCESS_TYPE`),
  KEY `idx_estimated_amount` (`ESTIMATED_AMOUNT`),
  KEY `idx_import_time` (`import_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='综合查询项目需求信息表';

-- 创建索引优化查询性能
CREATE INDEX idx_cust_id ON dict_zonghe_queryProjectDemand(CUST_ID);
CREATE INDEX idx_sale_opp_id ON dict_zonghe_queryProjectDemand(SALE_OPP_ID);
CREATE INDEX idx_contract_type ON dict_zonghe_queryProjectDemand(PROJECT_CONTRACT_TYPE);
