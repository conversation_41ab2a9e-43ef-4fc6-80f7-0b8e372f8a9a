# 综合查询团队信息系统使用说明

## 📋 系统概述

本系统用于从dict系统获取项目团队成员信息，并将数据同步到MySQL数据库中，支持团队信息的全面管理和分析。

## 🗃️ 数据表结构

### dict_zonghe_queryTeamMember 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int AUTO_INCREMENT | 自增主键 |
| PROJECT_ID | varchar(100) | 项目ID |
| TEAM_TYPE | varchar(20) | 团队类型 |
| STAFF_NAME | varchar(100) | 员工姓名 |
| POST_NAME | varchar(100) | 岗位名称 |
| DEPT_NAME | varchar(200) | 部门名称 |
| PHONE | varchar(50) | 电话号码 |
| JOIN_TIME | varchar(50) | 加入时间 |
| STAFF_ID | varchar(100) | 员工ID |
| MEMBER_ID | varchar(100) | 成员ID |
| DEPT_ID | varchar(100) | 部门ID |
| POST_ID | varchar(100) | 岗位ID |
| STAFF_TYPE | varchar(20) | 员工类型 |
| IS_CHIEF | varchar(10) | 是否负责人 |
| CONTRIBUTE_RATIO | varchar(20) | 贡献比例 |
| import_time | timestamp | 导入时间 |

## 📁 文件说明

### 核心文件

1. **create_dict_zonghe_queryTeamMember_table.sql** - 数据表创建脚本
2. **dict_zonghe_queryTeamMember.py** - 主程序，负责数据获取和入库
3. **check_team_data.py** - 数据验证脚本
4. **login2cookie.py** - Cookie更新脚本

## 🚀 使用步骤

### 第一步：创建数据表
```bash
# 执行SQL脚本创建数据表
python -c "import pymysql; conn = pymysql.connect(host='127.0.0.1', port=3306, user='root', password='cmcc12345', database='dict_spider', charset='utf8mb4'); cursor = conn.cursor(); cursor.execute(open('create_dict_zonghe_queryTeamMember_table.sql', 'r', encoding='utf-8').read()); conn.commit(); print('数据表创建成功'); cursor.close(); conn.close()"
```

### 第二步：更新Cookie（如需要）
```bash
# 如果遇到401认证错误，运行此命令更新Cookie
python login2cookie.py
```

### 第三步：运行数据同步
```bash
# 执行主程序，开始数据同步
python dict_zonghe_queryTeamMember.py
```

### 第四步：验证数据
```bash
# 检查数据同步结果
python check_team_data.py
```

## 🔧 配置说明

### 数据库配置
```python
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'cmcc12345',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}
```

### API配置
- **接口地址**: `http://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/formulation/queryTeamMember`
- **请求方式**: POST
- **数据格式**: JSON

## 📊 数据来源

程序会自动从以下数据源获取项目ID：
1. **优先**: `v_distinct_project_id` 视图
2. **备用**: `sign_data_detail` 表的项目编码字段

## ⚠️ 注意事项

1. **Cookie有效性**: 如果遇到401错误，需要运行 `login2cookie.py` 更新Cookie
2. **网络稳定性**: 程序会轮询所有项目，建议在网络稳定的环境下运行
3. **数据清理**: 每次运行会清空原有数据，重新同步全量数据
4. **运行时间**: 根据项目数量，完整同步可能需要较长时间

## 📈 运行监控

程序运行时会显示详细的进度信息：
- 当前处理的项目编号和总数
- 每个项目获取的团队成员数量
- 数据保存状态
- 最终统计结果

## 🔍 数据查询示例

```sql
-- 查询总记录数
SELECT COUNT(*) FROM dict_zonghe_queryTeamMember;

-- 查询项目数量
SELECT COUNT(DISTINCT PROJECT_ID) FROM dict_zonghe_queryTeamMember;

-- 查询某个项目的团队成员
SELECT * FROM dict_zonghe_queryTeamMember WHERE PROJECT_ID = 'CMGDZSICT20250407008';

-- 按部门统计团队成员数量
SELECT DEPT_NAME, COUNT(*) as member_count 
FROM dict_zonghe_queryTeamMember 
GROUP BY DEPT_NAME 
ORDER BY member_count DESC;

-- 查询负责人信息
SELECT PROJECT_ID, STAFF_NAME, POST_NAME, DEPT_NAME 
FROM dict_zonghe_queryTeamMember 
WHERE IS_CHIEF = '1';
```

## 🛠️ 故障排除

### 常见问题

1. **401认证错误**
   - 解决方案：运行 `python login2cookie.py` 更新Cookie

2. **数据库连接失败**
   - 检查数据库配置是否正确
   - 确认数据库服务是否启动

3. **项目ID获取失败**
   - 检查 `v_distinct_project_id` 视图是否存在
   - 确认 `sign_data_detail` 表是否有数据

4. **网络超时**
   - 检查网络连接
   - 适当增加请求超时时间

## 📞 技术支持

如遇到问题，请检查：
1. 数据库连接配置
2. Cookie文件是否存在且有效
3. 网络连接是否正常
4. 相关数据表是否存在

---

**版本**: 1.0  
**更新日期**: 2025-07-14  
**开发者**: RIPER AI Assistant
