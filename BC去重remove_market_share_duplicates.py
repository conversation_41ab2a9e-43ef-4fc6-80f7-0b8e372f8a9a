import pymysql
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'cmcc12345',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

def connect_to_database():
    """连接到MySQL数据库"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        print(f"[成功] 已连接到数据库: {DB_CONFIG['database']}")
        return conn
    except Exception as e:
        print(f"[错误] 连接数据库失败: {e}")
        exit(1)

def remove_duplicates_from_market_share_daily(conn):
    """从market_share_daily表中删除重复记录"""
    cursor = conn.cursor()
    try:
        print("[信息] 开始处理market_share_daily表的重复数据...")
        
        # 获取总记录数
        cursor.execute("SELECT COUNT(*) FROM market_share_daily")
        total_records = cursor.fetchone()[0]
        print(f"[信息] market_share_daily表当前共有 {total_records} 条记录")
        
        # 创建临时表存储要保留的最小id
        cursor.execute("""
        CREATE TEMPORARY TABLE min_ids_daily AS
        SELECT MIN(id) as min_id
        FROM market_share_daily
        GROUP BY 
            `日期`, `集团名称`, `总证件`, `属于拍照`, `属于非拍照新流入`, 
            `拍照时中山移动`, `截止当日在网`, `截止当日流失`, `拍照后新增`, 
            `其中属于XR`, `其中客经`, `其中网格经理`, `其中同一网格渠道`, 
            `其他`, `当日到达`, `拍照宽带`, `当日宽带`, `宽带流失`, 
            `宽带新增`, `宽带到达`
        """)
        
        # 获取唯一记录数
        cursor.execute("SELECT COUNT(*) FROM min_ids_daily")
        unique_records = cursor.fetchone()[0]
        duplicate_records = total_records - unique_records
        
        print(f"[信息] 发现 {duplicate_records} 条重复记录")
        
        if duplicate_records > 0:
            # 删除重复记录
            cursor.execute("""
            DELETE FROM market_share_daily
            WHERE id NOT IN (SELECT min_id FROM min_ids_daily)
            """)
            conn.commit()
            print(f"[成功] 已删除 {duplicate_records} 条重复记录")
        else:
            print("[信息] 没有发现重复记录")
        
        # 删除临时表
        cursor.execute("DROP TEMPORARY TABLE IF EXISTS min_ids_daily")
        
        return duplicate_records
    except Exception as e:
        conn.rollback()
        print(f"[错误] 处理market_share_daily表重复数据失败: {e}")
        import traceback
        print(traceback.format_exc())
        return 0
    finally:
        cursor.close()

def remove_duplicates_from_market_share_detail(conn):
    """从market_share_detail表中删除重复记录"""
    cursor = conn.cursor()
    try:
        print("[信息] 开始处理market_share_detail表的重复数据...")
        
        # 获取总记录数
        cursor.execute("SELECT COUNT(*) FROM market_share_detail")
        total_records = cursor.fetchone()[0]
        print(f"[信息] market_share_detail表当前共有 {total_records} 条记录")
        
        # 创建临时表存储要保留的最小id
        cursor.execute("""
        CREATE TEMPORARY TABLE min_ids_detail AS
        SELECT MIN(id) as min_id
        FROM market_share_detail
        GROUP BY 
            `更新日期`, `号码`, `集团名称`, `新入网归属类型`, `工号`, 
            `渠道编码`, `渠道名称`, `分公司`, `网格`, `是否XR`, 
            `是否有宽带`, `是否有高质量合约`
        """)
        
        # 获取唯一记录数
        cursor.execute("SELECT COUNT(*) FROM min_ids_detail")
        unique_records = cursor.fetchone()[0]
        duplicate_records = total_records - unique_records
        
        print(f"[信息] 发现 {duplicate_records} 条重复记录")
        
        if duplicate_records > 0:
            # 删除重复记录
            cursor.execute("""
            DELETE FROM market_share_detail
            WHERE id NOT IN (SELECT min_id FROM min_ids_detail)
            """)
            conn.commit()
            print(f"[成功] 已删除 {duplicate_records} 条重复记录")
        else:
            print("[信息] 没有发现重复记录")
        
        # 删除临时表
        cursor.execute("DROP TEMPORARY TABLE IF EXISTS min_ids_detail")
        
        return duplicate_records
    except Exception as e:
        conn.rollback()
        print(f"[错误] 处理market_share_detail表重复数据失败: {e}")
        import traceback
        print(traceback.format_exc())
        return 0
    finally:
        cursor.close()

def log_cleanup_operation(conn, daily_removed, detail_removed):
    """记录清理操作到日志表"""
    cursor = conn.cursor()
    try:
        # 检查日志表是否存在，不存在则创建
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS cleanup_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            table_name VARCHAR(50),
            records_removed INT,
            cleanup_time DATETIME
        )
        """)
        
        # 记录清理操作
        cleanup_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 记录market_share_daily表清理操作
        if daily_removed > 0:
            cursor.execute("""
            INSERT INTO cleanup_logs (table_name, records_removed, cleanup_time)
            VALUES (%s, %s, %s)
            """, ('market_share_daily', daily_removed, cleanup_time))
        
        # 记录market_share_detail表清理操作
        if detail_removed > 0:
            cursor.execute("""
            INSERT INTO cleanup_logs (table_name, records_removed, cleanup_time)
            VALUES (%s, %s, %s)
            """, ('market_share_detail', detail_removed, cleanup_time))
        
        conn.commit()
        print("[成功] 已记录清理操作到日志表")
    except Exception as e:
        conn.rollback()
        print(f"[警告] 记录清理操作失败: {e}")
    finally:
        cursor.close()

def main():
    print(f"🧹 开始清理市场份额数据表中的重复记录 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 连接数据库
    conn = connect_to_database()
    
    # 从market_share_daily表中删除重复记录
    daily_removed = remove_duplicates_from_market_share_daily(conn)
    
    # 从market_share_detail表中删除重复记录
    detail_removed = remove_duplicates_from_market_share_detail(conn)
    
    # 记录清理操作
    log_cleanup_operation(conn, daily_removed, detail_removed)
    
    # 关闭数据库连接
    conn.close()
    
    total_removed = daily_removed + detail_removed
    if total_removed > 0:
        print(f"✅ 清理完成，共删除 {total_removed} 条重复记录")
    else:
        print("✅ 清理完成，没有发现重复记录")

if __name__ == "__main__":
    main()