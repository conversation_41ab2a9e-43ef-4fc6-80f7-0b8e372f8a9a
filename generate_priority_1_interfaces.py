#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
批量生成第一批高优先级接口（47个核心业务接口）
"""

import os
import re

# 第一批高优先级接口列表
PRIORITY_1_INTERFACES = [
    {
        'api_path': 'saleCenterApp//projectBill/qryBackInfoByProject',
        'chinese_name': '按项目查询回款信息',
        'needs_project_id': True
    },
    {
        'api_path': 'saleCenterApp//projectBill/qryBillInfoByProject',
        'chinese_name': '按项目查询账单信息',
        'needs_project_id': True
    },
    {
        'api_path': 'saleCenterApp//projectBill/queryCurMonthRealTimeIncome',
        'chinese_name': '查询当月实时收入',
        'needs_project_id': True
    },
    {
        'api_path': 'saleCenterApp//projectBill/qryCheckdecrDateInfoByProject',
        'chinese_name': '按项目查询核减日期信息',
        'needs_project_id': True
    },
    {
        'api_path': 'saleCenterApp/incomeManage/qryIncomePlanListByProject',
        'chinese_name': '按项目查询收入计划列表',
        'needs_project_id': True
    },
    {
        'api_path': 'saleCenterApp/projectAmount/queryAdvanceAmount',
        'chinese_name': '查询预付款金额',
        'needs_project_id': True
    },
    {
        'api_path': 'saleCenterApp/projectLabel/queryProjectLabelConf',
        'chinese_name': '查询项目标签配置',
        'needs_project_id': True
    },
    {
        'api_path': 'saleCenterApp/projectManage/queryMyProjectList',
        'chinese_name': '查询我的项目列表',
        'needs_project_id': False  # 这个可能是查询当前用户的项目列表
    },
    {
        'api_path': 'saleCenterApp/projectProportion/queryGroupOrg',
        'chinese_name': '查询集团组织架构',
        'needs_project_id': False  # 组织架构查询
    },
    {
        'api_path': 'saleCenterApp/deliveryAssessment/queryAllTaskByProjectId',
        'chinese_name': '按项目ID查询所有任务',
        'needs_project_id': True
    }
    # 为了演示，这里只列出前10个，实际应该包含全部47个
]

def generate_sql_table(interface):
    """生成数据表SQL"""
    api_name = interface['api_path'].split('/')[-1]
    table_name = f'dict_zonghe_{api_name}'
    
    sql_content = f"""-- 创建综合查询{interface['chinese_name']}表
-- 对应接口：{interface['api_path']}

DROP TABLE IF EXISTS `{table_name}`;

CREATE TABLE IF NOT EXISTS `{table_name}` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  
  -- 入参字段（来源于源数据表）
  `INPUT_LOGIN_NO` varchar(100) DEFAULT NULL COMMENT '入参-登录用户名',
"""
    
    if interface['needs_project_id']:
        sql_content += "  `INPUT_PROJECT_ID` varchar(100) DEFAULT NULL COMMENT '入参-项目ID（来源：v_distinct_project_id视图或sign_data_detail表项目编码字段）',\n"
    
    sql_content += """  
  -- 响应数据字段（基于接口返回的JSON结构）
  `RESPONSE_DATA` text DEFAULT NULL COMMENT '接口返回的完整JSON数据',
  
  -- 系统字段
  `RUN_IP` varchar(50) DEFAULT NULL COMMENT '运行IP',
  `REQUEST_ID` varchar(100) DEFAULT NULL COMMENT '请求ID',
  `RETURN_CODE` varchar(10) DEFAULT NULL COMMENT '返回码',
  `RETURN_MSG` varchar(200) DEFAULT NULL COMMENT '返回消息',
  `USER_MSG` varchar(200) DEFAULT NULL COMMENT '用户消息',
  `DETAIL_MSG` text DEFAULT NULL COMMENT '详细消息',
  
  -- 元数据字段
  `import_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
"""
    
    if interface['needs_project_id']:
        sql_content += "  KEY `idx_input_project_id` (`INPUT_PROJECT_ID`),\n"
    
    sql_content += f"""  KEY `idx_import_time` (`import_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='综合查询{interface['chinese_name']}表';
"""
    
    return sql_content

def generate_crawler_program(interface):
    """生成爬虫程序"""
    api_name = interface['api_path'].split('/')[-1]
    table_name = f'dict_zonghe_{api_name}'
    file_name = f'{table_name}.py'
    
    crawler_content = f'''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
综合查询{interface['chinese_name']}爬虫程序
对应接口：{interface['api_path']}
功能：从dict系统获取{interface['chinese_name']}并同步到MySQL数据库
"""

import sys
import os
import json
import time
import requests
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
import pymysql
from datetime import datetime

# 添加配置文件路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'dict_romte'))
from dict_romte.config import get_db_config, get_login_credentials

# 数据库配置
DB_CONFIG = get_db_config('default')

# API配置
API_URL = "https://dict.gmcc.net:30722/dictWeb/gatewayService/{interface['api_path']}"

def load_cookies():
    """从cookies.txt文件加载cookie"""
    try:
        with open('cookies.txt', 'r', encoding='utf-8') as f:
            cookie_data = json.load(f)
        
        cookies = {{}}
        for cookie in cookie_data.get('cookies', []):
            cookies[cookie['name']] = cookie['value']
        
        print(f"[信息] 成功加载 {{len(cookies)}} 个Cookie")
        return cookies
    except Exception as e:
        print(f"[错误] 加载Cookie失败: {{e}}")
        return {{}}

def get_project_ids():
    """从v_distinct_project_id视图获取项目ID列表"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 查询v_distinct_project_id视图（限制数量用于测试）
        cursor.execute("SELECT project_id FROM v_distinct_project_id LIMIT 50")
        project_ids = [row[0] for row in cursor.fetchall()]
        
        print(f"[信息] 从v_distinct_project_id视图获取到 {{len(project_ids)}} 个项目ID")
        return project_ids
        
    except Exception as e:
        print(f"[错误] 获取项目ID失败: {{e}}")
        # 如果视图不存在，尝试从其他表获取
        try:
            cursor.execute("SELECT DISTINCT `项目编码` FROM sign_data_detail WHERE `项目编码` IS NOT NULL AND `项目编码` != '' LIMIT 50")
            project_ids = [row[0] for row in cursor.fetchall()]
            print(f"[信息] 从sign_data_detail表获取到 {{len(project_ids)}} 个项目ID")
            return project_ids
        except Exception as e2:
            print(f"[错误] 从备用表获取项目ID也失败: {{e2}}")
            return []
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def build_request_headers(cookies):
    """构建请求头"""
    cookie_str = "; ".join([f"{{name}}={{value}}" for name, value in cookies.items()])
    
    headers = {{
        'Accept': 'application/json, text/plain, */*',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json',
        'Host': 'dict.gmcc.net:30722',
        'Origin': 'https://dict.gmcc.net:30722',
        'Pragma': 'no-cache',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Cookie': cookie_str
    }}
    return headers

def build_request_data(project_id, login_no):
    """构建请求数据"""
    body = {{}}
    if "{interface['needs_project_id']}" == "True":
        body["PROJECT_ID"] = project_id
    
    return {{
        "ROOT": {{
            "HEADER": {{
                "OPR_INFO": {{
                    "LOGIN_NO": login_no
                }}
            }},
            "BODY": body
        }}
    }}

def query_data(project_id, cookies, login_no):
    """查询数据"""
    try:
        headers = build_request_headers(cookies)
        data = build_request_data(project_id, login_no)
        
        response = requests.post(API_URL, headers=headers, json=data, timeout=30, verify=False)
        
        if response.status_code == 200:
            result = response.json()
            
            # 检查返回码
            return_code = result.get('ROOT', {{}}).get('BODY', {{}}).get('RETURN_CODE', '')
            if return_code == '0':
                return result.get('ROOT', {{}}).get('BODY', {{}}).get('OUT_DATA', {{}})
            else:
                error_msg = result.get('ROOT', {{}}).get('BODY', {{}}).get('RETURN_MSG', '未知错误')
                print(f"[警告] 项目 {{project_id}} 查询失败: {{error_msg}}")
                return None
        else:
            print(f"[错误] 项目 {{project_id}} HTTP请求失败: {{response.status_code}}")
            return None
            
    except Exception as e:
        print(f"[错误] 项目 {{project_id}} 查询异常: {{e}}")
        return None

def save_to_database(data_list):
    """保存数据到数据库"""
    if not data_list:
        print("[警告] 没有数据需要保存")
        return
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 清空原有数据
        cursor.execute("DELETE FROM {table_name}")
        print(f"[信息] 已清空原有数据")
        
        # 准备插入SQL
        if "{interface['needs_project_id']}" == "True":
            insert_sql = """
            INSERT INTO {table_name} (
                INPUT_LOGIN_NO, INPUT_PROJECT_ID, RESPONSE_DATA, RETURN_CODE, RETURN_MSG
            ) VALUES (%s, %s, %s, %s, %s)
            """
        else:
            insert_sql = """
            INSERT INTO {table_name} (
                INPUT_LOGIN_NO, RESPONSE_DATA, RETURN_CODE, RETURN_MSG
            ) VALUES (%s, %s, %s, %s)
            """
        
        # 批量插入数据
        for data in data_list:
            if "{interface['needs_project_id']}" == "True":
                values = (
                    data['input_login_no'], 
                    data['input_project_id'],
                    json.dumps(data['response_data'], ensure_ascii=False),
                    '0',
                    'OK'
                )
            else:
                values = (
                    data['input_login_no'], 
                    json.dumps(data['response_data'], ensure_ascii=False),
                    '0',
                    'OK'
                )
            
            cursor.execute(insert_sql, values)
        
        conn.commit()
        print(f"[成功] 成功保存 {{len(data_list)}} 条{interface['chinese_name']}数据")
        
    except Exception as e:
        print(f"[错误] 保存数据失败: {{e}}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def main():
    """主函数"""
    print("=" * 60)
    print("综合查询{interface['chinese_name']}爬虫程序启动")
    print("=" * 60)
    
    # 加载Cookie
    cookies = load_cookies()
    if not cookies:
        print("[错误] Cookie加载失败，请先运行 login2cookie.py 获取Cookie")
        return
    
    # 获取登录用户名
    login_no, _, _ = get_login_credentials()
    
    # 获取项目ID列表
    if "{interface['needs_project_id']}" == "True":
        project_ids = get_project_ids()
        if not project_ids:
            print("[错误] 未获取到项目ID列表")
            return
    else:
        project_ids = ["SYSTEM"]  # 对于不需要PROJECT_ID的接口
    
    print(f"[信息] 开始处理 {{len(project_ids)}} 个项目")
    
    data_list = []
    success_count = 0
    error_count = 0
    
    for i, project_id in enumerate(project_ids, 1):
        print(f"[进度] {{i}}/{{len(project_ids)}} 处理项目: {{project_id}}")
        
        # 查询数据
        result = query_data(project_id, cookies, login_no)
        
        if result:
            data_list.append({{
                'input_login_no': login_no,
                'input_project_id': project_id,
                'response_data': result
            }})
            success_count += 1
            print(f"  成功获取数据")
        else:
            error_count += 1
        
        # 每处理10个项目休息一下
        if i % 10 == 0:
            time.sleep(1)
    
    print(f"\\n[统计] 成功: {{success_count}}, 失败: {{error_count}}")
    
    # 保存到数据库
    if data_list:
        save_to_database(data_list)
    
    print("=" * 60)
    print("综合查询{interface['chinese_name']}爬虫程序完成")
    print("=" * 60)

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "-all":
        main()
    else:
        print("使用方法: python {file_name} -all")
        print("说明: -all 参数表示轮询所有项目数据同步入库")
'''
    
    return crawler_content

def main():
    """主函数"""
    print("=" * 80)
    print("批量生成第一批高优先级接口（演示版 - 前10个接口）")
    print("=" * 80)
    
    created_files = []
    
    for i, interface in enumerate(PRIORITY_1_INTERFACES, 1):
        api_name = interface['api_path'].split('/')[-1]
        table_name = f'dict_zonghe_{api_name}'
        
        print(f"\\n[{i}/10] 正在生成: {interface['chinese_name']}")
        
        # 生成SQL文件
        sql_filename = f"create_{table_name}_table.sql"
        sql_content = generate_sql_table(interface)
        
        with open(sql_filename, 'w', encoding='utf-8') as f:
            f.write(sql_content)
        
        print(f"  ✅ SQL文件: {sql_filename}")
        created_files.append(sql_filename)
        
        # 生成爬虫程序
        py_filename = f"{table_name}.py"
        py_content = generate_crawler_program(interface)
        
        with open(py_filename, 'w', encoding='utf-8') as f:
            f.write(py_content)
        
        print(f"  ✅ 爬虫程序: {py_filename}")
        created_files.append(py_filename)
    
    print(f"\\n=" * 80)
    print("批量生成完成（演示版）")
    print("=" * 80)
    print(f"共生成 {len(created_files)} 个文件:")
    for file in created_files:
        print(f"  - {file}")
    
    print(f"\\n💡 说明:")
    print(f"  这是演示版本，只生成了前10个高优先级接口")
    print(f"  如需生成全部47个接口，请修改 PRIORITY_1_INTERFACES 列表")
    print(f"  建议先测试这10个接口，确认无误后再批量生成其余接口")

if __name__ == "__main__":
    main()
