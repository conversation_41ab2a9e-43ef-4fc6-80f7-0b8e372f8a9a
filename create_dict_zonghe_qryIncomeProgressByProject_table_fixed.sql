-- 创建综合查询按项目查询收入进度表
-- 对应接口：saleCenterApp/incomeManage/qryIncomeProgressByProject

DROP TABLE IF EXISTS `dict_zonghe_qryIncomeProgressByProject`;

CREATE TABLE IF NOT EXISTS `dict_zonghe_qryIncomeProgressByProject` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  
  -- 入参字段（来源于源数据表）
  `INPUT_LOGIN_NO` varchar(100) DEFAULT NULL COMMENT '入参-登录用户名',
  `INPUT_PROJECT_ID` varchar(100) DEFAULT NULL COMMENT '入参-项目ID（来源：v_distinct_project_id视图或sign_data_detail表项目编码字段）',
  `INPUT_PROJECT_STAGE` varchar(50) DEFAULT NULL COMMENT '入参-项目阶段',
  
  -- 响应数据字段（基于接口返回的JSON结构）
  `EXPENSE_DETAIL_ID` varchar(100) DEFAULT NULL COMMENT '费用明细ID',
  `EXPENSE_ID` varchar(100) DEFAULT NULL COMMENT '费用ID',
  `PROD_ID` varchar(100) DEFAULT NULL COMMENT '产品ID',
  `PROD_NAME` varchar(200) DEFAULT NULL COMMENT '产品名称',
  `CT_OR_IT` varchar(20) DEFAULT NULL COMMENT 'CT或IT',
  `SUBJECT_CODE` varchar(100) DEFAULT NULL COMMENT '科目编码',
  `SUBJECT_NAME` varchar(200) DEFAULT NULL COMMENT '科目名称',
  `SUBJECT_CLASS` varchar(50) DEFAULT NULL COMMENT '科目类别',
  `IS_FLAT_RATE` varchar(10) DEFAULT NULL COMMENT '是否固定费率',
  `VAT_RATE` decimal(5,2) DEFAULT NULL COMMENT '增值税率',
  `CYCLE_TYPE` varchar(50) DEFAULT NULL COMMENT '周期类型',
  `CYCLE_COUNT` int DEFAULT NULL COMMENT '周期数量',
  `COLL_CYCLE_TYPE` varchar(50) DEFAULT NULL COMMENT '收款周期类型',
  `COLL_CYCLE_COUNT` int DEFAULT NULL COMMENT '收款周期数量',
  `MONEY` decimal(15,2) DEFAULT NULL COMMENT '金额',
  `VAT` decimal(15,2) DEFAULT NULL COMMENT '增值税',
  `MONEY_EX_TAX` decimal(15,2) DEFAULT NULL COMMENT '不含税金额',
  `PLAN_BILL_START_TIME` varchar(50) DEFAULT NULL COMMENT '计划开票开始时间',
  `PLAN_COLL_START_TIME` varchar(50) DEFAULT NULL COMMENT '计划收款开始时间',
  `ACT_BILL_START_TIME` varchar(50) DEFAULT NULL COMMENT '实际开票开始时间',
  `BILL_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT '开票金额',
  `BILL_AMOUNT_TOTAL_VAT` decimal(15,2) DEFAULT NULL COMMENT '开票总含税金额',
  `COLLECTION_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT '收款金额',
  
  -- 系统字段
  `RUN_IP` varchar(50) DEFAULT NULL COMMENT '运行IP',
  `REQUEST_ID` varchar(100) DEFAULT NULL COMMENT '请求ID',
  `RETURN_CODE` varchar(10) DEFAULT NULL COMMENT '返回码',
  `RETURN_MSG` varchar(200) DEFAULT NULL COMMENT '返回消息',
  `USER_MSG` varchar(200) DEFAULT NULL COMMENT '用户消息',
  `DETAIL_MSG` text DEFAULT NULL COMMENT '详细消息',
  
  -- 元数据字段
  `import_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_input_project_id` (`INPUT_PROJECT_ID`),
  KEY `idx_expense_detail_id` (`EXPENSE_DETAIL_ID`),
  KEY `idx_expense_id` (`EXPENSE_ID`),
  KEY `idx_prod_id` (`PROD_ID`),
  KEY `idx_subject_code` (`SUBJECT_CODE`),
  KEY `idx_import_time` (`import_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='综合查询按项目查询收入进度表';
