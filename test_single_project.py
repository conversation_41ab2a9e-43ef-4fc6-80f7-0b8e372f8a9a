#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试单个项目的爬虫功能
"""

import sys
import os
import json
import requests
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
sys.path.append(os.path.join(os.path.dirname(__file__), 'dict_romte'))
from dict_romte.config import get_db_config, get_login_credentials
import pymysql

def load_cookies():
    """从cookies.txt文件加载cookie"""
    try:
        with open('cookies.txt', 'r', encoding='utf-8') as f:
            cookie_data = json.load(f)
        
        cookies = {}
        for cookie in cookie_data.get('cookies', []):
            cookies[cookie['name']] = cookie['value']
        
        print(f"[信息] 成功加载 {len(cookies)} 个Cookie")
        return cookies
    except Exception as e:
        print(f"[错误] 加载Cookie失败: {e}")
        return {}

def get_sample_project_id():
    """获取一个示例项目ID"""
    try:
        DB_CONFIG = get_db_config('default')
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        cursor.execute("SELECT DISTINCT `项目编码` FROM sign_data_detail WHERE `项目编码` IS NOT NULL AND `项目编码` != '' LIMIT 1")
        result = cursor.fetchone()
        
        cursor.close()
        conn.close()
        
        if result:
            return result[0]
        else:
            return None
    except Exception as e:
        print(f"[错误] 获取项目ID失败: {e}")
        return None

def test_project_info_api(project_id, cookies, login_no):
    """测试项目基本信息API"""
    API_URL = "https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/projectManage/queryProjectInfo"
    
    cookie_str = "; ".join([f"{name}={value}" for name, value in cookies.items()])
    
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json',
        'Host': 'dict.gmcc.net:30722',
        'Origin': 'https://dict.gmcc.net:30722',
        'Pragma': 'no-cache',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Cookie': cookie_str
    }
    
    data = {
        "ROOT": {
            "HEADER": {
                "OPR_INFO": {
                    "LOGIN_NO": login_no
                }
            },
            "BODY": {
                "PROJECT_ID": project_id
            }
        }
    }
    
    try:
        print(f"[测试] 请求项目信息: {project_id}")
        response = requests.post(API_URL, headers=headers, json=data, timeout=30, verify=False)
        
        print(f"[响应] HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            return_code = result.get('ROOT', {}).get('BODY', {}).get('RETURN_CODE', '')
            return_msg = result.get('ROOT', {}).get('BODY', {}).get('RETURN_MSG', '')
            
            print(f"[响应] 返回码: {return_code}")
            print(f"[响应] 返回消息: {return_msg}")
            
            if return_code == '0':
                out_data = result.get('ROOT', {}).get('BODY', {}).get('OUT_DATA', {})
                print(f"[成功] 获取到项目信息，字段数: {len(out_data)}")
                
                # 显示部分关键字段
                key_fields = ['PROJECT_NAME', 'CUST_NAME', 'PROJECT_STAGE_NAME', 'ESTIMATED_AMOUNT']
                for field in key_fields:
                    if field in out_data:
                        print(f"  {field}: {out_data[field]}")
                
                return True, out_data
            else:
                print(f"[失败] API返回错误: {return_msg}")
                return False, None
        else:
            print(f"[失败] HTTP请求失败: {response.status_code}")
            print(f"[响应内容] {response.text[:200]}...")
            return False, None
            
    except Exception as e:
        print(f"[异常] 请求失败: {e}")
        return False, None

def main():
    """主函数"""
    print("=" * 60)
    print("测试单个项目爬虫功能")
    print("=" * 60)
    
    # 加载Cookie
    cookies = load_cookies()
    if not cookies:
        print("[错误] Cookie加载失败，请先运行 login2cookie.py 获取Cookie")
        return
    
    # 获取登录用户名
    login_no, _, _ = get_login_credentials()
    print(f"[信息] 登录用户: {login_no}")
    
    # 获取示例项目ID
    project_id = get_sample_project_id()
    if not project_id:
        print("[错误] 未获取到项目ID")
        return
    
    print(f"[信息] 测试项目ID: {project_id}")
    
    # 测试项目基本信息API
    success, data = test_project_info_api(project_id, cookies, login_no)
    
    if success:
        print("\n[结论] API测试成功！可以开始批量爬取数据")
    else:
        print("\n[结论] API测试失败！需要检查Cookie或网络连接")
        print("建议运行: python login2cookie.py 更新Cookie")

if __name__ == "__main__":
    main()
