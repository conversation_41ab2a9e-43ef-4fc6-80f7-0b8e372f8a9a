#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度分析加密过程，尝试找出与 JavaScript 版本的差异
"""

import hashlib
import base64
from urllib.parse import quote, unquote
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5, DES
from Crypto.Util.Padding import pad
import binascii


def analyze_expected_result():
    """分析期望的加密结果"""
    expected_result = "VDHesuGD7wIGwc4g5lGu8To/DJCbvhoXLNhgvlYLOvijLZkSzAqarPuovKdSjaUB7D3zY+aor2pR45MJ6ByKN48SSfgtNCOBI+WUcxVza+PAXEQFqdN0yVo2/Suq9I5X/LPMn3FJ15v38b6rElgQ129VRDtYXDUB5Gv+VLUZdCGUNKB8EtVCBC14AhMZAqcFSVqflT2OKF+Xn/oW6aJvogh53WmseSQHbaijwBDda9O4Y2GIKKewrnVX8TMzZ4VO14PUsZYDh3Hx2gyTvTpxta6jhXrIUYGxGfj0I5KBW6O2kDS5uD789urnkxA39YkcO3Jq5Egy0XuIaxyMi2uGeIoUev0en9Z1pSMU9ryi1zKyW+pJg9wRKDrGdAPKste1nYsLyiv7CNN8KNh3m3r/lbLXQ9mFouZEhHuBMcExh1SdNeXmod3Asbcw9aqeL9mmURgNiHUZUZcGf4yYWLScRA=="
    
    print("=== 分析期望结果 ===")
    print(f"原始长度: {len(expected_result)}")
    
    # URL 解码
    try:
        url_decoded = unquote(expected_result)
        print(f"URL解码后长度: {len(url_decoded)}")
        print(f"URL解码结果: {url_decoded[:50]}...")
        
        # Base64 解码
        try:
            # 添加填充
            padding = 4 - len(url_decoded) % 4
            if padding != 4:
                url_decoded += '=' * padding
            
            b64_decoded = base64.b64decode(url_decoded)
            print(f"Base64解码后长度: {len(b64_decoded)}")
            print(f"Base64解码结果 (hex): {binascii.hexlify(b64_decoded)[:50]}...")
            
            return b64_decoded
            
        except Exception as e:
            print(f"Base64解码失败: {e}")
            
    except Exception as e:
        print(f"URL解码失败: {e}")
    
    return None


def reverse_engineer_des():
    """尝试反向工程 DES 加密过程"""
    print("\n=== 反向工程 DES 加密 ===")
    
    expected_des_data = analyze_expected_result()
    if expected_des_data is None:
        return
    
    test_login_key = "1751645934512463581"
    
    # 准备 DES 密钥
    key_bytes = test_login_key.encode('utf-8')[:8].ljust(8, b'\x00')
    print(f"DES 密钥: {key_bytes}")
    print(f"DES 密钥 (hex): {binascii.hexlify(key_bytes)}")
    
    # 尝试 DES 解密
    try:
        cipher = DES.new(key_bytes, DES.MODE_ECB)
        decrypted = cipher.decrypt(expected_des_data)
        print(f"DES 解密结果长度: {len(decrypted)}")
        print(f"DES 解密结果 (hex): {binascii.hexlify(decrypted)[:100]}...")
        
        # 尝试去除填充
        from Crypto.Util.Padding import unpad
        try:
            unpadded = unpad(decrypted, DES.block_size)
            print(f"去除填充后长度: {len(unpadded)}")
            print(f"去除填充后 (hex): {binascii.hexlify(unpadded)[:100]}...")
            
            # 这应该是 RSA 加密的结果
            return unpadded
            
        except Exception as e:
            print(f"去除填充失败: {e}")
            
    except Exception as e:
        print(f"DES 解密失败: {e}")
    
    return None


def test_rsa_variations():
    """测试不同的 RSA 实现方式"""
    print("\n=== 测试 RSA 变体 ===")
    
    test_password = "Dewen@428"
    md5_hash = hashlib.md5(test_password.encode('utf-8')).hexdigest()
    print(f"MD5 哈希: {md5_hash}")
    
    # RSA 公钥
    public_key_pem = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvEr5to90Z5w5+vZ+TIJw
uNExLBuYBKgCvKZll85RAJmbwCMuDTBU18XB5RBerd6c/CUqnrYoxoRjaHGdpOIA
VsHOlxloR3s9Y9/0EUpjpKKfzSsLlUp9N5bHbsoImmJo5+3Hgxpquv/6MCdjTqZ8
P7Uwjdzg7XYMSeBZzEzJ2vIFctjUF4kQWX03ljclLHPpn0mZYQ4Ue5afMv/xuBj5
8BnKl3LLW2mTIMohNkfIqivNnWn3fF6/TJLaI0GMxDBese3QRaJWSDnjSRrMlRJF
NnvB5+AGoPHMfpekqde1t02Zj+MiwFupe/Pm4JkqC5TYEU+Dq7LIZ7LT4imafZR2
0QIDAQAB
-----END PUBLIC KEY-----"""
    
    rsa_key = RSA.import_key(public_key_pem)
    
    # 测试不同的 RSA 实现
    print("\n1. 使用 PKCS1_v1_5:")
    cipher1 = PKCS1_v1_5.new(rsa_key)
    encrypted1 = cipher1.encrypt(md5_hash.encode('utf-8'))
    print(f"   长度: {len(encrypted1)}")
    print(f"   结果 (hex): {binascii.hexlify(encrypted1)[:50]}...")
    
    # 测试多次加密（RSA 有随机性）
    print("\n2. 多次 PKCS1_v1_5 加密:")
    for i in range(3):
        encrypted = cipher1.encrypt(md5_hash.encode('utf-8'))
        print(f"   第{i+1}次: {binascii.hexlify(encrypted)[:50]}...")
    
    # 尝试其他 RSA 实现
    try:
        from Crypto.Cipher import PKCS1_OAEP
        print("\n3. 使用 PKCS1_OAEP:")
        cipher2 = PKCS1_OAEP.new(rsa_key)
        encrypted2 = cipher2.encrypt(md5_hash.encode('utf-8'))
        print(f"   长度: {len(encrypted2)}")
        print(f"   结果 (hex): {binascii.hexlify(encrypted2)[:50]}...")
    except Exception as e:
        print(f"PKCS1_OAEP 失败: {e}")


def test_different_encodings():
    """测试不同的编码方式"""
    print("\n=== 测试不同编码 ===")
    
    test_data = "hello world"
    
    # 测试不同的 URL 编码
    print("1. URL 编码变体:")
    print(f"   quote: {quote(test_data)}")
    print(f"   quote_plus: {quote(test_data).replace(' ', '+')}")
    
    # 测试 Base64 编码
    print("2. Base64 编码:")
    b64_encoded = base64.b64encode(test_data.encode('utf-8')).decode('utf-8')
    print(f"   标准: {b64_encoded}")
    print(f"   URL安全: {base64.urlsafe_b64encode(test_data.encode('utf-8')).decode('utf-8')}")


def compare_with_known_good():
    """与已知正确结果对比"""
    print("\n=== 与已知结果对比 ===")
    
    # 如果我们能从期望结果反推出 RSA 加密的数据
    expected_rsa_data = reverse_engineer_des()
    
    if expected_rsa_data:
        print(f"期望的 RSA 加密数据长度: {len(expected_rsa_data)}")
        print(f"期望的 RSA 加密数据 (hex): {binascii.hexlify(expected_rsa_data)[:100]}...")
        
        # 尝试用我们的 RSA 实现加密相同的数据
        test_password = "Dewen@428"
        md5_hash = hashlib.md5(test_password.encode('utf-8')).hexdigest()
        
        # 检查 MD5 是否匹配
        print(f"我们的 MD5: {md5_hash}")
        
        # 如果能解密 RSA，我们就能看到原始数据
        # 但由于我们只有公钥，无法解密


if __name__ == "__main__":
    analyze_expected_result()
    reverse_engineer_des()
    test_rsa_variations()
    test_different_encodings()
    compare_with_known_good()
    
    print("\n" + "="*60)
    print("💡 分析结论:")
    print("1. RSA 加密具有随机性，每次结果都不同")
    print("2. 需要确认 JavaScript 版本是否使用了固定的随机种子")
    print("3. 可能需要检查 JavaScript 中 RSA 库的具体实现")
    print("4. DES 加密部分的逻辑应该是正确的")
