-- 市场份额数据去重处理脚本

-- 第一部分：market_share_daily表去重处理
-- 创建临时表存储要保留的最小id（按业务字段分组）
CREATE TEMPORARY TABLE min_ids_daily AS
SELECT MIN(id) as min_id
FROM dict_spider.market_share_daily
GROUP BY 
    `日期`, `集团名称`, `总证件`, `属于拍照`, `属于非拍照新流入`, 
    `拍照时中山移动`, `截止当日在网`, `截止当日流失`, `拍照后新增`, 
    `其中属于XR`, `其中客经`, `其中网格经理`, `其中同一网格渠道`, 
    `其他`, `当日到达`, `拍照宽带`, `当日宽带`, `宽带流失`, 
    `宽带新增`, `宽带到达`;

-- 显示重复记录数量
SELECT 
    COUNT(*) AS total_records,
    (SELECT COUNT(*) FROM min_ids_daily) AS unique_records,
    COUNT(*) - (SELECT COUNT(*) FROM min_ids_daily) AS duplicate_records
FROM dict_spider.market_share_daily;

-- 删除重复记录（保留id最小的记录）
DELETE FROM dict_spider.market_share_daily
WHERE id NOT IN (SELECT min_id FROM min_ids_daily);

-- 删除临时表
DROP TEMPORARY TABLE IF EXISTS min_ids_daily;

-- 显示删除后的记录数
SELECT COUNT(*) AS remaining_records_daily FROM dict_spider.market_share_daily;

-- 第二部分：market_share_detail表去重处理
-- 创建临时表存储要保留的最小id（按业务字段分组）
CREATE TEMPORARY TABLE min_ids_detail AS
SELECT MIN(id) as min_id
FROM dict_spider.market_share_detail
GROUP BY 
    `更新日期`, `号码`, `集团名称`, `新入网归属类型`, `工号`, 
    `渠道编码`, `渠道名称`, `分公司`, `网格`, `是否XR`, 
    `是否有宽带`, `是否有高质量合约`;

-- 显示重复记录数量
SELECT 
    COUNT(*) AS total_records,
    (SELECT COUNT(*) FROM min_ids_detail) AS unique_records,
    COUNT(*) - (SELECT COUNT(*) FROM min_ids_detail) AS duplicate_records
FROM dict_spider.market_share_detail;

-- 删除重复记录（保留id最小的记录）
DELETE FROM dict_spider.market_share_detail
WHERE id NOT IN (SELECT min_id FROM min_ids_detail);

-- 删除临时表
DROP TEMPORARY TABLE IF EXISTS min_ids_detail;

-- 显示删除后的记录数
SELECT COUNT(*) AS remaining_records_detail FROM dict_spider.market_share_detail;