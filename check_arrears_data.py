#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查欠费数据表的内容
"""

import sys
import os
import json
import pymysql

# 添加配置文件路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'dict_romte'))
from dict_romte.config import get_db_config

def check_arrears_table():
    """检查欠费数据表"""
    try:
        # 获取数据库配置
        DB_CONFIG = get_db_config('default')
        
        # 连接数据库
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        print("=" * 60)
        print("检查 dict_zonghe_queryarrearslist 表数据")
        print("=" * 60)
        
        # 检查表是否存在
        cursor.execute("SHOW TABLES LIKE 'dict_zonghe_queryarrearslist'")
        if not cursor.fetchone():
            print("[错误] 表 dict_zonghe_queryarrearslist 不存在")
            return
        
        # 查询记录总数
        cursor.execute("SELECT COUNT(*) FROM dict_zonghe_queryarrearslist")
        total_count = cursor.fetchone()[0]
        print(f"[信息] 表中总记录数: {total_count}")
        
        if total_count == 0:
            print("[警告] 表中没有数据")
            return
        
        # 查询有response_data的记录数
        cursor.execute("SELECT COUNT(*) FROM dict_zonghe_queryarrearslist WHERE RESPONSE_DATA IS NOT NULL")
        data_count = cursor.fetchone()[0]
        print(f"[信息] 有RESPONSE_DATA的记录数: {data_count}")
        
        # 查看表结构
        cursor.execute("DESCRIBE dict_zonghe_queryarrearslist")
        columns = cursor.fetchall()
        print(f"[信息] 表结构:")
        for col in columns:
            print(f"  {col[0]} - {col[1]} - {col[2]} - {col[3]}")
        
        # 查看最新的一条记录
        cursor.execute("""
            SELECT id, INPUT_LOGIN_NO, INPUT_PROJECT_ID,
                   LEFT(RESPONSE_DATA, 200) as response_preview,
                   RETURN_CODE, RETURN_MSG,
                   import_time
            FROM dict_zonghe_queryarrearslist
            ORDER BY import_time DESC
            LIMIT 1
        """)
        
        latest_record = cursor.fetchone()
        if latest_record:
            print(f"\n[信息] 最新记录:")
            print(f"  ID: {latest_record[0]}")
            print(f"  登录号: {latest_record[1]}")
            print(f"  项目ID: {latest_record[2]}")
            print(f"  响应数据预览: {latest_record[3]}...")
            print(f"  返回码: {latest_record[4]}")
            print(f"  返回消息: {latest_record[5]}")
            print(f"  导入时间: {latest_record[6]}")
        
        # 查看完整的response_data结构
        cursor.execute("""
            SELECT RESPONSE_DATA
            FROM dict_zonghe_queryarrearslist
            WHERE RESPONSE_DATA IS NOT NULL
            ORDER BY import_time DESC
            LIMIT 1
        """)
        
        response_data = cursor.fetchone()
        if response_data and response_data[0]:
            print(f"\n[信息] 完整响应数据结构分析:")
            try:
                data = json.loads(response_data[0])
                print(f"  根节点: {list(data.keys())}")
                
                if 'ROOT' in data:
                    root = data['ROOT']
                    print(f"  ROOT节点: {list(root.keys())}")
                    
                    if 'BODY' in root:
                        body = root['BODY']
                        print(f"  BODY节点: {list(body.keys())}")
                        
                        if 'OUT_DATA' in body:
                            out_data = body['OUT_DATA']
                            print(f"  OUT_DATA节点: {list(out_data.keys())}")
                            
                            if 'OUT_PARAM' in out_data:
                                out_param = out_data['OUT_PARAM']
                                if isinstance(out_param, list):
                                    print(f"  OUT_PARAM数组长度: {len(out_param)}")
                                    if len(out_param) > 0:
                                        print(f"  第一个元素字段: {list(out_param[0].keys())}")
                                        print(f"  第一个元素内容: {out_param[0]}")
                                else:
                                    print(f"  OUT_PARAM类型: {type(out_param)}")
                                    print(f"  OUT_PARAM内容: {out_param}")
                            else:
                                print("  [警告] OUT_DATA中没有OUT_PARAM字段")
                        else:
                            print("  [警告] BODY中没有OUT_DATA字段")
                    else:
                        print("  [警告] ROOT中没有BODY字段")
                else:
                    print("  [警告] 数据中没有ROOT字段")
                    
            except json.JSONDecodeError as e:
                print(f"  [错误] JSON解析失败: {e}")
            except Exception as e:
                print(f"  [错误] 数据分析失败: {e}")
        
        # 关闭连接
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"[错误] 检查数据失败: {e}")

def main():
    """主函数"""
    check_arrears_table()

if __name__ == "__main__":
    main()
