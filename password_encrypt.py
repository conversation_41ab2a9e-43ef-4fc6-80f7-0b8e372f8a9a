#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
密码加密函数 - Python 3.10 实现
对应 JavaScript 中的 password 函数
实现 MD5 + RSA + DES 三重加密
"""

import hashlib
import base64
from urllib.parse import quote
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5, DES
from Crypto.Util.Padding import pad
import binascii


class PasswordEncryptor:
    """密码加密器类"""
    
    def __init__(self):
        """初始化加密器，设置 RSA 公钥"""
        # RSA 公钥（来自 JavaScript 代码）
        self.public_key_pem = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvEr5to90Z5w5+vZ+TIJw
uNExLBuYBKgCvKZll85RAJmbwCMuDTBU18XB5RBerd6c/CUqnrYoxoRjaHGdpOIA
VsHOlxloR3s9Y9/0EUpjpKKfzSsLlUp9N5bHbsoImmJo5+3Hgxpquv/6MCdjTqZ8
P7Uwjdzg7XYMSeBZzEzJ2vIFctjUF4kQWX03ljclLHPpn0mZYQ4Ue5afMv/xuBj5
8BnKl3LLW2mTIMohNkfIqivNnWn3fF6/TJLaI0GMxDBese3QRaJWSDnjSRrMlRJF
NnvB5+AGoPHMfpekqde1t02Zj+MiwFupe/Pm4JkqC5TYEU+Dq7LIZ7LT4imafZR2
0QIDAQAB
-----END PUBLIC KEY-----"""
        
        # 初始化 RSA 公钥
        self.rsa_key = RSA.import_key(self.public_key_pem)
        self.rsa_cipher = PKCS1_v1_5.new(self.rsa_key)
    
    def md5_hash(self, text: str) -> str:
        """
        MD5 哈希函数
        对应 JavaScript 中的 k()(t.password)
        
        Args:
            text: 要哈希的文本
            
        Returns:
            MD5 哈希值（十六进制字符串）
        """
        return hashlib.md5(text.encode('utf-8')).hexdigest()
    
    def rsa_encrypt(self, text: str) -> bytes:
        """
        RSA 公钥加密
        对应 JavaScript 中的 I.encrypt(e)
        
        Args:
            text: 要加密的文本
            
        Returns:
            RSA 加密后的字节数据
        """
        # 将文本转换为字节
        text_bytes = text.encode('utf-8')
        
        # RSA 加密
        encrypted = self.rsa_cipher.encrypt(text_bytes)
        
        # 返回 base64 编码的结果（模拟 JavaScript 的行为）
        return base64.b64encode(encrypted)
    
    def des_encrypt(self, data: bytes, key: str) -> str:
        """
        DES 加密
        对应 JavaScript 中的 C.a.DES.encrypt(e, n, a)
        
        Args:
            data: 要加密的数据（字节）
            key: DES 密钥
            
        Returns:
            DES 加密后的 base64 字符串
        """
        # 确保密钥长度为 8 字节（DES 要求）
        key_bytes = key.encode('utf-8')[:8].ljust(8, b'\x00')
        
        # 创建 DES 加密器（ECB 模式）
        cipher = DES.new(key_bytes, DES.MODE_ECB)
        
        # 对数据进行 PKCS7 填充
        padded_data = pad(data, DES.block_size)
        
        # 加密
        encrypted = cipher.encrypt(padded_data)
        
        # 返回 base64 编码的结果
        return base64.b64encode(encrypted).decode('utf-8')
    
    def encrypt_password(self, password: str, login_key: str) -> str:
        """
        完整的密码加密函数
        对应 JavaScript 中的 password 函数
        
        Args:
            password: 原始密码
            login_key: 登录密钥（来自服务器）
            
        Returns:
            加密后的密码字符串（URL 编码）
        """
        # 第一步：MD5 哈希
        md5_hash = self.md5_hash(password)
        print(f"1. MD5 哈希: {md5_hash}")
        
        # 第二步：RSA 公钥加密
        rsa_encrypted = self.rsa_encrypt(md5_hash)
        print(f"2. RSA 加密: {rsa_encrypted.decode('utf-8')[:50]}...")
        
        # 第三步：DES 加密
        des_encrypted = self.des_encrypt(rsa_encrypted, login_key)
        print(f"3. DES 加密: {des_encrypted[:50]}...")
        
        # 第四步：URL 编码
        url_encoded = quote(des_encrypted)
        print(f"4. URL 编码: {url_encoded[:50]}...")
        
        return url_encoded


def main():
    """测试函数"""
    # 创建加密器实例
    encryptor = PasswordEncryptor()
    
    # 测试参数
    test_password = "123456"
    test_login_key = "testkey123"
    
    print("=== 密码加密测试 ===")
    print(f"原始密码: {test_password}")
    print(f"登录密钥: {test_login_key}")
    print()
    
    # 执行加密
    encrypted_password = encryptor.encrypt_password(test_password, test_login_key)
    
    print()
    print(f"最终加密结果: {encrypted_password}")
    print(f"结果长度: {len(encrypted_password)}")


if __name__ == "__main__":
    main()
