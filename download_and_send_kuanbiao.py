import requests
import json
import os
from datetime import datetime
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
import pandas as pd

# 配置信息
# 126邮箱配置
EMAIL = "<EMAIL>"
PASSWORD = "XBbyfQEf2PpqUif5"  # 授权密码
SMTP_SERVER = "smtp.126.com"
SMTP_PORT = 465

# 收件人
RECIPIENT = "<EMAIL>"

# 数据平台配置
DATA_URL = "http://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/common/excelController/export"
LOGIN_HEADERS = {
    'Content-Type': 'application/json',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Cookie': "BSS-SESSION=NGE2NDBmZWMtM2FmZS00YTQ5LThmOWEtZDY5MGViNmQ2NGYz; isLogin=ImlzTG9naW4i; requestId=4e86f220-29ed-11f0-854e-a7722f3aadfe; systemUserCode=InpoZW5nZGV3ZW4i; jsession_id_4_boss=nF4FACEFE75B5ED3D50D25C5B16AE90D8-1"
}

def download_kuanbiao_data():
    """下载宽表视图数据"""
    today = datetime.now().strftime("%Y%m%d")
    filename = f"宽表{today}.xlsx"
    
    print(f"[信息] 开始下载宽表数据 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 构建请求数据
    payload = {
        "ROOT": {
            "HEADER": {
                "OPR_INFO": {
                    "LOGIN_NO": "zhengdewen"
                }
            },
            "BODY": {
                "BUSI_INFO": {
                    "QUERY_TYPE": "KUANBIAO_VIEW",  # 宽表视图
                    "START_DATE": datetime.now().strftime("%Y-%m-%d"),
                    "END_DATE": datetime.now().strftime("%Y-%m-%d")
                }
            }
        }
    }
    
    try:
        # 发送请求
        print("[信息] 正在发送请求获取宽表数据...")
        response = requests.post(DATA_URL, data=json.dumps(payload), headers=LOGIN_HEADERS)
        response.raise_for_status()  # 检查响应状态
        
        # 保存Excel文件
        with open(filename, "wb") as f:
            f.write(response.content)
        print(f"[成功] 宽表数据已保存为 {filename}")
        
        return filename
    except Exception as e:
        print(f"[错误] 下载宽表数据失败: {e}")
        import traceback
        print(traceback.format_exc())
        return None

def send_email_with_attachment(attachment_path):
    """发送带附件的邮件"""
    today = datetime.now().strftime("%Y%m%d")
    today_formatted = datetime.now().strftime("%Y%m%d")
    subject = f"宽表明细{today_formatted}"
    
    print(f"[信息] 开始发送邮件 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 创建邮件对象
        msg = MIMEMultipart()
        msg['From'] = EMAIL
        msg['To'] = RECIPIENT
        msg['Subject'] = subject
        
        # 添加邮件正文
        body = f"附件为{today}宽表数据，请查收。\n\n此邮件由系统自动发送，请勿回复。"
        msg.attach(MIMEText(body, 'plain'))
        
        # 添加附件
        with open(attachment_path, 'rb') as file:
            attachment = MIMEApplication(file.read())
            attachment.add_header('Content-Disposition', 'attachment', filename=os.path.basename(attachment_path))
            msg.attach(attachment)
        
        # 连接到SMTP服务器并发送邮件
        print(f"[信息] 正在连接到SMTP服务器: {SMTP_SERVER}...")
        server = smtplib.SMTP_SSL(SMTP_SERVER, SMTP_PORT)
        server.login(EMAIL, PASSWORD)
        
        print(f"[信息] 正在发送邮件到: {RECIPIENT}...")
        server.send_message(msg)
        server.quit()
        
        print(f"[成功] 邮件已发送到: {RECIPIENT}")
        return True
    except Exception as e:
        print(f"[错误] 发送邮件失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def main():
    print(f"🔄 开始下载宽表数据并发送邮件 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 下载宽表数据
    attachment_path = download_kuanbiao_data()
    
    if attachment_path and os.path.exists(attachment_path):
        # 发送邮件
        success = send_email_with_attachment(attachment_path)
        
        if success:
            print("✅ 任务完成：宽表数据已下载并发送")
        else:
            print("❌ 任务失败：邮件发送失败")
    else:
        print("❌ 任务失败：宽表数据下载失败")

if __name__ == "__main__":
    main()