#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
完整的86个接口处理计划
根据接口分析结果.md生成所有接口的处理方案
"""

import re
import os

def extract_all_interfaces():
    """从接口分析结果.md提取所有86个接口信息"""
    interfaces = []
    
    try:
        with open('dict_romte/接口分析结果.md', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 使用正则表达式提取所有接口
        pattern = r'## (\d+)\. (.+?)\n\*\*中文名称\*\*: (.+?)\n'
        matches = re.findall(pattern, content)
        
        for match in matches:
            interface_num = int(match[0])
            api_path = match[1].strip()
            chinese_name = match[2].strip()
            
            interfaces.append({
                'number': interface_num,
                'api_path': api_path,
                'chinese_name': chinese_name,
                'table_name': f'dict_zonghe_{api_path.split("/")[-1]}',
                'file_name': f'dict_zonghe_{api_path.split("/")[-1]}.py'
            })
        
        print(f"[成功] 提取到 {len(interfaces)} 个接口")
        return interfaces
        
    except Exception as e:
        print(f"[错误] 提取接口失败: {e}")
        return []

def categorize_interfaces(interfaces):
    """将接口按复杂度分类"""
    simple_interfaces = []  # 只需要PROJECT_ID的接口
    complex_interfaces = []  # 需要额外参数的接口
    
    # 已知的简单接口（只需要PROJECT_ID）
    simple_patterns = [
        'queryProjectInfo', 'queryProjectDemand', 'queryProgram', 
        'saleBiddingInfo', 'queryProjectPlanWithImplement',
        'queryProjectAmount', 'qryIncomeProgressByProject',
        'qryContractByProject', 'queryProjectBenefit',
        'queryProjectFlow', 'queryProjectMilestone',
        'queryProjectJointTest', 'queryProjectStart',
        'queryTotalProcess', 'queryTaskDisassemble',
        'queryProjectDisclosure', 'queryTsolution',
        'queryProjectDecisionInfo', 'queryProjectEstimatedExp',
        'queryProjectIncDetailList', 'queryAbilityMapByProject',
        'queryMobliePoliceInfo', 'queryPlanAndSubjectTable',
        'queryContractInfo', 'queryAgreementContract',
        'queryExpensePlanInfo', 'queryExpenseChangeInfo',
        'queryExpenseChangeInfoList'
    ]
    
    for interface in interfaces:
        api_name = interface['api_path'].split('/')[-1]
        if any(pattern in api_name for pattern in simple_patterns):
            simple_interfaces.append(interface)
        else:
            complex_interfaces.append(interface)
    
    return simple_interfaces, complex_interfaces

def generate_processing_plan():
    """生成完整的处理计划"""
    print("=" * 80)
    print("86个接口完整处理计划")
    print("=" * 80)
    
    # 提取所有接口
    interfaces = extract_all_interfaces()
    if not interfaces:
        return
    
    # 分类接口
    simple_interfaces, complex_interfaces = categorize_interfaces(interfaces)
    
    print(f"\n📊 接口分类统计:")
    print(f"  简单接口（只需PROJECT_ID）: {len(simple_interfaces)} 个")
    print(f"  复杂接口（需要额外参数）: {len(complex_interfaces)} 个")
    print(f"  总计: {len(interfaces)} 个")
    
    # 已完成的接口
    completed_interfaces = [
        'batchLoadCodeList', 'getTodo', 'queryProjectInfo', 'loadCodeList',
        'queryProjectDemand', 'qryWoListByProject', 'qryContractByProject',
        'queryProjectProdprcDict', 'queryProjectPlanWithImplement',
        'queryProjectAmount', 'saleBiddingInfo', 'queryProgram',
        'qryIncomeProgressByProject'
    ]
    
    print(f"\n✅ 已完成接口: {len(completed_interfaces)} 个")
    for i, name in enumerate(completed_interfaces, 1):
        print(f"  {i:2d}. {name}")
    
    # 待处理的接口
    remaining_interfaces = []
    for interface in interfaces:
        api_name = interface['api_path'].split('/')[-1]
        if api_name not in completed_interfaces:
            remaining_interfaces.append(interface)
    
    print(f"\n⏳ 待处理接口: {len(remaining_interfaces)} 个")
    
    # 按优先级排序
    priority_1 = []  # 高优先级：项目核心信息
    priority_2 = []  # 中优先级：项目管理相关
    priority_3 = []  # 低优先级：其他功能
    
    high_priority_keywords = [
        'project', 'contract', 'income', 'amount', 'bill', 'expense'
    ]
    
    medium_priority_keywords = [
        'team', 'member', 'milestone', 'plan', 'implement', 'disclosure'
    ]
    
    for interface in remaining_interfaces:
        api_path_lower = interface['api_path'].lower()
        chinese_name_lower = interface['chinese_name'].lower()
        
        if any(keyword in api_path_lower or keyword in chinese_name_lower 
               for keyword in high_priority_keywords):
            priority_1.append(interface)
        elif any(keyword in api_path_lower or keyword in chinese_name_lower 
                 for keyword in medium_priority_keywords):
            priority_2.append(interface)
        else:
            priority_3.append(interface)
    
    print(f"\n🎯 优先级分配:")
    print(f"  高优先级（核心业务）: {len(priority_1)} 个")
    print(f"  中优先级（管理功能）: {len(priority_2)} 个")
    print(f"  低优先级（其他功能）: {len(priority_3)} 个")
    
    # 生成详细的处理计划
    print(f"\n📋 详细处理计划:")
    
    print(f"\n🔥 第一批（高优先级）- {len(priority_1)} 个接口:")
    for i, interface in enumerate(priority_1, 1):
        print(f"  {i:2d}. {interface['chinese_name']}")
        print(f"      API: {interface['api_path']}")
        print(f"      表名: {interface['table_name']}")
    
    print(f"\n📈 第二批（中优先级）- {len(priority_2)} 个接口:")
    for i, interface in enumerate(priority_2, 1):
        print(f"  {i:2d}. {interface['chinese_name']}")
        print(f"      API: {interface['api_path']}")
    
    print(f"\n📝 第三批（低优先级）- {len(priority_3)} 个接口:")
    for i, interface in enumerate(priority_3, 1):
        print(f"  {i:2d}. {interface['chinese_name']}")
        print(f"      API: {interface['api_path']}")
    
    # 生成执行建议
    print(f"\n💡 执行建议:")
    print(f"1. 立即处理第一批高优先级接口（{len(priority_1)}个）")
    print(f"2. 使用现有的模板快速生成数据表和爬虫程序")
    print(f"3. 批量测试和验证数据同步")
    print(f"4. 根据业务需求决定是否处理第二、三批接口")
    
    # 估算工作量
    estimated_hours = len(priority_1) * 0.5 + len(priority_2) * 0.3 + len(priority_3) * 0.2
    print(f"\n⏰ 工作量估算:")
    print(f"  第一批: {len(priority_1)} × 0.5小时 = {len(priority_1) * 0.5}小时")
    print(f"  第二批: {len(priority_2)} × 0.3小时 = {len(priority_2) * 0.3}小时")
    print(f"  第三批: {len(priority_3)} × 0.2小时 = {len(priority_3) * 0.2}小时")
    print(f"  总计: 约 {estimated_hours:.1f} 小时")
    
    return priority_1, priority_2, priority_3

def main():
    """主函数"""
    priority_1, priority_2, priority_3 = generate_processing_plan()
    
    # 保存处理计划到文件
    with open('86_interfaces_processing_plan.txt', 'w', encoding='utf-8') as f:
        f.write("86个接口完整处理计划\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"高优先级接口（{len(priority_1)}个）:\n")
        for i, interface in enumerate(priority_1, 1):
            f.write(f"{i:2d}. {interface['chinese_name']} - {interface['api_path']}\n")
        
        f.write(f"\n中优先级接口（{len(priority_2)}个）:\n")
        for i, interface in enumerate(priority_2, 1):
            f.write(f"{i:2d}. {interface['chinese_name']} - {interface['api_path']}\n")
        
        f.write(f"\n低优先级接口（{len(priority_3)}个）:\n")
        for i, interface in enumerate(priority_3, 1):
            f.write(f"{i:2d}. {interface['chinese_name']} - {interface['api_path']}\n")
    
    print(f"\n📄 处理计划已保存到: 86_interfaces_processing_plan.txt")

if __name__ == "__main__":
    main()
