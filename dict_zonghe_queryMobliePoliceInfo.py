#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
查询移动警务信息爬虫程序
接口：saleCenterApp/incomeManage/queryMobliePoliceInfo
"""

import sys
import os
import json
import requests
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
import pymysql

sys.path.append(os.path.join(os.path.dirname(__file__), 'dict_romte'))
from dict_romte.config import get_db_config, get_login_credentials

DB_CONFIG = get_db_config('default')
API_URL = "https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/incomeManage/queryMobliePoliceInfo"

def load_cookies():
    try:
        with open('cookies.txt', 'r', encoding='utf-8') as f:
            cookie_data = json.load(f)
        cookies = {}
        for cookie in cookie_data.get('cookies', []):
            cookies[cookie['name']] = cookie['value']
        return cookies
    except:
        return {}

def get_test_project_ids():
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        cursor.execute("SELECT project_id FROM v_distinct_project_id LIMIT 5")
        return [row[0] for row in cursor.fetchall()]
    except:
        return ["TEST_PROJECT"]
    finally:
        if 'cursor' in locals(): cursor.close()
        if 'conn' in locals(): conn.close()

def query_api(project_id, cookies, login_no):
    try:
        cookie_str = "; ".join([f"{k}={v}" for k, v in cookies.items()])
        headers = {
            'Content-Type': 'application/json',
            'Cookie': cookie_str
        }
        
        body = {}
        if false:
            body["PROJECT_ID"] = project_id
        
        data = {
            "ROOT": {
                "HEADER": {"OPR_INFO": {"LOGIN_NO": login_no}},
                "BODY": body
            }
        }
        
        response = requests.post(API_URL, headers=headers, json=data, timeout=30, verify=False)
        if response.status_code == 200:
            result = response.json()
            return_code = result.get('ROOT', {}).get('BODY', {}).get('RETURN_CODE', '')
            if return_code == '0':
                return result.get('ROOT', {}).get('BODY', {}).get('OUT_DATA', {})
        return None
    except Exception as e:
        print(f"[错误] API调用失败: {e}")
        return None

def save_simple_data(data_list):
    if not data_list:
        return
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 创建简单表（如果不存在）
        create_sql = f"""
        CREATE TABLE IF NOT EXISTS dict_zonghe_queryMobliePoliceInfo (
            id int AUTO_INCREMENT PRIMARY KEY,
            project_id varchar(100),
            response_data text,
            import_time timestamp DEFAULT CURRENT_TIMESTAMP
        )
        """
        cursor.execute(create_sql)
        
        # 清空并插入数据
        cursor.execute(f"DELETE FROM dict_zonghe_queryMobliePoliceInfo")
        
        for data in data_list:
            cursor.execute(
                f"INSERT INTO dict_zonghe_queryMobliePoliceInfo (project_id, response_data) VALUES (%s, %s)",
                (data['project_id'], json.dumps(data['response'], ensure_ascii=False))
            )
        
        conn.commit()
        print(f"[成功] 保存了 {len(data_list)} 条数据到 dict_zonghe_queryMobliePoliceInfo")
        
    except Exception as e:
        print(f"[错误] 保存数据失败: {e}")
    finally:
        if 'cursor' in locals(): cursor.close()
        if 'conn' in locals(): conn.close()

def main():
    print(f"开始测试接口: 查询移动警务信息")
    
    cookies = load_cookies()
    if not cookies:
        print("[错误] 无法加载Cookie")
        return
    
    login_no, _, _ = get_login_credentials()
    project_ids = get_test_project_ids()
    
    data_list = []
    for project_id in project_ids:
        result = query_api(project_id, cookies, login_no)
        if result:
            data_list.append({'project_id': project_id, 'response': result})
            print(f"[成功] 项目 {project_id} 获取数据成功")
        else:
            print(f"[失败] 项目 {project_id} 获取数据失败")
    
    save_simple_data(data_list)
    print(f"接口测试完成: 查询移动警务信息")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "-all":
        main()
    else:
        print("使用方法: python dict_zonghe_queryMobliePoliceInfo.py -all")
