import pandas as pd
import pymysql
import warnings
import sys
import os
from sqlalchemy import create_engine
from datetime import datetime

# 忽略pandas的警告
warnings.filterwarnings('ignore')

# 数据库连接信息
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'cmcc12345',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

def import_to_mysql(input_file=None):
    # 如果没有提供输入文件，尝试使用命令行参数
    if input_file is None:
        if len(sys.argv) > 1:
            input_file = sys.argv[1]
        else:
            # 查找最新的天眼查文件
            files = [f for f in os.listdir('.') if f.startswith('【天眼查】') and f.endswith('.xlsx')]
            if not files:
                print("[错误] 未找到天眼查数据文件")
                return False
            # 按修改时间排序，获取最新的文件
            input_file = max(files, key=os.path.getmtime)
            print(f"[信息] 自动选择最新的文件: {input_file}")

    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"[错误] 文件不存在: {input_file}")
        return False

    # 读取Excel文件，第二行为表头
    print(f"[信息] 正在读取Excel文件: {input_file}")
    df = pd.read_excel(input_file, header=1)  # 指定第二行为表头

    # 检查数据
    print(f"读取到 {len(df)} 行数据")
    print("表头列名:", df.columns.tolist())

    # 数据清洗和验证
    print("[信息] 正在清洗和验证数据...")
    
    # 处理列名，确保有效
    df.columns = [col.strip() for col in df.columns]
    
    # 处理可能的数值列
    numeric_columns = ['注册资本', '实缴资本', '参保人数', '招投标数量', '专利数量', '软件著作权数量']
    for col in numeric_columns:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 连接数据库
    print("正在连接到MySQL数据库...")
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()

    try:
        # 检查表是否存在，如果不存在则创建
        print("检查数据表是否存在...")
        cursor.execute("SHOW TABLES LIKE 'tianyancha'")
        if not cursor.fetchone():
            print("数据表不存在，正在创建...")
            # 使用TEXT类型而不是VARCHAR(255)来避免行大小限制
            columns = []
            for col in df.columns:
                # 替换特殊字符，确保列名有效
                safe_col = col.replace(' ', '_').replace('-', '_').replace('(', '').replace(')', '')
                # 对于可能的数值列使用适当的类型
                if col in ['注册资本', '实缴资本']:
                    columns.append(f"`{safe_col}` DECIMAL(20,2)")
                elif col in ['参保人数', '招投标数量', '专利数量', '软件著作权数量']:
                    columns.append(f"`{safe_col}` INT")
                else:
                    columns.append(f"`{safe_col}` TEXT")

            create_table_sql = """
            CREATE TABLE `tianyancha` (
                id INT AUTO_INCREMENT PRIMARY KEY,
                {0},
                import_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """.format(', '.join(columns))

            cursor.execute(create_table_sql)
            print("数据表创建成功")

        # 检查import_logs表是否存在，如果不存在则创建
        cursor.execute("SHOW TABLES LIKE 'import_logs'")
        if not cursor.fetchone():
            print("import_logs表不存在，正在创建...")
            create_logs_table_sql = """
            CREATE TABLE `import_logs` (
                id INT AUTO_INCREMENT PRIMARY KEY,
                file_name TEXT,
                table_name VARCHAR(100),
                record_count INT,
                import_time DATETIME,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """
            cursor.execute(create_logs_table_sql)
            print("import_logs表创建成功")

        # 使用pandas的to_sql方法直接导入数据
        print("正在使用pandas to_sql方法插入数据...")

        # 处理列名，确保有效
        df_copy = df.copy()
        df_copy.columns = [col.replace(' ', '_').replace('-', '_').replace('(', '').replace(')', '') for col in df.columns]

        # 创建SQLAlchemy引擎
        engine_url = f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?charset={DB_CONFIG['charset']}"
        engine = create_engine(engine_url)

        # 定义数据类型映射
        from sqlalchemy.types import DECIMAL, TEXT, INTEGER
        dtype = {}
        for col in df_copy.columns:
            safe_col = col.replace(' ', '_').replace('-', '_').replace('(', '').replace(')', '')
            if col in ['注册资本', '实缴资本']:
                dtype[safe_col] = DECIMAL(20,2)
            elif col in ['参保人数', '招投标数量', '专利数量', '软件著作权数量']:
                dtype[safe_col] = INTEGER()
            else:
                dtype[safe_col] = TEXT()

        # 使用to_sql方法将数据导入到MySQL，指定数据类型
        df_copy.to_sql('tianyancha', engine, if_exists='append', index=False, chunksize=10, dtype=dtype)

        print(f"[成功] 成功导入 {len(df_copy)} 行数据到 dict_spider.tianyancha 表")
        
        # 添加导入记录
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        cursor.execute(
            "INSERT INTO import_logs (file_name, table_name, record_count, import_time) VALUES (%s, %s, %s, %s)",
            (input_file, 'tianyancha', len(df_copy), timestamp)
        )
        conn.commit()
        
        return True

    except Exception as e:
        conn.rollback()
        print(f"[错误] 导入数据时出错: {e}")
        # 提供更详细的错误信息
        import traceback
        print(traceback.format_exc())
        return False
    finally:
        cursor.close()
        conn.close()
        print("[信息] 数据库连接已关闭")

def main():
    import_to_mysql()

if __name__ == "__main__":
    main()