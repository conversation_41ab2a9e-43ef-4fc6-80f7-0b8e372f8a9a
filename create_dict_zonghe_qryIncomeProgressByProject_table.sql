-- 创建综合查询按项目查询收入进度表
-- 对应接口：saleCenterApp/incomeManage/qryIncomeProgressByProject

DROP TABLE IF EXISTS `dict_zonghe_qryIncomeProgressByProject`;

CREATE TABLE IF NOT EXISTS `dict_zonghe_qryIncomeProgressByProject` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  
  -- 入参字段（来源于源数据表）
  `INPUT_LOGIN_NO` varchar(100) DEFAULT NULL COMMENT '入参-登录用户名',
  `INPUT_PROJECT_ID` varchar(100) DEFAULT NULL COMMENT '入参-项目ID（来源：v_distinct_project_id视图或sign_data_detail表项目编码字段）',
  `INPUT_PROJECT_STAGE` varchar(50) DEFAULT NULL COMMENT '入参-项目阶段',
  
  -- 响应数据字段（基于接口返回的JSON结构）
  `EXPENSE_DETAIL_ID` varchar(200) DEFAULT NULL COMMENT 'EXPENSE_DETAIL_ID',
  `EXPENSE_ID` varchar(200) DEFAULT NULL COMMENT 'EXPENSE_ID',
  `PROD_ID` varchar(200) DEFAULT NULL COMMENT 'PROD_ID',
  `PROD_NAME` text DEFAULT NULL COMMENT 'PROD_NAME',
  `CT_OR_IT` varchar(200) DEFAULT NULL COMMENT 'CT_OR_IT',
  `SUBJECT_CODE` varchar(200) DEFAULT NULL COMMENT 'SUBJECT_CODE',
  `SUBJECT_NAME` text DEFAULT NULL COMMENT 'SUBJECT_NAME',
  `SUBJECT_CLASS` varchar(200) DEFAULT NULL COMMENT 'SUBJECT_CLASS',
  `IS_FLAT_RATE` varchar(200) DEFAULT NULL COMMENT 'IS_FLAT_RATE',
  `VAT_RATE` varchar(200) DEFAULT NULL COMMENT 'VAT_RATE',
  `CYCLE_TYPE` varchar(200) DEFAULT NULL COMMENT 'CYCLE_TYPE',
  `CYCLE_COUNT` varchar(200) DEFAULT NULL COMMENT 'CYCLE_COUNT',
  `COLL_CYCLE_TYPE` varchar(200) DEFAULT NULL COMMENT 'COLL_CYCLE_TYPE',
  `COLL_CYCLE_COUNT` varchar(200) DEFAULT NULL COMMENT 'COLL_CYCLE_COUNT',
  `MONEY` decimal(15,2) DEFAULT NULL COMMENT 'MONEY',
  `VAT` varchar(200) DEFAULT NULL COMMENT 'VAT',
  `MONEY_EX_TAX` decimal(15,2) DEFAULT NULL COMMENT 'MONEY_EX_TAX',
  `PLAN_BILL_START_TIME` varchar(50) DEFAULT NULL COMMENT 'PLAN_BILL_START_TIME',
  `PLAN_COLL_START_TIME` varchar(50) DEFAULT NULL COMMENT 'PLAN_COLL_START_TIME',
  `ACT_BILL_START_TIME` varchar(50) DEFAULT NULL COMMENT 'ACT_BILL_START_TIME',
  `BILL_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'BILL_AMOUNT',
  `BILL_AMOUNT_TOTAL_VAT` decimal(15,2) DEFAULT NULL COMMENT 'BILL_AMOUNT_TOTAL_VAT',
  `COLLECTION_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'COLLECTION_AMOUNT',
  
  -- 系统字段
  `RUN_IP` varchar(50) DEFAULT NULL COMMENT '运行IP',
  `REQUEST_ID` varchar(100) DEFAULT NULL COMMENT '请求ID',
  `RETURN_CODE` varchar(10) DEFAULT NULL COMMENT '返回码',
  `RETURN_MSG` varchar(200) DEFAULT NULL COMMENT '返回消息',
  `USER_MSG` varchar(200) DEFAULT NULL COMMENT '用户消息',
  `DETAIL_MSG` text DEFAULT NULL COMMENT '详细消息',
  
  -- 元数据字段
  `import_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_input_project_id` (`INPUT_PROJECT_ID`),
  KEY `idx_import_time` (`import_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='综合查询{interface['chinese_name']}表';
