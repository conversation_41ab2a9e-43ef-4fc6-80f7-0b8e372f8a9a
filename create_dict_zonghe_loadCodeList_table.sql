-- 创建综合查询加载数据字典代码列表表
-- 对应接口：saleCenterApp/common/dataDictService/loadCodeList

DROP TABLE IF EXISTS `dict_zonghe_loadCodeList`;

CREATE TABLE IF NOT EXISTS `dict_zonghe_loadCodeList` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  
  -- 入参字段（来源于源数据表）
  `INPUT_LOGIN_NO` varchar(100) DEFAULT NULL COMMENT '入参-登录用户名',
  `INPUT_TABLE_EN_NAME` varchar(100) DEFAULT NULL COMMENT '入参-表英文名',
  `INPUT_FIELD_EN_NAME` varchar(100) DEFAULT NULL COMMENT '入参-字段英文名',
  
  -- 响应数据字段（基于接口返回的JSON结构）
  `CODE_ID` varchar(200) DEFAULT NULL COMMENT 'CODE_ID',
  `TABLE_EN_NAME` text DEFAULT NULL COMMENT 'TABLE_EN_NAME',
  `FIELD_EN_NAME` text DEFAULT NULL COMMENT 'FIELD_EN_NAME',
  `CODE_VALUE` varchar(200) DEFAULT NULL COMMENT 'CODE_VALUE',
  `CODE_NAME` text DEFAULT NULL COMMENT 'CODE_NAME',
  `CODE_DESC` text DEFAULT NULL COMMENT 'CODE_DESC',
  `PARENT_CODE` varchar(200) DEFAULT NULL COMMENT 'PARENT_CODE',
  `CODE_LEVEL` varchar(200) DEFAULT NULL COMMENT 'CODE_LEVEL',
  `SORT_ORDER` varchar(200) DEFAULT NULL COMMENT 'SORT_ORDER',
  `IS_ACTIVE` varchar(200) DEFAULT NULL COMMENT 'IS_ACTIVE',
  `CREATE_TIME` varchar(50) DEFAULT NULL COMMENT 'CREATE_TIME',
  `FIELD_UPDATE_TIME` varchar(50) DEFAULT NULL COMMENT 'FIELD_UPDATE_TIME',
  
  -- 系统字段
  `RUN_IP` varchar(50) DEFAULT NULL COMMENT '运行IP',
  `REQUEST_ID` varchar(100) DEFAULT NULL COMMENT '请求ID',
  `RETURN_CODE` varchar(10) DEFAULT NULL COMMENT '返回码',
  `RETURN_MSG` varchar(200) DEFAULT NULL COMMENT '返回消息',
  `USER_MSG` varchar(200) DEFAULT NULL COMMENT '用户消息',
  `DETAIL_MSG` text DEFAULT NULL COMMENT '详细消息',
  
  -- 元数据字段
  `import_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_import_time` (`import_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='综合查询表';
