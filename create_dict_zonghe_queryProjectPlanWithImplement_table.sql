-- 创建综合查询查询项目计划及实施情况表
-- 对应接口：saleCenterApp//preparation/queryProjectPlanWithImplement

DROP TABLE IF EXISTS `dict_zonghe_queryProjectPlanWithImplement`;

CREATE TABLE IF NOT EXISTS `dict_zonghe_queryProjectPlanWithImplement` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  
  -- 入参字段（来源于源数据表）
  `INPUT_LOGIN_NO` varchar(100) DEFAULT NULL COMMENT '入参-登录用户名',
  `INPUT_PROJECT_ID` varchar(100) DEFAULT NULL COMMENT '入参-项目ID（来源：v_distinct_project_id视图或sign_data_detail表项目编码字段）',
  
  -- 响应数据字段（基于接口返回的JSON结构）
  `IMPLEMENT_PLAN_ID` varchar(200) DEFAULT NULL COMMENT 'IMPLEMENT_PLAN_ID',
  `PLAN_ID` varchar(200) DEFAULT NULL COMMENT 'PLAN_ID',
  `PLAN_TYPE` varchar(200) DEFAULT NULL COMMENT 'PLAN_TYPE',
  `PLAN_NAME` text DEFAULT NULL COMMENT 'PLAN_NAME',
  `PLAN_DESC` text DEFAULT NULL COMMENT 'PLAN_DESC',
  `PLAN_DEAL_STAFF` varchar(200) DEFAULT NULL COMMENT 'PLAN_DEAL_STAFF',
  `PLAN_DEAL_NAME` text DEFAULT NULL COMMENT 'PLAN_DEAL_NAME',
  `REQUIRE_START_DATE` varchar(50) DEFAULT NULL COMMENT 'REQUIRE_START_DATE',
  `REQUIRE_FINISH_DATE` varchar(50) DEFAULT NULL COMMENT 'REQUIRE_FINISH_DATE',
  `IMPLEMENT_STATUS` varchar(200) DEFAULT NULL COMMENT 'IMPLEMENT_STATUS',
  `IMPLEMENT_STATUS_DESC` text DEFAULT NULL COMMENT 'IMPLEMENT_STATUS_DESC',
  `ACT_FINSH_TIME` varchar(50) DEFAULT NULL COMMENT 'ACT_FINSH_TIME',
  `PROGRESS_RATE` varchar(200) DEFAULT NULL COMMENT 'PROGRESS_RATE',
  `DELIVERY_TYPE` varchar(200) DEFAULT NULL COMMENT 'DELIVERY_TYPE',
  `DELIVERY_TYPE_DESC` text DEFAULT NULL COMMENT 'DELIVERY_TYPE_DESC',
  `CREATE_DATE` varchar(50) DEFAULT NULL COMMENT 'CREATE_DATE',
  `PLAN_TYPE_DESC` text DEFAULT NULL COMMENT 'PLAN_TYPE_DESC',
  `PLAN_LEVEL` varchar(200) DEFAULT NULL COMMENT 'PLAN_LEVEL',
  `PARENT_IMPLEMENT_PLAN_ID` varchar(200) DEFAULT NULL COMMENT 'PARENT_IMPLEMENT_PLAN_ID',
  `STAFF_TYPE` varchar(200) DEFAULT NULL COMMENT 'STAFF_TYPE',
  `PLAN_MODE` varchar(200) DEFAULT NULL COMMENT 'PLAN_MODE',
  `SUBJECT_TYPE` varchar(200) DEFAULT NULL COMMENT 'SUBJECT_TYPE',
  `SUBJECT_TYPE_DESC` text DEFAULT NULL COMMENT 'SUBJECT_TYPE_DESC',
  
  -- 系统字段
  `RUN_IP` varchar(50) DEFAULT NULL COMMENT '运行IP',
  `REQUEST_ID` varchar(100) DEFAULT NULL COMMENT '请求ID',
  `RETURN_CODE` varchar(10) DEFAULT NULL COMMENT '返回码',
  `RETURN_MSG` varchar(200) DEFAULT NULL COMMENT '返回消息',
  `USER_MSG` varchar(200) DEFAULT NULL COMMENT '用户消息',
  `DETAIL_MSG` text DEFAULT NULL COMMENT '详细消息',
  
  -- 元数据字段
  `import_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_input_project_id` (`INPUT_PROJECT_ID`),
  KEY `idx_import_time` (`import_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='综合查询{interface['chinese_name']}表';
