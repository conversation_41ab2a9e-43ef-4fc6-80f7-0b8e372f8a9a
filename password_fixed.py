#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正版密码加密实现
基于对 JavaScript 代码的深入分析
"""

import hashlib
import base64
from urllib.parse import quote
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5, DES
from Crypto.Util.Padding import pad
import struct


# RSA 公钥
PUBLIC_KEY_PEM = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvEr5to90Z5w5+vZ+TIJw
uNExLBuYBKgCvKZll85RAJmbwCMuDTBU18XB5RBerd6c/CUqnrYoxoRjaHGdpOIA
VsHOlxloR3s9Y9/0EUpjpKKfzSsLlUp9N5bHbsoImmJo5+3Hgxpquv/6MCdjTqZ8
P7Uwjdzg7XYMSeBZzEzJ2vIFctjUF4kQWX03ljclLHPpn0mZYQ4Ue5afMv/xuBj5
8BnKl3LLW2mTIMohNkfIqivNnWn3fF6/TJLaI0GMxDBese3QRaJWSDnjSRrMlRJF
NnvB5+AGoPHMfpekqde1t02Zj+MiwFupe/Pm4JkqC5TYEU+Dq7LIZ7LT4imafZR2
0QIDAQAB
-----END PUBLIC KEY-----"""

# 初始化 RSA
rsa_key = RSA.import_key(PUBLIC_KEY_PEM)
rsa_cipher = PKCS1_v1_5.new(rsa_key)


def md5_hash(text: str) -> str:
    """MD5 哈希"""
    return hashlib.md5(text.encode('utf-8')).hexdigest()


def rsa_encrypt_to_base64(text: str) -> str:
    """
    RSA 加密并转换为 base64 字符串
    模拟 JavaScript 中 I.encrypt() 的行为
    """
    text_bytes = text.encode('utf-8')
    encrypted = rsa_cipher.encrypt(text_bytes)
    # 转换为 base64 字符串，这可能是 JavaScript 中的行为
    return base64.b64encode(encrypted).decode('utf-8')


def des_encrypt_cryptojs_style(data_str: str, key_str: str) -> str:
    """
    模拟 CryptoJS 的 DES 加密行为
    
    在 JavaScript 中：
    - C.a.enc.Utf8.parse(t) 将字符串转换为 WordArray
    - C.a.DES.encrypt(e, n, a) 对数据进行 DES 加密
    """
    # 处理密钥：CryptoJS 中 Utf8.parse() 的行为
    # 取前8个字节作为 DES 密钥
    key_bytes = key_str.encode('utf-8')[:8]
    if len(key_bytes) < 8:
        key_bytes = key_bytes.ljust(8, b'\x00')
    
    # 处理数据：将 base64 字符串转换为字节
    try:
        data_bytes = base64.b64decode(data_str)
    except:
        # 如果不是有效的 base64，直接使用字符串
        data_bytes = data_str.encode('utf-8')
    
    # DES 加密
    cipher = DES.new(key_bytes, DES.MODE_ECB)
    
    # PKCS7 填充
    padded_data = pad(data_bytes, DES.block_size)
    
    # 加密
    encrypted = cipher.encrypt(padded_data)
    
    # 返回 base64 编码的结果
    return base64.b64encode(encrypted).decode('utf-8')


def password_encrypt_v2(password: str, login_key: str) -> str:
    """
    修正版密码加密函数
    更准确地模拟 JavaScript 的行为
    """
    print(f"🔐 开始加密密码: {password}")
    print(f"🔑 登录密钥: {login_key}")
    
    # 步骤1: MD5 哈希
    md5_result = md5_hash(password)
    print(f"1️⃣ MD5 哈希: {md5_result}")
    
    # 步骤2: RSA 加密
    rsa_result = rsa_encrypt_to_base64(md5_result)
    print(f"2️⃣ RSA 加密: {rsa_result[:50]}...")
    
    # 步骤3: DES 加密
    des_result = des_encrypt_cryptojs_style(rsa_result, login_key)
    print(f"3️⃣ DES 加密: {des_result[:50]}...")
    
    # 步骤4: URL 编码
    final_result = quote(des_result)
    print(f"4️⃣ URL 编码: {final_result[:50]}...")
    
    return final_result


def test_alternative_approach():
    """测试另一种方法：直接使用字节数据"""
    print("\n=== 测试替代方法 ===")
    
    password = "Dewen@428"
    login_key = "1751645934512463581"
    
    # MD5
    md5_result = md5_hash(password)
    print(f"MD5: {md5_result}")
    
    # RSA 加密（返回字节）
    md5_bytes = md5_result.encode('utf-8')
    rsa_encrypted_bytes = rsa_cipher.encrypt(md5_bytes)
    print(f"RSA 字节长度: {len(rsa_encrypted_bytes)}")
    
    # 直接对 RSA 字节进行 DES 加密
    key_bytes = login_key.encode('utf-8')[:8].ljust(8, b'\x00')
    cipher = DES.new(key_bytes, DES.MODE_ECB)
    padded_rsa = pad(rsa_encrypted_bytes, DES.block_size)
    des_encrypted = cipher.encrypt(padded_rsa)
    
    # Base64 编码
    b64_result = base64.b64encode(des_encrypted).decode('utf-8')
    print(f"DES+Base64: {b64_result[:50]}...")
    
    # URL 编码
    final = quote(b64_result)
    print(f"最终结果: {final[:50]}...")
    print(f"长度: {len(final)}")
    
    return final


def test_hex_approach():
    """测试十六进制方法"""
    print("\n=== 测试十六进制方法 ===")
    
    password = "Dewen@428"
    login_key = "1751645934512463581"
    
    # MD5
    md5_result = md5_hash(password)
    print(f"MD5: {md5_result}")
    
    # RSA 加密
    md5_bytes = md5_result.encode('utf-8')
    rsa_encrypted = rsa_cipher.encrypt(md5_bytes)
    
    # 转换为十六进制字符串
    hex_str = rsa_encrypted.hex()
    print(f"RSA 十六进制: {hex_str[:50]}...")
    
    # DES 加密十六进制字符串
    key_bytes = login_key.encode('utf-8')[:8].ljust(8, b'\x00')
    cipher = DES.new(key_bytes, DES.MODE_ECB)
    hex_bytes = hex_str.encode('utf-8')
    padded_hex = pad(hex_bytes, DES.block_size)
    des_encrypted = cipher.encrypt(padded_hex)
    
    # Base64 编码
    b64_result = base64.b64encode(des_encrypted).decode('utf-8')
    final = quote(b64_result)
    print(f"最终结果: {final[:50]}...")
    print(f"长度: {len(final)}")
    
    return final


if __name__ == "__main__":
    # 测试参数
    test_password = "Dewen@428"
    test_login_key = "1751645934512463581"
    expected = "VDHesuGD7wIGwc4g5lGu8To/DJCbvhoXLNhgvlYLOvijLZkSzAqarPuovKdSjaUB7D3zY+aor2pR45MJ6ByKN48SSfgtNCOBI+WUcxVza+PAXEQFqdN0yVo2/Suq9I5X/LPMn3FJ15v38b6rElgQ129VRDtYXDUB5Gv+VLUZdCGUNKB8EtVCBC14AhMZAqcFSVqflT2OKF+Xn/oW6aJvogh53WmseSQHbaijwBDda9O4Y2GIKKewrnVX8TMzZ4VO14PUsZYDh3Hx2gyTvTpxta6jhXrIUYGxGfj0I5KBW6O2kDS5uD789urnkxA39YkcO3Jq5Egy0XuIaxyMi2uGeIoUev0en9Z1pSMU9ryi1zKyW+pJg9wRKDrGdAPKste1nYsLyiv7CNN8KNh3m3r/lbLXQ9mFouZEhHuBMcExh1SdNeXmod3Asbcw9aqeL9mmURgNiHUZUZcGf4yYWLScRA=="
    
    print("=== 测试修正版实现 ===")
    result1 = password_encrypt_v2(test_password, test_login_key)
    print(f"结果1匹配: {'✅' if result1 == expected else '❌'}")
    
    result2 = test_alternative_approach()
    print(f"结果2匹配: {'✅' if result2 == expected else '❌'}")
    
    result3 = test_hex_approach()
    print(f"结果3匹配: {'✅' if result3 == expected else '❌'}")
    
    print(f"\n期望长度: {len(expected)}")
    print(f"结果1长度: {len(result1)}")
    print(f"结果2长度: {len(result2)}")
    print(f"结果3长度: {len(result3)}")
