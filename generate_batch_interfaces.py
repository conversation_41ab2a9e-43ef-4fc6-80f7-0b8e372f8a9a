#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
分批生成剩余接口（每次处理10个）
"""

import re
import os

def extract_interface_from_md(interface_num):
    """从接口分析结果.md提取单个接口信息"""
    try:
        with open('dict_romte/接口分析结果.md', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找接口
        pattern = rf'## {interface_num}\. (.+?)\n\*\*中文名称\*\*: (.+?)\n'
        match = re.search(pattern, content)
        
        if match:
            api_path = match.group(1).strip()
            chinese_name = match.group(2).strip()
            
            # 简单判断是否需要PROJECT_ID
            needs_project_id = 'project' in api_path.lower() or 'Project' in api_path
            
            return {
                'number': interface_num,
                'api_path': api_path,
                'chinese_name': chinese_name,
                'needs_project_id': needs_project_id
            }
    except Exception as e:
        print(f"[错误] 提取接口 {interface_num} 失败: {e}")
    
    return None

def generate_simple_crawler(interface):
    """生成简化的爬虫程序"""
    api_name = interface['api_path'].split('/')[-1]
    table_name = f'dict_zonghe_{api_name}'
    
    template = f'''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
{interface['chinese_name']}爬虫程序
接口：{interface['api_path']}
"""

import sys
import os
import json
import requests
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
import pymysql

sys.path.append(os.path.join(os.path.dirname(__file__), 'dict_romte'))
from dict_romte.config import get_db_config, get_login_credentials

DB_CONFIG = get_db_config('default')
API_URL = "https://dict.gmcc.net:30722/dictWeb/gatewayService/{interface['api_path']}"

def load_cookies():
    try:
        with open('cookies.txt', 'r', encoding='utf-8') as f:
            cookie_data = json.load(f)
        cookies = {{}}
        for cookie in cookie_data.get('cookies', []):
            cookies[cookie['name']] = cookie['value']
        return cookies
    except:
        return {{}}

def get_test_project_ids():
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        cursor.execute("SELECT project_id FROM v_distinct_project_id LIMIT 5")
        return [row[0] for row in cursor.fetchall()]
    except:
        return ["TEST_PROJECT"]
    finally:
        if 'cursor' in locals(): cursor.close()
        if 'conn' in locals(): conn.close()

def query_api(project_id, cookies, login_no):
    try:
        cookie_str = "; ".join([f"{{k}}={{v}}" for k, v in cookies.items()])
        headers = {{
            'Content-Type': 'application/json',
            'Cookie': cookie_str
        }}
        
        body = {{}}
        if {str(interface['needs_project_id']).lower()}:
            body["PROJECT_ID"] = project_id
        
        data = {{
            "ROOT": {{
                "HEADER": {{"OPR_INFO": {{"LOGIN_NO": login_no}}}},
                "BODY": body
            }}
        }}
        
        response = requests.post(API_URL, headers=headers, json=data, timeout=30, verify=False)
        if response.status_code == 200:
            result = response.json()
            return_code = result.get('ROOT', {{}}).get('BODY', {{}}).get('RETURN_CODE', '')
            if return_code == '0':
                return result.get('ROOT', {{}}).get('BODY', {{}}).get('OUT_DATA', {{}})
        return None
    except Exception as e:
        print(f"[错误] API调用失败: {{e}}")
        return None

def save_simple_data(data_list):
    if not data_list:
        return
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 创建简单表（如果不存在）
        create_sql = f"""
        CREATE TABLE IF NOT EXISTS {table_name} (
            id int AUTO_INCREMENT PRIMARY KEY,
            project_id varchar(100),
            response_data text,
            import_time timestamp DEFAULT CURRENT_TIMESTAMP
        )
        """
        cursor.execute(create_sql)
        
        # 清空并插入数据
        cursor.execute(f"DELETE FROM {table_name}")
        
        for data in data_list:
            cursor.execute(
                f"INSERT INTO {table_name} (project_id, response_data) VALUES (%s, %s)",
                (data['project_id'], json.dumps(data['response'], ensure_ascii=False))
            )
        
        conn.commit()
        print(f"[成功] 保存了 {{len(data_list)}} 条数据到 {table_name}")
        
    except Exception as e:
        print(f"[错误] 保存数据失败: {{e}}")
    finally:
        if 'cursor' in locals(): cursor.close()
        if 'conn' in locals(): conn.close()

def main():
    print(f"开始测试接口: {interface['chinese_name']}")
    
    cookies = load_cookies()
    if not cookies:
        print("[错误] 无法加载Cookie")
        return
    
    login_no, _, _ = get_login_credentials()
    project_ids = get_test_project_ids()
    
    data_list = []
    for project_id in project_ids:
        result = query_api(project_id, cookies, login_no)
        if result:
            data_list.append({{'project_id': project_id, 'response': result}})
            print(f"[成功] 项目 {{project_id}} 获取数据成功")
        else:
            print(f"[失败] 项目 {{project_id}} 获取数据失败")
    
    save_simple_data(data_list)
    print(f"接口测试完成: {interface['chinese_name']}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "-all":
        main()
    else:
        print("使用方法: python {table_name}.py -all")
'''
    
    return template

def main():
    """主函数"""
    print("=" * 60)
    print("分批生成剩余接口（每次10个）")
    print("=" * 60)
    
    # 获取已有的爬虫程序数量
    existing_files = [f for f in os.listdir('.') if f.startswith('dict_zonghe_') and f.endswith('.py')]
    print(f"当前已有 {len(existing_files)} 个爬虫程序")
    
    # 确定要生成的接口范围
    start_num = 45  # 从第45个接口开始
    end_num = 54    # 到第54个接口（生成10个）
    
    print(f"将生成接口 {start_num} 到 {end_num}")
    
    generated_count = 0
    
    for interface_num in range(start_num, end_num + 1):
        print(f"\\n处理接口 {interface_num}...")
        
        # 提取接口信息
        interface = extract_interface_from_md(interface_num)
        if not interface:
            print(f"  跳过接口 {interface_num}（提取失败）")
            continue
        
        api_name = interface['api_path'].split('/')[-1]
        filename = f"dict_zonghe_{api_name}.py"
        
        # 检查是否已存在
        if os.path.exists(filename):
            print(f"  跳过接口 {interface_num}（文件已存在）")
            continue
        
        try:
            # 生成爬虫程序
            code = generate_simple_crawler(interface)
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(code)
            
            print(f"  ✅ 生成成功: {filename}")
            print(f"     接口: {interface['chinese_name']}")
            generated_count += 1
            
        except Exception as e:
            print(f"  ❌ 生成失败: {e}")
    
    print(f"\\n=" * 60)
    print(f"批量生成完成，共生成 {generated_count} 个爬虫程序")
    print("=" * 60)
    
    # 检查总数
    all_files = [f for f in os.listdir('.') if f.startswith('dict_zonghe_') and f.endswith('.py')]
    print(f"现在总共有 {len(all_files)} 个爬虫程序")

if __name__ == "__main__":
    main()
