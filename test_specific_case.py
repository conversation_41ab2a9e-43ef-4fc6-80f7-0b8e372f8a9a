#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特定的密码加密用例
验证 Python 实现是否与 JavaScript 结果一致
"""

from password_utils import encrypt_password, md5_hash, rsa_encrypt, des_encrypt
from urllib.parse import quote, unquote
import base64


def test_specific_case():
    """测试用户提供的特定用例"""
    # 用户提供的测试参数
    test_password = "Dewen@428"
    test_login_key = "1751645934512463581"
    
    # 期望的加密结果（去除空格）
    expected_result = "VDHesuGD7wIGwc4g5lGu8To/DJCbvhoXLNhgvlYLOvijLZkSzAqarPuovKdSjaUB7D3zY+aor2pR45MJ6ByKN48SSfgtNCOBI+WUcxVza+PAXEQFqdN0yVo2/Suq9I5X/LPMn3FJ15v38b6rElgQ129VRDtYXDUB5Gv+VLUZdCGUNKB8EtVCBC14AhMZAqcFSVqflT2OKF+Xn/oW6aJvogh53WmseSQHbaijwBDda9O4Y2GIKKewrnVX8TMzZ4VO14PUsZYDh3Hx2gyTvTpxta6jhXrIUYGxGfj0I5KBW6O2kDS5uD789urnkxA39YkcO3Jq5Egy0XuIaxyMi2uGeIoUev0en9Z1pSMU9ryi1zKyW+pJg9wRKDrGdAPKste1nYsLyiv7CNN8KNh3m3r/lbLXQ9mFouZEhHuBMcExh1SdNeXmod3Asbcw9aqeL9mmURgNiHUZUZcGf4yYWLScRA=="
    
    print("=== 特定用例测试 ===")
    print(f"测试密码: {test_password}")
    print(f"登录密钥: {test_login_key}")
    print(f"期望结果长度: {len(expected_result)}")
    print()
    
    # 执行加密
    print("🔐 执行 Python 加密...")
    actual_result = encrypt_password(test_password, test_login_key, debug=True)
    
    print()
    print("=== 结果对比 ===")
    print(f"期望结果: {expected_result}")
    print(f"实际结果: {actual_result}")
    print(f"长度对比: 期望={len(expected_result)}, 实际={len(actual_result)}")
    
    # 检查是否匹配
    if actual_result == expected_result:
        print("✅ 结果完全匹配！")
        return True
    else:
        print("❌ 结果不匹配")
        
        # 尝试 URL 解码对比
        try:
            decoded_expected = unquote(expected_result)
            decoded_actual = unquote(actual_result)
            print(f"URL解码后匹配: {'✅ 是' if decoded_expected == decoded_actual else '❌ 否'}")
            
            if decoded_expected == decoded_actual:
                print("💡 URL编码方式不同，但解码后内容相同")
                return True
        except Exception as e:
            print(f"URL解码失败: {e}")
        
        # 详细分析差异
        analyze_differences(expected_result, actual_result)
        return False


def analyze_differences(expected, actual):
    """分析两个结果的差异"""
    print()
    print("=== 差异分析 ===")
    
    # 长度对比
    print(f"长度差异: {len(actual) - len(expected)}")
    
    # 字符级对比
    min_len = min(len(expected), len(actual))
    diff_positions = []
    
    for i in range(min_len):
        if expected[i] != actual[i]:
            diff_positions.append(i)
    
    if diff_positions:
        print(f"发现 {len(diff_positions)} 个字符差异")
        for i, pos in enumerate(diff_positions[:10]):  # 只显示前10个差异
            print(f"  位置 {pos}: 期望='{expected[pos]}', 实际='{actual[pos]}'")
        if len(diff_positions) > 10:
            print(f"  ... 还有 {len(diff_positions) - 10} 个差异")
    
    # 检查是否是编码问题
    try:
        # 尝试不同的解码方式
        expected_decoded = unquote(expected)
        actual_decoded = unquote(actual)
        
        print(f"URL解码后长度: 期望={len(expected_decoded)}, 实际={len(actual_decoded)}")
        
        # 检查 base64 内容
        try:
            expected_b64 = base64.b64decode(expected_decoded + '==')
            actual_b64 = base64.b64decode(actual_decoded + '==')
            print(f"Base64解码后长度: 期望={len(expected_b64)}, 实际={len(actual_b64)}")
        except:
            print("Base64解码失败")
            
    except Exception as e:
        print(f"解码分析失败: {e}")


def debug_step_by_step():
    """逐步调试加密过程"""
    print("\n=== 逐步调试 ===")
    
    test_password = "Dewen@428"
    test_login_key = "1751645934512463581"
    
    # 步骤1: MD5
    md5_result = md5_hash(test_password)
    print(f"1. MD5 哈希: {md5_result}")
    
    # 步骤2: RSA
    rsa_result = rsa_encrypt(md5_result)
    print(f"2. RSA 加密: {rsa_result[:50]}...")
    print(f"   RSA 结果长度: {len(rsa_result)}")
    
    # 步骤3: DES
    des_result = des_encrypt(rsa_result, test_login_key)
    print(f"3. DES 加密: {des_result[:50]}...")
    print(f"   DES 结果长度: {len(des_result)}")
    
    # 步骤4: URL编码
    final_result = quote(des_result)
    print(f"4. URL 编码: {final_result[:50]}...")
    print(f"   最终结果长度: {len(final_result)}")
    
    return final_result


def test_different_approaches():
    """尝试不同的实现方法"""
    print("\n=== 尝试不同方法 ===")
    
    test_password = "Dewen@428"
    test_login_key = "1751645934512463581"
    
    # 方法1: 标准实现
    result1 = encrypt_password(test_password, test_login_key)
    print(f"方法1 (标准): {result1[:50]}...")
    
    # 方法2: 不同的URL编码
    from password_utils import md5_hash, rsa_encrypt, des_encrypt
    md5_result = md5_hash(test_password)
    rsa_result = rsa_encrypt(md5_result)
    des_result = des_encrypt(rsa_result, test_login_key)
    
    # 尝试不同的编码方式
    import urllib.parse
    result2 = urllib.parse.quote_plus(des_result)
    print(f"方法2 (quote_plus): {result2[:50]}...")
    
    result3 = urllib.parse.quote(des_result, safe='')
    print(f"方法3 (quote safe=''): {result3[:50]}...")
    
    return [result1, result2, result3]


if __name__ == "__main__":
    # 运行测试
    success = test_specific_case()
    
    if not success:
        print("\n" + "="*60)
        debug_step_by_step()
        
        print("\n" + "="*60)
        test_different_approaches()
        
        print("\n💡 建议:")
        print("1. 检查 JavaScript 版本的具体实现细节")
        print("2. 确认登录密钥的获取方式")
        print("3. 验证 RSA 公钥是否完全一致")
        print("4. 检查 DES 加密的模式和填充方式")
