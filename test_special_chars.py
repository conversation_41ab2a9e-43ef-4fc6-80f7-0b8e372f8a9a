#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特殊字符字段名的插入问题
"""

import pymysql

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'cmcc12345',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

def test_insert():
    """测试插入包含特殊字符的字段"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 创建测试表
        create_sql = """
        CREATE TABLE IF NOT EXISTS test_special_chars (
            id int AUTO_INCREMENT PRIMARY KEY,
            `效益评估_概算__收入利润率（%）` text,
            `效益评估_概算__收入利润率含代收代支%` text
        )
        """
        cursor.execute(create_sql)
        
        # 清空表
        cursor.execute("TRUNCATE TABLE test_special_chars")
        
        # 测试插入
        insert_sql = "INSERT INTO test_special_chars (`效益评估_概算__收入利润率（%）`, `效益评估_概算__收入利润率含代收代支%`) VALUES (%s, %s)"
        
        test_data = ("10.5", "12.3")
        
        print("测试插入SQL:", insert_sql)
        print("测试数据:", test_data)
        
        cursor.execute(insert_sql, test_data)
        conn.commit()
        
        print("✅ 插入成功!")
        
        # 验证数据
        cursor.execute("SELECT * FROM test_special_chars")
        result = cursor.fetchall()
        print("查询结果:", result)
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    test_insert()
