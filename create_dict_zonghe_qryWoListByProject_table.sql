-- 创建综合查询按项目查询工单列表表
-- 对应接口：iom-app-svc/iom/api/wo/qryWoListByProject

DROP TABLE IF EXISTS `dict_zonghe_qryWoListByProject`;

CREATE TABLE IF NOT EXISTS `dict_zonghe_qryWoListByProject` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  
  -- 入参字段（来源于源数据表）
  `INPUT_LOGIN_NO` varchar(100) DEFAULT NULL COMMENT '入参-登录用户名',
  `INPUT_PROJECT_ID` varchar(100) DEFAULT NULL COMMENT '入参-项目ID',
  `INPUT_COMMODITY_ID` varchar(100) DEFAULT NULL COMMENT '入参-商品ID',
  `INPUT_QRY_TYPE` varchar(50) DEFAULT NULL COMMENT '入参-查询类型',
  `INPUT_CURR_PAGE` int DEFAULT NULL COMMENT '入参-当前页',
  `INPUT_PAGE_SIZE` int DEFAULT NULL COMMENT '入参-页大小',
  `INPUT_PAGE_NUM` int DEFAULT NULL COMMENT '入参-页码',
  
  -- 响应数据字段（基于接口返回的JSON结构）
  `WO_ID` varchar(200) DEFAULT NULL COMMENT 'WO_ID',
  `WO_NO` varchar(200) DEFAULT NULL COMMENT 'WO_NO',
  `WO_TYPE` varchar(200) DEFAULT NULL COMMENT 'WO_TYPE',
  `WO_TYPE_DESC` text DEFAULT NULL COMMENT 'WO_TYPE_DESC',
  `WO_STATUS` varchar(200) DEFAULT NULL COMMENT 'WO_STATUS',
  `WO_STATUS_DESC` text DEFAULT NULL COMMENT 'WO_STATUS_DESC',
  `PROJECT_ID` varchar(200) DEFAULT NULL COMMENT 'PROJECT_ID',
  `COMMODITY_ID` varchar(200) DEFAULT NULL COMMENT 'COMMODITY_ID',
  `COMMODITY_NAME` text DEFAULT NULL COMMENT 'COMMODITY_NAME',
  `CREATE_TIME` varchar(50) DEFAULT NULL COMMENT 'CREATE_TIME',
  `ASSIGN_TIME` varchar(50) DEFAULT NULL COMMENT 'ASSIGN_TIME',
  `COMPLETE_TIME` varchar(50) DEFAULT NULL COMMENT 'COMPLETE_TIME',
  `ASSIGN_STAFF` varchar(200) DEFAULT NULL COMMENT 'ASSIGN_STAFF',
  `ASSIGN_STAFF_NAME` text DEFAULT NULL COMMENT 'ASSIGN_STAFF_NAME',
  `COMPLETE_STAFF` varchar(200) DEFAULT NULL COMMENT 'COMPLETE_STAFF',
  `COMPLETE_STAFF_NAME` text DEFAULT NULL COMMENT 'COMPLETE_STAFF_NAME',
  `WO_DESC` text DEFAULT NULL COMMENT 'WO_DESC',
  `PRIORITY` varchar(200) DEFAULT NULL COMMENT 'PRIORITY',
  `PRIORITY_DESC` text DEFAULT NULL COMMENT 'PRIORITY_DESC',
  `ESTIMATED_HOURS` decimal(15,2) DEFAULT NULL COMMENT 'ESTIMATED_HOURS',
  `ACTUAL_HOURS` decimal(15,2) DEFAULT NULL COMMENT 'ACTUAL_HOURS',
  
  -- 系统字段
  `RUN_IP` varchar(50) DEFAULT NULL COMMENT '运行IP',
  `REQUEST_ID` varchar(100) DEFAULT NULL COMMENT '请求ID',
  `RETURN_CODE` varchar(10) DEFAULT NULL COMMENT '返回码',
  `RETURN_MSG` varchar(200) DEFAULT NULL COMMENT '返回消息',
  `USER_MSG` varchar(200) DEFAULT NULL COMMENT '用户消息',
  `DETAIL_MSG` text DEFAULT NULL COMMENT '详细消息',
  
  -- 元数据字段
  `import_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_import_time` (`import_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='综合查询表';
