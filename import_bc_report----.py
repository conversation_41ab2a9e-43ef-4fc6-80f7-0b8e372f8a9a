def import_bc_report_data(conn, filepath, filename):
    """导入BC融合业务日报表数据到数据库"""
    try:
        print(f"[信息] 正在导入BC融合业务日报表数据: {filepath}")
        
        # 检查文件是否已经导入过
        if check_file_already_imported(conn, filename):
            print(f"[信息] 文件 {filename} 已经导入过数据库，跳过导入")
            # 将文件移动到已导入文件夹
            move_to_imported_folder(filepath, filename)
            return True

        # 创建SQLAlchemy引擎
        engine_url = f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?charset={DB_CONFIG['charset']}"
        engine = create_engine(engine_url)

        # 读取Excel文件中的"工号级"sheet，跳过前几行，直到找到包含实际列名的行
        print("[信息] 正在读取'工号级'sheet...")
        
        # 先读取前10行，查找包含"员工编号"的行作为表头
        header_df = pd.read_excel(filepath, sheet_name="工号级", nrows=10)
        header_row = None
        
        for i in range(len(header_df)):
            row_values = header_df.iloc[i].astype(str).tolist()
            if '员工编号' in row_values:
                header_row = i
                print(f"[信息] 在第 {i+1} 行找到表头")
                break
        
        if header_row is None:
            print("[错误] 未找到包含'员工编号'的表头行")
            return False
        
        # 使用找到的表头行重新读取数据
        df = pd.read_excel(filepath, sheet_name="工号级", header=header_row)
        
        # 打印当前DataFrame的列，帮助调试
        print("[信息] Excel文件中的列:", df.columns.tolist())
        
        # 清理列名（替换特殊字符）
        df.columns = [col.replace('/', '_').replace('：', '_').replace('（', '_').replace('）', '') for col in df.columns]
        
        # 从文件名中提取日期
        date_match = re.search(r'(\d{8})', filename)
        if date_match:
            file_date = date_match.group(1)
            # 转换为日期格式 YYYY-MM-DD
            file_date = f"{file_date[:4]}-{file_date[4:6]}-{file_date[6:8]}"
            # 添加日期列
            df['日期'] = file_date
            # 提取月份
            df['月份'] = file_date[:7]  # YYYY-MM
        
        # 添加文件名列，用于追踪数据来源
        df['文件名'] = filename
        
        # 添加导入时间列
        import_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        df['导入时间'] = import_time
        
        # 移除空行（所有值都是NaN的行）
        df = df.dropna(how='all')
        
        # 检查是否有必要的列
        required_columns = ['员工编号', '姓名', '部门_分公司', '网格', 'BOSS工号']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"[错误] 缺少必要的列: {', '.join(missing_columns)}")
            # 尝试映射列名
            column_mapping = {
                '员工编号': ['员工编号', '员工工号', '工号'],
                '姓名': ['姓名', '员工姓名', '姓名'],
                '部门_分公司': ['部门/分公司', '部门_分公司', '分公司', '部门'],
                '网格': ['网格', '网格名称'],
                'BOSS工号': ['BOSS工号', 'BOSS号', '工号']
            }
            
            for db_col, possible_names in column_mapping.items():
                if db_col in missing_columns:
                    for excel_col in df.columns:
                        if any(name in excel_col for name in possible_names):
                            print(f"[信息] 将列 '{excel_col}' 映射到 '{db_col}'")
                            df = df.rename(columns={excel_col: db_col})
                            break
        
        # 导入数据到bc_channeng_gonghao表
        if len(df) > 0:
            # 将DataFrame中的列名与数据库表的列名进行匹配
            cursor = conn.cursor()
            cursor.execute("DESCRIBE bc_channeng_gonghao")
            table_columns = [column[0] for column in cursor.fetchall()]
            cursor.close()
            
            # 检查是否有不匹配的列
            df_columns = set(df.columns)
            db_columns = set(table_columns)
            
            # 移除DataFrame中存在但数据库中不存在的列
            columns_to_drop = df_columns - db_columns
            if columns_to_drop:
                print(f"[警告] 以下列在Excel中存在但在数据库中不存在，将被忽略: {', '.join(columns_to_drop)}")
                df = df.drop(columns=columns_to_drop)
            
            # 检查是否有数据库中存在但DataFrame中不存在的列
            missing_db_columns = db_columns - df_columns
            if missing_db_columns:
                print(f"[警告] 以下列在数据库中存在但在Excel中不存在，将设为NULL: {', '.join(missing_db_columns)}")
                for col in missing_db_columns:
                    if col not in ['id', '导入时间', '文件名', '日期', '月份']:  # 跳过自动生成的列
                        df[col] = None
            
            # 打印前5行数据，帮助调试
            print("[信息] 数据预览:")
            print(df.head().to_string())
            
            # 导入数据
            df.to_sql('bc_channeng_gonghao', engine, if_exists='append', index=False)
            print(f"[成功] 已导入 {len(df)} 条BC融合业务工号级数据")
        else:
            print("[警告] '工号级'sheet中没有数据")
            
        # 记录导入信息
        cursor = conn.cursor()
        
        # 记录BC融合业务工号级数据导入
        sql = """
        INSERT INTO import_logs (file_name, table_name, record_count, import_time)
        VALUES (%s, %s, %s, %s)
        """
        cursor.execute(sql, (filename, 'bc_channeng_gonghao', len(df), import_time))
        conn.commit()
        cursor.close()
        
        # 将已导入的Excel文件移动到inserted2db文件夹
        move_to_imported_folder(filepath, filename)
        
        return True
    except Exception as e:
        print(f"[错误] 导入BC融合业务日报表数据失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False