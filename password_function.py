#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接对应 JavaScript password 函数的 Python 实现
"""

import hashlib
import base64
from urllib.parse import quote
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5, DES
from Crypto.Util.Padding import pad


# RSA 公钥（对应 JavaScript 中的 I）
PUBLIC_KEY_PEM = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvEr5to90Z5w5+vZ+TIJw
uNExLBuYBKgCvKZll85RAJmbwCMuDTBU18XB5RBerd6c/CUqnrYoxoRjaHGdpOIA
VsHOlxloR3s9Y9/0EUpjpKKfzSsLlUp9N5bHbsoImmJo5+3Hgxpquv/6MCdjTqZ8
P7Uwjdzg7XYMSeBZzEzJ2vIFctjUF4kQWX03ljclLHPpn0mZYQ4Ue5afMv/xuBj5
8BnKl3LLW2mTIMohNkfIqivNnWn3fF6/TJLaI0GMxDBese3QRaJWSDnjSRrMlRJF
NnvB5+AGoPHMfpekqde1t02Zj+MiwFupe/Pm4JkqC5TYEU+Dq7LIZ7LT4imafZR2
0QIDAQAB
-----END PUBLIC KEY-----"""

# 初始化 RSA 加密器
rsa_key = RSA.import_key(PUBLIC_KEY_PEM)
I = PKCS1_v1_5.new(rsa_key)  # 对应 JavaScript 中的 I


def k(text: str) -> str:
    """
    MD5 哈希函数
    对应 JavaScript 中的 k() 函数
    """
    return hashlib.md5(text.encode('utf-8')).hexdigest()


def password(e: str, t: str) -> str:
    """
    密码加密函数 - 直接对应 JavaScript 版本

    Args:
        e: 密码（经过 MD5 哈希）
        t: 登录密钥

    Returns:
        加密后的密码字符串
    """
    # e = I.encrypt(e);
    e_bytes = e.encode('utf-8')
    e_encrypted = I.encrypt(e_bytes)  # 直接使用字节数据，不进行 base64 编码

    # var n = C.a.enc.Utf8.parse(t)
    n = t.encode('utf-8')[:8].ljust(8, b'\x00')  # DES 密钥必须是 8 字节

    # var a = { mode: C.a.mode.ECB, padding: C.a.pad.Pkcs7 }
    # var o = C.a.DES.encrypt(e, n, a);
    cipher = DES.new(n, DES.MODE_ECB)
    e_padded = pad(e_encrypted, DES.block_size)  # 直接对 RSA 加密的字节数据进行填充
    o = base64.b64encode(cipher.encrypt(e_padded)).decode('utf-8')

    # return encodeURIComponent(o).toString()
    return quote(o)


def encrypt_user_password(user_password: str, login_key: str) -> str:
    """
    完整的用户密码加密流程
    对应 JavaScript 中的: password(k()(t.password), o)
    
    Args:
        user_password: 用户输入的原始密码
        login_key: 从服务器获取的登录密钥
        
    Returns:
        最终加密的密码字符串
    """
    # 对应 JavaScript: k()(t.password)
    hashed_password = k(user_password)
    
    # 对应 JavaScript: password(k()(t.password), o)
    encrypted_password = password(hashed_password, login_key)
    
    return encrypted_password


# 测试代码
if __name__ == "__main__":
    # 测试参数
    test_password = "Dewen@428"
    test_login_key = "1751645934512463581"
     #  加密后的password: VDHesuGD7wIGwc4g5lGu8To / DJCbvhoXLNhgvlYLOvijLZkSzAqarPuovKdSjaUB7D3zY + aor2pR45MJ6ByKN48SSfgtNCOBI + WUcxVza + PAXEQFqdN0yVo2 / Suq9I5X / LPMn3FJ15v38b6rElgQ129VRDtYXDUB5Gv + VLUZdCGUNKB8EtVCBC14AhMZAqcFSVqflT2OKF + Xn / oW6aJvogh53WmseSQHbaijwBDda9O4Y2GIKKewrnVX8TMzZ4VO14PUsZYDh3Hx2gyTvTpxta6jhXrIUYGxGfj0I5KBW6O2kDS5uD789urnkxA39YkcO3Jq5Egy0XuIaxyMi2uGeIoUev0en9Z1pSMU9ryi1zKyW + pJg9wRKDrGdAPKste1nYsLyiv7CNN8KNh3m3r / lbLXQ9mFouZEhHuBMcExh1SdNeXmod3Asbcw9aqeL9mmURgNiHUZUZcGf4yYWLScRA ==
    
    print("=== 密码加密测试 ===")
    print(f"原始密码: {test_password}")
    print(f"登录密钥: {test_login_key}")
    
    # 步骤1: MD5 哈希
    md5_result = k(test_password)
    print(f"MD5 哈希: {md5_result}")
    
    # 步骤2: 完整加密
    final_result = encrypt_user_password(test_password, test_login_key)
    print(f"最终结果: {final_result}")
    
    print(f"结果长度: {len(final_result)}")
