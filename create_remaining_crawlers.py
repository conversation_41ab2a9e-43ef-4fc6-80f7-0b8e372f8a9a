#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快速创建剩余的爬虫程序
"""

import os

# 剩余需要创建的爬虫程序
REMAINING_CRAWLERS = [
    {
        'filename': 'dict_zonghe_saleBiddingInfo.py',
        'api_path': 'saleCenterApp//biddingSupport/saleBiddingInfo',
        'description': '查询销售投标信息',
        'table_name': 'dict_zonghe_saleBiddingInfo',
        'needs_project_id': True
    },
    {
        'filename': 'dict_zonghe_qryWoListByProject.py',
        'api_path': 'iom-app-svc/iom/api/wo/qryWoListByProject',
        'description': '按项目查询工单列表',
        'table_name': 'dict_zonghe_qryWoListByProject',
        'needs_project_id': True
    },
    {
        'filename': 'dict_zonghe_getTodo.py',
        'api_path': 'iom-app-svc/iom/api/wo/getTodo',
        'description': '获取待办工单',
        'table_name': 'dict_zonghe_getTodo',
        'needs_project_id': False
    },
    {
        'filename': 'dict_zonghe_queryProjectProdprcDict.py',
        'api_path': 'saleCenterApp/projectImplement/queryProjectProdprcDict',
        'description': '查询项目产品流程字典',
        'table_name': 'dict_zonghe_queryProjectProdprcDict',
        'needs_project_id': False
    },
    {
        'filename': 'dict_zonghe_batchLoadCodeList.py',
        'api_path': 'saleCenterApp/common/dataDictService/batchLoadCodeList',
        'description': '批量加载数据字典代码列表',
        'table_name': 'dict_zonghe_batchLoadCodeList',
        'needs_project_id': False
    },
    {
        'filename': 'dict_zonghe_loadCodeList.py',
        'api_path': 'saleCenterApp/common/dataDictService/loadCodeList',
        'description': '加载数据字典代码列表',
        'table_name': 'dict_zonghe_loadCodeList',
        'needs_project_id': False
    }
]

def create_crawler_template(crawler_info):
    """创建爬虫程序模板"""
    
    template = f'''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
综合查询{crawler_info['description']}爬虫程序
对应接口：{crawler_info['api_path']}
功能：从dict系统获取{crawler_info['description']}并同步到MySQL数据库
"""

import sys
import os
import json
import time
import requests
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
import pymysql
from datetime import datetime

# 添加配置文件路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'dict_romte'))
from dict_romte.config import get_db_config, get_login_credentials

# 数据库配置
DB_CONFIG = get_db_config('default')

# API配置
API_URL = "https://dict.gmcc.net:30722/dictWeb/gatewayService/{crawler_info['api_path']}"

def load_cookies():
    """从cookies.txt文件加载cookie"""
    try:
        with open('cookies.txt', 'r', encoding='utf-8') as f:
            cookie_data = json.load(f)
        
        cookies = {{}}
        for cookie in cookie_data.get('cookies', []):
            cookies[cookie['name']] = cookie['value']
        
        print(f"[信息] 成功加载 {{len(cookies)}} 个Cookie")
        return cookies
    except Exception as e:
        print(f"[错误] 加载Cookie失败: {{e}}")
        return {{}}

def get_project_ids():
    """从v_distinct_project_id视图获取项目ID列表"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 查询v_distinct_project_id视图
        cursor.execute("SELECT project_id FROM v_distinct_project_id LIMIT 10")  # 限制测试数量
        project_ids = [row[0] for row in cursor.fetchall()]
        
        print(f"[信息] 从v_distinct_project_id视图获取到 {{len(project_ids)}} 个项目ID")
        return project_ids
        
    except Exception as e:
        print(f"[错误] 获取项目ID失败: {{e}}")
        # 如果视图不存在，尝试从其他表获取
        try:
            cursor.execute("SELECT DISTINCT `项目编码` FROM sign_data_detail WHERE `项目编码` IS NOT NULL AND `项目编码` != '' LIMIT 10")
            project_ids = [row[0] for row in cursor.fetchall()]
            print(f"[信息] 从sign_data_detail表获取到 {{len(project_ids)}} 个项目ID")
            return project_ids
        except Exception as e2:
            print(f"[错误] 从备用表获取项目ID也失败: {{e2}}")
            return []
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def build_request_headers(cookies):
    """构建请求头"""
    cookie_str = "; ".join([f"{{name}}={{value}}" for name, value in cookies.items()])
    
    headers = {{
        'Accept': 'application/json, text/plain, */*',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json',
        'Host': 'dict.gmcc.net:30722',
        'Origin': 'https://dict.gmcc.net:30722',
        'Pragma': 'no-cache',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Cookie': cookie_str
    }}
    return headers

def build_request_data(project_id, login_no):
    """构建请求数据"""
    if "{crawler_info['needs_project_id']}" == "True":
        body = {{"PROJECT_ID": project_id}}
    else:
        # 对于不需要PROJECT_ID的接口，使用默认参数
        body = {{}}
    
    return {{
        "ROOT": {{
            "HEADER": {{
                "OPR_INFO": {{
                    "LOGIN_NO": login_no
                }}
            }},
            "BODY": body
        }}
    }}

def query_data(project_id, cookies, login_no):
    """查询数据"""
    try:
        headers = build_request_headers(cookies)
        data = build_request_data(project_id, login_no)
        
        response = requests.post(API_URL, headers=headers, json=data, timeout=30, verify=False)
        
        if response.status_code == 200:
            result = response.json()
            
            # 检查返回码
            return_code = result.get('ROOT', {{}}).get('BODY', {{}}).get('RETURN_CODE', '')
            if return_code == '0':
                return result.get('ROOT', {{}}).get('BODY', {{}}).get('OUT_DATA', {{}})
            else:
                error_msg = result.get('ROOT', {{}}).get('BODY', {{}}).get('RETURN_MSG', '未知错误')
                print(f"[警告] 项目 {{project_id}} 查询失败: {{error_msg}}")
                return None
        else:
            print(f"[错误] 项目 {{project_id}} HTTP请求失败: {{response.status_code}}")
            return None
            
    except Exception as e:
        print(f"[错误] 项目 {{project_id}} 查询异常: {{e}}")
        return None

def save_to_database(data_list):
    """保存数据到数据库（简化版）"""
    if not data_list:
        print("[警告] 没有数据需要保存")
        return
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 清空原有数据
        cursor.execute("DELETE FROM {crawler_info['table_name']}")
        print(f"[信息] 已清空原有数据")
        
        # 简化的插入（只插入基本字段）
        insert_sql = """
        INSERT INTO {crawler_info['table_name']} (
            INPUT_LOGIN_NO, INPUT_PROJECT_ID, import_time
        ) VALUES (%s, %s, NOW())
        """
        
        # 批量插入数据
        for data in data_list:
            values = (
                data['input_login_no'], 
                data['input_project_id']
            )
            cursor.execute(insert_sql, values)
        
        conn.commit()
        print(f"[成功] 成功保存 {{len(data_list)}} 条{crawler_info['description']}数据")
        
    except Exception as e:
        print(f"[错误] 保存数据失败: {{e}}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def main():
    """主函数"""
    print("=" * 60)
    print("综合查询{crawler_info['description']}爬虫程序启动")
    print("=" * 60)
    
    # 加载Cookie
    cookies = load_cookies()
    if not cookies:
        print("[错误] Cookie加载失败，请先运行 login2cookie.py 获取Cookie")
        return
    
    # 获取登录用户名
    login_no, _, _ = get_login_credentials()
    
    # 获取项目ID列表（测试模式，只处理少量数据）
    if "{crawler_info['needs_project_id']}" == "True":
        project_ids = get_project_ids()
        if not project_ids:
            print("[错误] 未获取到项目ID列表")
            return
    else:
        project_ids = ["TEST"]  # 对于不需要PROJECT_ID的接口
    
    print(f"[信息] 开始处理 {{len(project_ids)}} 个项目")
    
    data_list = []
    success_count = 0
    error_count = 0
    
    for i, project_id in enumerate(project_ids, 1):
        print(f"[进度] {{i}}/{{len(project_ids)}} 处理项目: {{project_id}}")
        
        # 查询数据
        result = query_data(project_id, cookies, login_no)
        
        if result:
            data_list.append({{
                'input_login_no': login_no,
                'input_project_id': project_id,
                'result': result
            }})
            success_count += 1
            print(f"  成功获取数据")
        else:
            error_count += 1
        
        # 每处理5个项目休息一下
        if i % 5 == 0:
            time.sleep(1)
    
    print(f"\\n[统计] 成功: {{success_count}}, 失败: {{error_count}}")
    
    # 保存到数据库
    if data_list:
        save_to_database(data_list)
    
    print("=" * 60)
    print("综合查询{crawler_info['description']}爬虫程序完成")
    print("=" * 60)

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "-all":
        main()
    else:
        print("使用方法: python {crawler_info['filename']} -all")
        print("说明: -all 参数表示轮询所有项目数据同步入库")
'''
    
    return template

def main():
    """主函数"""
    print("=" * 60)
    print("快速创建剩余的爬虫程序")
    print("=" * 60)
    
    for crawler_info in REMAINING_CRAWLERS:
        filename = crawler_info['filename']
        
        if os.path.exists(filename):
            print(f"[跳过] {filename} 已存在")
            continue
        
        print(f"[创建] {filename}")
        
        # 生成爬虫程序代码
        code = create_crawler_template(crawler_info)
        
        # 写入文件
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(code)
        
        print(f"[完成] {filename} 创建成功")
    
    print("\n=" * 60)
    print("剩余爬虫程序创建完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
