# 项目数据字典

## 📊 概述

本文档记录了dict爬虫项目中所有数据库表结构、字段定义和数据模型说明。

**更新时间**: 2025-07-02  
**项目路径**: D:/0回集成/dict爬虫/独立爬虫  
**数据库**: dict_spider (MySQL 8.0)

---

## 🗄️ 数据库配置

### 本地数据库
```json
{
    "host": "127.0.0.1",
    "port": 3306,
    "user": "root", 
    "password": "cmcc12345",
    "database": "dict_spider",
    "charset": "utf8mb4"
}
```

### 远程数据库
```json
{
    "host": "*************",
    "port": 20029,
    "user": "root",
    "password": "5eb9a11916e3a66d", 
    "database": "dict_spider",
    "charset": "utf8mb4"
}
```

---

## 📋 核心数据表

### 1. sign_data_detail (签约项目明细表)

**用途**: 存储从dict系统爬取的签约项目详细信息

**表结构**:
```sql
CREATE TABLE `sign_data_detail` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `签约上报日期` TEXT COMMENT '项目签约上报的日期',
    `项目类型` TEXT COMMENT '项目分类类型',
    `项目编码` TEXT COMMENT '项目唯一标识编码',
    `项目名称` TEXT COMMENT '项目完整名称',
    `项目主流程` TEXT COMMENT '项目所属的主要业务流程',
    `归属区县` TEXT COMMENT '项目归属的区县',
    `是否跨区县分成` TEXT COMMENT '是否涉及跨区县收益分成',
    `分成区县名称` TEXT COMMENT '参与分成的区县名称',
    `分成占比` TEXT COMMENT '各区县的分成比例',
    `项目阶段` TEXT COMMENT '项目当前所处阶段',
    `项目进度` TEXT COMMENT '项目完成进度百分比',
    `父项目编码` TEXT COMMENT '上级项目编码（如有）',
    `立项方式` TEXT COMMENT '项目立项的方式',
    `商机编码` TEXT COMMENT '关联的商机编码',
    `集团编码` TEXT COMMENT '客户集团编码',
    `客户名称` TEXT COMMENT '客户公司名称',
    `项目状态` TEXT COMMENT '项目当前状态',
    `所属行业` TEXT COMMENT '客户所属行业分类',
    `一级场景` TEXT COMMENT '业务场景一级分类',
    `二级场景` TEXT COMMENT '业务场景二级分类',
    `能力成熟度（系统测算）` TEXT COMMENT '系统自动计算的能力成熟度',
    `能力成熟度（自评）` TEXT COMMENT '人工自评的能力成熟度',
    `分公司/省政企室` TEXT COMMENT '负责的分公司或省政企室',
    `项目经理` TEXT COMMENT '项目负责人姓名',
    `项目创建时间` TEXT COMMENT '项目在系统中的创建时间',
    `中标时间` TEXT COMMENT '项目中标的时间',
    `竞标方式` TEXT COMMENT '参与竞标的方式',
    `合同类型` TEXT COMMENT '签署的合同类型',
    `合同状态` TEXT COMMENT '合同当前状态',
    `合同编码` TEXT COMMENT '合同唯一编码',
    `签约时间` TEXT COMMENT '合同签署时间',
    `合同年限（年）` TEXT COMMENT '合同有效期年数',
    `合同开始时间` TEXT COMMENT '合同生效开始时间',
    `合同结束时间` TEXT COMMENT '合同到期结束时间',
    `import_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '数据导入时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

**数据来源**: get_sign_detail.py 爬虫脚本  
**更新频率**: 每日自动更新  
**记录数量**: 约1000+条

---

### 2. project_start_date (项目开工日期表)

**用途**: 存储项目的开工时间信息

**表结构**:
```sql
CREATE TABLE `project_start_date` (
    `项目编码` TEXT COMMENT '项目唯一标识编码',
    `开工时间` TEXT COMMENT '项目实际开工时间',
    `查询日期` TEXT COMMENT '数据查询获取的日期'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

**数据来源**: 开工日期处理project_start_date_oldcookie.py  
**关联字段**: 项目编码 -> sign_data_detail.项目编码

---

### 3. market_share_daily (市场份额日监控表)

**用途**: 存储每日市场份额监控数据

**表结构**:
```sql
CREATE TABLE `market_share_daily` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `日期` DATE COMMENT '统计日期',
    `集团名称` VARCHAR(100) COMMENT '集团客户名称',
    `总证件` INT COMMENT '总证件数量',
    `属于拍照` INT COMMENT '属于拍照业务的数量',
    `属于非拍照新流入` INT COMMENT '非拍照新流入数量',
    `拍照时中山移动` INT COMMENT '拍照时中山移动数量',
    `截止当日在网` INT COMMENT '截止当日在网数量',
    `截止当日流失` INT COMMENT '截止当日流失数量',
    `拍照后新增` INT COMMENT '拍照后新增数量',
    `其中属于XR` INT COMMENT '其中属于XR的数量',
    `其中客经` INT COMMENT '其中客户经理负责的数量',
    `其中网格经理` INT COMMENT '其中网格经理负责的数量',
    `其中同一网格渠道` INT COMMENT '同一网格渠道的数量',
    `其他` INT COMMENT '其他类型数量',
    `当日到达` INT COMMENT '当日到达数量',
    `拍照宽带` INT COMMENT '拍照宽带数量',
    `当日宽带` INT COMMENT '当日宽带数量',
    `宽带流失` INT COMMENT '宽带流失数量',
    `宽带新增` INT COMMENT '宽带新增数量',
    `宽带到达` INT COMMENT '宽带到达数量',
    `导入时间` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '数据导入时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

**数据来源**: 126邮箱附件自动下载  
**更新频率**: 每日更新

---

### 4. market_share_detail (市场份额号码明细表)

**用途**: 存储市场份额的详细号码信息

**表结构**:
```sql
CREATE TABLE `market_share_detail` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `更新日期` DATE COMMENT '数据更新日期',
    `号码` VARCHAR(20) COMMENT '手机号码',
    `集团名称` VARCHAR(100) COMMENT '所属集团名称',
    `新入网归属类型` VARCHAR(50) COMMENT '新入网的归属分类',
    `工号` VARCHAR(50) COMMENT '负责人工号',
    `渠道编码` VARCHAR(50) COMMENT '销售渠道编码',
    `渠道名称` VARCHAR(100) COMMENT '销售渠道名称',
    `分公司` VARCHAR(50) COMMENT '所属分公司',
    `网格` VARCHAR(50) COMMENT '所属网格',
    `是否XR` VARCHAR(10) COMMENT '是否为XR业务',
    `是否有宽带` VARCHAR(10) COMMENT '是否开通宽带',
    `是否有高质量合约` VARCHAR(10) COMMENT '是否签署高质量合约',
    `导入时间` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '数据导入时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

---

### 5. import_logs (导入日志表)

**用途**: 记录数据导入操作的日志信息

**表结构**:
```sql
CREATE TABLE `import_logs` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `file_name` VARCHAR(255) COMMENT '导入的文件名',
    `table_name` VARCHAR(50) COMMENT '目标数据表名',
    `record_count` INT COMMENT '导入的记录数量',
    `import_time` DATETIME COMMENT '导入操作时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

**用途**: 防止重复导入，追踪数据导入历史

---

### 6. email_records (邮件记录表)

**用途**: 记录邮件下载和处理的历史

**表结构**:
```sql
CREATE TABLE `email_records` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `sender` VARCHAR(255) COMMENT '发件人邮箱',
    `recipients` VARCHAR(255) COMMENT '收件人邮箱',
    `send_date` VARCHAR(50) COMMENT '邮件发送日期',
    `subject` VARCHAR(255) COMMENT '邮件主题',
    `attachment_list` TEXT COMMENT '附件列表（JSON格式）',
    `receive_date` DATETIME COMMENT '邮件接收处理时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

---

## 🔄 数据流程

### 1. 签约项目数据流程
```
getcookie.py → NEW_sign_get_jsessionid.py → get_sign_detail.py → merge_headers.py → import_to_mysql.py
```

### 2. 项目开工日期流程
```
开工日期处理project_start_date_oldcookie.py → project_start_date表
```

### 3. 合同数据流程
```
合同处理.py → get_hetong_all.py → import_hetong_to_mysql.py
```

### 4. 市场份额数据流程
```
126邮箱 → download_126_attachments.py → market_share_daily/detail表
```

---

## 📊 数据同步

### 本地到远程同步脚本
- `DICT_hetong_local2remote.py` - 合同数据同步
- `DICT_project_start_date_local2remote.py` - 开工日期同步  
- `DICT_sign_data_detail_local2remote.py` - 签约明细同步

### 远程到本地同步脚本
- `BC_opportunity_rm2local.py` - 商机数据同步
- `BC_groups_rm2local.py` - 集团数据同步

---

## 🔍 重要视图

### t_kuanbiao (宽表视图)
**用途**: 综合多表数据的宽表视图，用于报表生成  
**生成方式**: 存储过程 `turn2tkuanbiao`  
**导出脚本**: `DICT_download_kuanbiao_from_db2email.py`

---

## 📝 字段命名规范

1. **中文字段名**: 保持与源系统一致，便于业务理解
2. **TEXT类型**: 大部分字段使用TEXT类型，避免长度限制
3. **时间字段**: 统一使用 `import_time` 记录导入时间
4. **主键**: 所有表都有自增主键 `id`
5. **字符集**: 统一使用 `utf8mb4` 支持中文和特殊字符

---

## 🚀 维护说明

1. **定期清理**: 使用 `remove_duplicates.sql` 清理重复数据
2. **性能优化**: 根据查询需求添加索引
3. **备份策略**: 重要数据表需要定期备份
4. **监控告警**: 关注数据导入失败和异常情况

---

*本文档由系统自动生成，如有疑问请联系项目维护人员*
