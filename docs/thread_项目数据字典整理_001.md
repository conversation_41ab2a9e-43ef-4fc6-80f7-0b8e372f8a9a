# Threads对话记录 - 项目数据字典整理

**导出时间**: 2025-07-02 15:35:00  
**线程ID**: `thread-001-data-dict`  
**线程标题**: 项目数据字典整理  
**线程描述**: 整理dict爬虫项目的数据库表结构和字段定义  

---

## 🧵 项目数据字典整理

**线程ID**: `thread-001-data-dict`

### 📅 2025-07-02 14:00:00

**👤 用户**:

你好，我需要整理项目的数据字典

**🤖 助手**:

好的，我来帮你整理项目中的数据库表结构和字段定义。首先让我分析一下项目中的主要数据表。

根据项目代码分析，主要包含以下核心数据表：

1. **sign_data_detail** - 签约项目明细表
2. **project_start_date** - 项目开工日期表  
3. **market_share_daily** - 市场份额日监控表
4. **market_share_detail** - 市场份额号码明细表
5. **import_logs** - 导入日志表
6. **email_records** - 邮件记录表

我将为每个表提供详细的字段定义和用途说明。

**📋 元数据**:
```json
{
  "type": "greeting",
  "topic": "data_dictionary",
  "tables_identified": 6
}
```

---

### 📅 2025-07-02 14:05:00

**👤 用户**:

请分析sign_data_detail表的结构

**🤖 助手**:

sign_data_detail表是项目的核心业务数据表，存储从dict系统爬取的签约项目详细信息。

**表结构分析**：

```sql
CREATE TABLE `sign_data_detail` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `签约上报日期` TEXT COMMENT '项目签约上报的日期',
    `项目类型` TEXT COMMENT '项目分类类型',
    `项目编码` TEXT COMMENT '项目唯一标识编码',
    `项目名称` TEXT COMMENT '项目完整名称',
    `项目主流程` TEXT COMMENT '项目所属的主要业务流程',
    `归属区县` TEXT COMMENT '项目归属的区县',
    `客户名称` TEXT COMMENT '客户公司名称',
    `项目状态` TEXT COMMENT '项目当前状态',
    `签约时间` TEXT COMMENT '合同签署时间',
    `合同编码` TEXT COMMENT '合同唯一编码',
    `import_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

**关键字段说明**：
- **项目编码**: 业务主键，用于关联其他表
- **客户名称**: 重要的业务维度，用于客户分析
- **签约时间**: 时间维度，用于趋势分析
- **项目状态**: 业务状态跟踪

**数据来源**: get_sign_detail.py爬虫脚本  
**更新频率**: 每日自动更新  
**当前记录数**: 约1000+条

**📋 元数据**:
```json
{
  "type": "analysis",
  "table": "sign_data_detail",
  "fields_count": 30,
  "data_source": "get_sign_detail.py"
}
```

---

### 📅 2025-07-02 14:10:00

**👤 用户**:

market_share_daily表的用途是什么？

**🤖 助手**:

market_share_daily表用于存储每日市场份额监控数据，是业务分析的重要数据源。

**表结构**：
```sql
CREATE TABLE `market_share_daily` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `日期` DATE COMMENT '统计日期',
    `集团名称` VARCHAR(100) COMMENT '集团客户名称',
    `总证件` INT COMMENT '总证件数量',
    `属于拍照` INT COMMENT '属于拍照业务的数量',
    `属于非拍照新流入` INT COMMENT '非拍照新流入数量',
    `拍照时中山移动` INT COMMENT '拍照时中山移动数量',
    `截止当日在网` INT COMMENT '截止当日在网数量',
    `截止当日流失` INT COMMENT '截止当日流失数量',
    `拍照后新增` INT COMMENT '拍照后新增数量',
    `当日宽带` INT COMMENT '当日宽带数量',
    `宽带新增` INT COMMENT '宽带新增数量',
    `导入时间` DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

**业务用途**：
1. **市场份额监控**: 跟踪各集团客户的市场表现
2. **业务趋势分析**: 分析拍照业务和宽带业务的发展趋势
3. **客户流失预警**: 监控客户流失情况
4. **新增客户分析**: 跟踪新客户获取情况

**数据来源**: 126邮箱每日报表附件  
**处理脚本**: download_126_attachments.py  
**更新频率**: 每日自动更新

**📋 元数据**:
```json
{
  "type": "analysis",
  "table": "market_share_daily",
  "business_purpose": "market_monitoring",
  "update_frequency": "daily"
}
```

---

### 📅 2025-07-02 14:15:00

**👤 用户**:

数据表之间有什么关联关系？

**🤖 助手**:

项目中的数据表存在以下关联关系：

**主要关联关系**：

1. **sign_data_detail ↔ project_start_date**
   - 关联字段: `项目编码`
   - 关系: 一对一
   - 用途: 为签约项目补充开工时间信息

2. **sign_data_detail → import_logs**
   - 关联字段: 文件名模式匹配
   - 关系: 多对一
   - 用途: 追踪数据导入历史

3. **market_share_daily ↔ market_share_detail**
   - 关联字段: `日期` + `集团名称`
   - 关系: 一对多
   - 用途: 汇总数据与明细数据的关联

**数据流向**：
```
外部系统 → 爬虫脚本 → Excel文件 → 数据库表 → 远程同步
```

**同步关系**：
- **本地 → 远程**: DICT_*_local2remote.py脚本
- **远程 → 本地**: BC_*_rm2local.py脚本

**业务关联**：
- 项目编码是核心业务主键
- 客户名称/集团名称用于客户维度分析
- 时间字段用于趋势分析和数据同步

**📋 元数据**:
```json
{
  "type": "relationship_analysis",
  "primary_keys": ["项目编码", "集团名称", "日期"],
  "sync_direction": "bidirectional"
}
```

---

## 📊 总结

本次对话完成了项目数据字典的核心内容整理：

1. ✅ 识别了6个主要数据表
2. ✅ 分析了sign_data_detail核心表结构  
3. ✅ 说明了market_share_daily业务用途
4. ✅ 梳理了表间关联关系

**后续工作**：
- 完善其他表的详细字段说明
- 添加索引和性能优化建议
- 补充数据质量检查规则

---

*线程记录完成时间: 2025-07-02 14:15:00*
