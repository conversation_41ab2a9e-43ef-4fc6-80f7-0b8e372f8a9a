# 📚 项目文档说明

## 🎯 概述

本目录包含dict爬虫项目的完整文档，包括数据字典和threads对话记录管理系统。

## 📋 文档结构

```
docs/
├── README.md                    # 本文档
├── data_dictionary.md           # 项目数据字典
├── all_threads_conversations.md # 所有对话记录
├── thread_*.md                  # 按线程分类的对话记录
└── conversations_*.md           # 按日期范围的对话记录
```

## 🗄️ 数据字典

### 📖 [data_dictionary.md](data_dictionary.md)

包含项目中所有数据库表的完整定义：

- **核心表结构**: sign_data_detail, project_start_date, market_share_daily等
- **字段说明**: 每个字段的用途和数据类型
- **数据流程**: 数据采集、处理、同步的完整流程
- **维护说明**: 数据库维护和优化建议

### 🔍 主要数据表

| 表名 | 用途 | 记录数 |
|------|------|--------|
| sign_data_detail | 签约项目明细 | 1000+ |
| project_start_date | 项目开工日期 | 500+ |
| market_share_daily | 市场份额日监控 | 每日更新 |
| market_share_detail | 市场份额号码明细 | 大量数据 |
| import_logs | 数据导入日志 | 历史记录 |

## 🧵 Threads对话记录系统

### 🚀 快速开始

1. **安装依赖**
```bash
# 无需额外依赖，使用Python标准库
python --version  # 需要Python 3.6+
```

2. **运行示例**
```bash
# 创建示例数据并导出
python export_threads_example.py
```

3. **查看结果**
```bash
# 查看生成的文档
ls docs/
```

### 💻 使用方法

#### 基本用法

```python
from threads_manager import ThreadsManager

# 创建管理器
manager = ThreadsManager()

# 创建对话线程
thread_id = manager.create_thread("技术讨论", "关于项目技术问题的讨论")

# 添加对话记录
manager.add_conversation(
    thread_id,
    user_message="如何优化数据库查询？",
    assistant_message="可以通过添加索引、优化SQL语句等方式提升性能。",
    metadata={"type": "technical", "topic": "database"}
)

# 导出为Markdown
manager.export_to_markdown("my_conversations.md")
```

#### 高级功能

```python
# 按线程导出
manager.export_to_markdown("thread_tech.md", thread_id=thread_id)

# 按日期范围导出
manager.export_to_markdown(
    "recent_conversations.md",
    start_date="2025-07-01 00:00:00",
    end_date="2025-07-02 23:59:59"
)

# 获取对话记录
conversations = manager.get_conversations(thread_id=thread_id)

# 获取线程列表
threads = manager.get_threads()
```

### 📊 数据结构

#### 对话记录表 (conversations)
```sql
CREATE TABLE conversations (
    id TEXT PRIMARY KEY,              -- 对话记录ID
    thread_id TEXT NOT NULL,          -- 线程ID
    user_message TEXT,                -- 用户消息
    assistant_message TEXT,           -- 助手回复
    timestamp DATETIME,               -- 时间戳
    metadata TEXT,                    -- 元数据(JSON)
    created_at DATETIME               -- 创建时间
);
```

#### 线程表 (threads)
```sql
CREATE TABLE threads (
    thread_id TEXT PRIMARY KEY,       -- 线程ID
    title TEXT,                       -- 线程标题
    description TEXT,                 -- 线程描述
    created_at DATETIME,              -- 创建时间
    updated_at DATETIME               -- 更新时间
);
```

### 📤 导出格式

生成的Markdown文件包含：

1. **文档头部**: 导出时间、记录数量、过滤条件
2. **线程分组**: 按线程ID分组显示对话
3. **时间排序**: 按时间顺序排列对话记录
4. **完整内容**: 用户消息、助手回复、元数据
5. **格式化**: 清晰的Markdown格式，便于阅读

### 🔧 自定义配置

#### 数据库路径
```python
# 使用自定义数据库文件
manager = ThreadsManager(db_path="custom_threads.db")
```

#### 元数据结构
```python
metadata = {
    "type": "technical",           # 对话类型
    "topic": "database",           # 话题
    "priority": "high",            # 优先级
    "tags": ["sql", "performance"], # 标签
    "user_id": "user123"           # 用户ID
}
```

## 🛠️ 维护和扩展

### 数据备份
```bash
# 备份SQLite数据库
cp threads.db threads_backup_$(date +%Y%m%d).db
```

### 性能优化
- 定期清理旧的对话记录
- 为常用查询字段添加索引
- 考虑数据分片存储大量历史记录

### 功能扩展
- 添加全文搜索功能
- 支持对话记录的标签分类
- 实现对话记录的导入导出
- 添加Web界面管理

## 📝 使用场景

### 1. 项目文档管理
- 记录技术讨论过程
- 保存问题解决方案
- 维护知识库

### 2. 客户服务记录
- 保存客户咨询历史
- 跟踪问题解决进度
- 分析常见问题

### 3. 团队协作
- 记录会议讨论内容
- 保存决策过程
- 分享经验知识

## 🚨 注意事项

1. **数据安全**: 敏感信息请加密存储
2. **性能考虑**: 大量数据时考虑分页查询
3. **备份策略**: 定期备份重要对话记录
4. **权限控制**: 根据需要实现访问权限管理

## 📞 支持

如有问题或建议，请：
1. 查看代码注释和文档
2. 运行示例代码了解用法
3. 联系项目维护人员

---

*文档更新时间: 2025-07-02*  
*项目路径: D:/0回集成/dict爬虫/独立爬虫*
