#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Threads对话记录导出示例
演示如何使用ThreadsManager导出对话记录为Markdown文件
"""

from threads_manager import ThreadsManager
from datetime import datetime, timedelta
import os

def create_sample_data():
    """创建示例对话数据"""
    manager = ThreadsManager()
    
    print("🚀 创建示例对话数据...")
    
    # 创建多个对话线程
    threads = []
    
    # 线程1: 项目数据字典讨论
    thread1 = manager.create_thread(
        "项目数据字典整理", 
        "整理dict爬虫项目的数据库表结构和字段定义"
    )
    threads.append(thread1)
    
    # 线程2: 技术问题讨论
    thread2 = manager.create_thread(
        "技术问题解决", 
        "解决项目开发中遇到的技术问题"
    )
    threads.append(thread2)
    
    # 线程3: 功能需求讨论
    thread3 = manager.create_thread(
        "功能需求分析", 
        "分析和讨论新功能的需求和实现方案"
    )
    threads.append(thread3)
    
    # 为线程1添加对话记录
    conversations_thread1 = [
        {
            "user": "你好，我需要整理项目的数据字典",
            "assistant": "好的，我来帮你整理项目中的数据库表结构和字段定义。首先让我分析一下项目中的主要数据表。",
            "metadata": {"type": "greeting", "topic": "data_dictionary"}
        },
        {
            "user": "请分析sign_data_detail表的结构",
            "assistant": "sign_data_detail表是签约项目明细表，主要字段包括：\n- 项目编码：项目唯一标识\n- 项目名称：项目完整名称\n- 客户名称：客户公司名称\n- 签约时间：合同签署时间\n等等。这是核心业务数据表。",
            "metadata": {"type": "analysis", "table": "sign_data_detail"}
        },
        {
            "user": "market_share_daily表的用途是什么？",
            "assistant": "market_share_daily表用于存储每日市场份额监控数据，包括：\n- 日期：统计日期\n- 集团名称：集团客户名称\n- 总证件：总证件数量\n- 拍照相关指标：各种拍照业务统计\n这个表主要用于市场份额分析和监控。",
            "metadata": {"type": "analysis", "table": "market_share_daily"}
        }
    ]
    
    for conv in conversations_thread1:
        manager.add_conversation(
            thread1,
            user_message=conv["user"],
            assistant_message=conv["assistant"],
            metadata=conv["metadata"]
        )
    
    # 为线程2添加对话记录
    conversations_thread2 = [
        {
            "user": "Python脚本运行时出现编码错误怎么解决？",
            "assistant": "编码错误通常是字符集问题。建议：\n1. 在文件开头添加 # -*- coding: utf-8 -*-\n2. 使用 open() 时指定 encoding='utf-8'\n3. 数据库连接时设置 charset='utf8mb4'\n4. 确保所有文本处理都使用UTF-8编码",
            "metadata": {"type": "technical_issue", "category": "encoding"}
        },
        {
            "user": "如何优化数据库查询性能？",
            "assistant": "数据库查询优化建议：\n1. 添加适当的索引\n2. 避免SELECT *，只查询需要的字段\n3. 使用LIMIT限制返回结果\n4. 优化WHERE条件，使用索引字段\n5. 考虑分页查询大数据集\n6. 定期分析查询执行计划",
            "metadata": {"type": "technical_issue", "category": "database_optimization"}
        }
    ]
    
    for conv in conversations_thread2:
        manager.add_conversation(
            thread2,
            user_message=conv["user"],
            assistant_message=conv["assistant"],
            metadata=conv["metadata"]
        )
    
    # 为线程3添加对话记录
    conversations_thread3 = [
        {
            "user": "需要添加threads对话记录管理功能",
            "assistant": "好的，我来设计threads对话记录管理系统。主要功能包括：\n1. 对话记录存储（SQLite数据库）\n2. 按时间顺序导出为Markdown\n3. 支持多线程对话管理\n4. 提供查询和过滤功能\n5. 元数据支持",
            "metadata": {"type": "feature_request", "feature": "threads_management"}
        },
        {
            "user": "如何实现按时间顺序输出MD文件？",
            "assistant": "实现思路：\n1. 使用SQLite存储对话记录，包含时间戳\n2. 查询时按timestamp字段排序\n3. 生成Markdown格式内容\n4. 支持按线程、日期范围过滤\n5. 包含用户消息、助手回复、元数据\n6. 自动生成目录和索引",
            "metadata": {"type": "implementation", "feature": "markdown_export"}
        }
    ]
    
    for conv in conversations_thread3:
        manager.add_conversation(
            thread3,
            user_message=conv["user"],
            assistant_message=conv["assistant"],
            metadata=conv["metadata"]
        )
    
    print(f"✅ 已创建 {len(threads)} 个对话线程")
    return manager, threads

def export_all_conversations():
    """导出所有对话记录"""
    manager, threads = create_sample_data()
    
    print("\n📤 导出所有对话记录...")
    
    # 导出所有对话记录
    all_file = manager.export_to_markdown("docs/all_threads_conversations.md")
    print(f"✅ 所有对话记录已导出到: {all_file}")
    
    return manager, threads

def export_by_thread():
    """按线程导出对话记录"""
    manager, threads = create_sample_data()
    
    print("\n📤 按线程导出对话记录...")
    
    # 获取所有线程
    thread_list = manager.get_threads()
    
    for thread in thread_list:
        thread_id = thread['thread_id']
        title = thread['title']
        safe_title = title.replace(' ', '_').replace('/', '_')
        
        filename = f"docs/thread_{safe_title}_{thread_id[:8]}.md"
        manager.export_to_markdown(filename, thread_id=thread_id)
        print(f"✅ 线程 '{title}' 已导出到: {filename}")

def export_by_date_range():
    """按日期范围导出对话记录"""
    manager, threads = create_sample_data()
    
    print("\n📤 按日期范围导出对话记录...")
    
    # 导出今天的对话记录
    today = datetime.now().strftime('%Y-%m-%d')
    today_file = manager.export_to_markdown(
        f"docs/conversations_{today}.md",
        start_date=f"{today} 00:00:00",
        end_date=f"{today} 23:59:59"
    )
    print(f"✅ 今日对话记录已导出到: {today_file}")
    
    # 导出最近7天的对话记录
    week_ago = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
    week_file = manager.export_to_markdown(
        "docs/conversations_last_week.md",
        start_date=f"{week_ago} 00:00:00"
    )
    print(f"✅ 最近7天对话记录已导出到: {week_file}")

def show_statistics():
    """显示对话记录统计信息"""
    manager = ThreadsManager()
    
    print("\n📊 对话记录统计信息:")
    
    # 获取所有线程
    threads = manager.get_threads()
    print(f"总线程数: {len(threads)}")
    
    # 获取所有对话记录
    conversations = manager.get_conversations()
    print(f"总对话数: {len(conversations)}")
    
    # 按线程显示统计
    print("\n线程详情:")
    for thread in threads:
        print(f"- {thread['title']}: {thread['conversation_count']}条对话")
        print(f"  线程ID: {thread['thread_id']}")
        print(f"  创建时间: {thread['created_at']}")
        print(f"  更新时间: {thread['updated_at']}")
        print()

def main():
    """主函数"""
    print("🎯 Threads对话记录导出示例")
    print("=" * 50)
    
    # 确保docs目录存在
    os.makedirs("docs", exist_ok=True)
    
    # 1. 导出所有对话记录
    export_all_conversations()
    
    # 2. 按线程导出
    export_by_thread()
    
    # 3. 按日期范围导出
    export_by_date_range()
    
    # 4. 显示统计信息
    show_statistics()
    
    print("\n🎉 所有导出任务完成！")
    print("📁 导出文件位于 docs/ 目录下")

if __name__ == "__main__":
    main()
