-- 基本信息表
CREATE TABLE `dict_spider`.`sign_data_basic` (
  `项目编码` varchar(100) NOT NULL PRIMARY KEY,
  `签约上报日期` varchar(100) NULL,
  `项目类型` varchar(100) NULL,
  `项目名称` varchar(255) NULL,
  -- 其他基本信息字段...
);

-- 效益评估-概算表
CREATE TABLE `dict_spider`.`sign_data_budget_estimate` (
  `项目编码` varchar(100) NOT NULL,
  `战略价值得分` varchar(100) NULL,
  `项目评级` varchar(100) NULL,
  -- 其他概算相关字段...
  PRIMARY KEY (`项目编码`),
  FOREIGN KEY (`项目编码`) REFERENCES `sign_data_basic`(`项目编码`)
);

-- 效益评估-预算表
CREATE TABLE `dict_spider`.`sign_data_budget_final` (
  `项目编码` varchar(100) NOT NULL,
  `战略价值得分` varchar(100) NULL,
  `项目评级` varchar(100) NULL,
  -- 其他预算相关字段...
  PRIMARY KEY (`项目编码`),
  FOREIGN KEY (`项目编码`) REFERENCES `sign_data_basic`(`项目编码`)
);