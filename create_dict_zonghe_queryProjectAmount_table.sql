-- 创建综合查询查询项目金额信息表
-- 对应接口：saleCenterApp/projectAmount/queryProjectAmount

DROP TABLE IF EXISTS `dict_zonghe_queryProjectAmount`;

CREATE TABLE IF NOT EXISTS `dict_zonghe_queryProjectAmount` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  
  -- 入参字段（来源于源数据表）
  `INPUT_LOGIN_NO` varchar(100) DEFAULT NULL COMMENT '入参-登录用户名',
  `INPUT_PROJECT_ID` varchar(100) DEFAULT NULL COMMENT '入参-项目ID（来源：v_distinct_project_id视图或sign_data_detail表项目编码字段）',
  `INPUT_PROJECT_STAGE` varchar(50) DEFAULT NULL COMMENT '入参-项目阶段',
  
  -- 响应数据字段（基于接口返回的JSON结构）
  `AMOUNT_ID` decimal(15,2) DEFAULT NULL COMMENT 'AMOUNT_ID',
  `PROJECT_ID` varchar(200) DEFAULT NULL COMMENT 'PROJECT_ID',
  `BENEFIT_TYPE` varchar(200) DEFAULT NULL COMMENT 'BENEFIT_TYPE',
  `INC_CON_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'INC_CON_AMOUNT',
  `CT_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'CT_AMOUNT',
  `IT_CISERV_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'IT_CISERV_AMOUNT',
  `PLATFORM9ONE_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'PLATFORM9ONE_AMOUNT',
  `CT_PNET5G_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'CT_PNET5G_AMOUNT',
  `IT_PNET5G_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'IT_PNET5G_AMOUNT',
  `MOBILE_CLOUD_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'MOBILE_CLOUD_AMOUNT',
  `MOBILE_CLOUD_IP_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'MOBILE_CLOUD_IP_AMOUNT',
  `IDC_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'IDC_AMOUNT',
  `LINE_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'LINE_AMOUNT',
  `IOT_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'IOT_AMOUNT',
  `STATUS_CD` varchar(200) DEFAULT NULL COMMENT 'STATUS_CD',
  `STATUS_DATE` varchar(50) DEFAULT NULL COMMENT 'STATUS_DATE',
  `CREATE_STAFF` varchar(200) DEFAULT NULL COMMENT 'CREATE_STAFF',
  `CREATE_NAME` text DEFAULT NULL COMMENT 'CREATE_NAME',
  `CREATE_DATE` varchar(50) DEFAULT NULL COMMENT 'CREATE_DATE',
  `UPDATE_STAFF` varchar(50) DEFAULT NULL COMMENT 'UPDATE_STAFF',
  `UPDATE_DATE` varchar(50) DEFAULT NULL COMMENT 'UPDATE_DATE',
  `IT_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'IT_AMOUNT',
  `TD_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'TD_AMOUNT',
  `JG_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'JG_AMOUNT',
  `LL_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'LL_AMOUNT',
  `RZ_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'RZ_AMOUNT',
  `MC_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'MC_AMOUNT',
  `SALE_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'SALE_AMOUNT',
  `VN_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'VN_AMOUNT',
  `IB_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'IB_AMOUNT',
  `AI_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'AI_AMOUNT',
  
  -- 系统字段
  `RUN_IP` varchar(50) DEFAULT NULL COMMENT '运行IP',
  `REQUEST_ID` varchar(100) DEFAULT NULL COMMENT '请求ID',
  `RETURN_CODE` varchar(10) DEFAULT NULL COMMENT '返回码',
  `RETURN_MSG` varchar(200) DEFAULT NULL COMMENT '返回消息',
  `USER_MSG` varchar(200) DEFAULT NULL COMMENT '用户消息',
  `DETAIL_MSG` text DEFAULT NULL COMMENT '详细消息',
  
  -- 元数据字段
  `import_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_input_project_id` (`INPUT_PROJECT_ID`),
  KEY `idx_import_time` (`import_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='综合查询{interface['chinese_name']}表';
