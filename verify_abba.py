#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证ABBA关系
"""

import pymysql

DB_CONFIG = {
    'host': '*************',
    'port': 12136,
    'user': 'root',
    'password': '5eb9a11916e3a66d',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

try:
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    print('=== 验证具体的ABBA关系 ===')
    
    # 验证中国铁塔和广东民新通信的关系
    print('\n1. 中国铁塔股份有限公司中山市分公司 ↔ 广东民新通信科技有限公司')
    
    # 中国铁塔作为客户，广东民新通信作为供应商
    cursor.execute("""
    SELECT 项目编码, 项目名称, 一级场景, 二级场景 
    FROM kuanbiao 
    WHERE 收入侧客户 = '中国铁塔股份有限公司中山市分公司' 
    AND 成本侧供应商 = '广东民新通信科技有限公司'
    """)
    results1 = cursor.fetchall()
    print(f'  中国铁塔(客户) → 广东民新通信(供应商): {len(results1)}个项目')
    for row in results1:
        print(f'    {row[0]} - {row[1]} - {row[2]}-{row[3]}')
    
    # 广东民新通信作为客户，中国铁塔作为供应商
    cursor.execute("""
    SELECT 项目编码, 项目名称, 一级场景, 二级场景 
    FROM kuanbiao 
    WHERE 收入侧客户 = '广东民新通信科技有限公司' 
    AND 成本侧供应商 = '中国铁塔股份有限公司中山市分公司'
    """)
    results2 = cursor.fetchall()
    print(f'  广东民新通信(客户) → 中国铁塔(供应商): {len(results2)}个项目')
    for row in results2:
        print(f'    {row[0]} - {row[1]} - {row[2]}-{row[3]}')
    
    # 验证中通服建设和中移建设的关系
    print('\n2. 中通服建设有限公司 ↔ 中移建设有限公司广东分公司')
    
    # 中通服建设作为客户，中移建设作为供应商
    cursor.execute("""
    SELECT 项目编码, 项目名称, 一级场景, 二级场景 
    FROM kuanbiao 
    WHERE 收入侧客户 = '中通服建设有限公司' 
    AND 成本侧供应商 = '中移建设有限公司广东分公司'
    """)
    results3 = cursor.fetchall()
    print(f'  中通服建设(客户) → 中移建设(供应商): {len(results3)}个项目')
    for row in results3:
        print(f'    {row[0]} - {row[1]} - {row[2]}-{row[3]}')
    
    # 中移建设作为客户，中通服建设作为供应商
    cursor.execute("""
    SELECT 项目编码, 项目名称, 一级场景, 二级场景 
    FROM kuanbiao 
    WHERE 收入侧客户 = '中移建设有限公司广东分公司' 
    AND 成本侧供应商 = '中通服建设有限公司'
    """)
    results4 = cursor.fetchall()
    print(f'  中移建设(客户) → 中通服建设(供应商): {len(results4)}个项目')
    for row in results4:
        print(f'    {row[0]} - {row[1]} - {row[2]}-{row[3]}')
    
    # 验证中山农村商业银行和宜通世纪的关系
    print('\n3. 中山农村商业银行股份有限公司 ↔ 宜通世纪科技股份有限公司')
    
    # 中山农村商业银行作为客户，宜通世纪作为供应商
    cursor.execute("""
    SELECT 项目编码, 项目名称, 一级场景, 二级场景 
    FROM kuanbiao 
    WHERE 收入侧客户 = '中山农村商业银行股份有限公司' 
    AND 成本侧供应商 = '宜通世纪科技股份有限公司'
    """)
    results5 = cursor.fetchall()
    print(f'  中山农村商业银行(客户) → 宜通世纪(供应商): {len(results5)}个项目')
    for row in results5:
        print(f'    {row[0]} - {row[1]} - {row[2]}-{row[3]}')
    
    # 宜通世纪作为客户，中山农村商业银行作为供应商
    cursor.execute("""
    SELECT 项目编码, 项目名称, 一级场景, 二级场景 
    FROM kuanbiao 
    WHERE 收入侧客户 = '宜通世纪科技股份有限公司' 
    AND 成本侧供应商 = '中山农村商业银行股份有限公司'
    """)
    results6 = cursor.fetchall()
    print(f'  宜通世纪(客户) → 中山农村商业银行(供应商): {len(results6)}个项目')
    for row in results6:
        print(f'    {row[0]} - {row[1]} - {row[2]}-{row[3]}')
    
    print('\n=== 总结 ===')
    total_abba = 0
    if results1 and results2:
        total_abba += 1
        print('✓ 中国铁塔 ↔ 广东民新通信: 真实ABBA关系')
    if results3 and results4:
        total_abba += 1
        print('✓ 中通服建设 ↔ 中移建设: 真实ABBA关系')
    if results5 and results6:
        total_abba += 1
        print('✓ 中山农村商业银行 ↔ 宜通世纪: 真实ABBA关系')
    
    print(f'\\n发现的真实ABBA关系总数: {total_abba}')
    
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f'错误: {e}')
