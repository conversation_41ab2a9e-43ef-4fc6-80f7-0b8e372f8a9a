# 数据采集与导入系统

这个系统用于自动化采集签约项目明细数据并导入到MySQL数据库。

## 文件说明

- `getcookie.py` - 获取登录Cookie
- `NEW_sign_get_jsessionid.py` - 更新JSESSIONID，使用动态生成的sign值
- `get_sign_detail.py` - 获取签约项目明细数据
- `merge_headers.py` - 合并表头，格式化Excel文件
- `import_to_mysql.py` - 将数据导入到MySQL数据库
- `main.py` - 自动化执行整个工作流程

## 使用方法

### 配置数据库连接

在首次运行或需要更改数据库连接信息时，请运行：

```
python import_to_mysql.py --config
```

这将引导您输入MySQL数据库的连接信息，并保存到`db_config.json`文件中供后续使用。

### 运行完整工作流程

要执行完整的数据采集和导入流程，请运行：

```
python main.py
```

这将按顺序执行以下步骤：
1. 获取登录Cookie
2. 更新JSESSIONID
3. 获取签约项目明细数据
4. 合并表头
5. 导入数据到MySQL

### 单独运行各个脚本

您也可以单独运行各个脚本：

```
python getcookie.py
python NEW_sign_get_jsessionid.py
python get_sign_detail.py
python merge_headers.py
python import_to_mysql.py
```

## 故障排除

### 数据库连接问题

如果遇到数据库连接错误（例如"Access denied for user"），请使用以下命令重新配置数据库连接：

```
python import_to_mysql.py --config
```

确保输入正确的用户名、密码和数据库名称。

### 文件未找到

如果脚本报告找不到输入文件，请确保按正确的顺序运行脚本，或者手动指定输入文件路径：

```
python merge_headers.py <输入文件路径>
python import_to_mysql.py <输入文件路径>
```

## 注意事项

- 所有脚本会自动查找最新的相关文件作为输入
- 数据库配置保存在`db_config.json`文件中
- 如果数据表不存在，系统会自动创建
