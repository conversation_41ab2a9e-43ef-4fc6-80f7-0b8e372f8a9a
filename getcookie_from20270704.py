import requests
import base64
import ddddocr
from getpassword import get_encrypted_password
import os
import json
from datetime import datetime

# ========== 配置信息 ==========
USERNAME = "zhengdewen"  # 当前使用的用户名

# 密码映射表
PASSWORD_MAP = {
    "zhengdewen": "<PERSON><PERSON>@428"
}

def get_captcha():
    url = "http://dict.gmcc.net:30722/dictWeb/gatewayService/bss-base-operation/base/getVerificationImage"
    try:
        response = requests.post(url)
        response.raise_for_status()
        data = response.json()
        body = data["ROOT"]["BODY"]
        if body["RETURN_CODE"] != "0":
            raise Exception(f"验证码获取失败: {body['RETURN_MSG']}")
        base64_str = body["OUT_DATA"]["base64String"]
        key = body["OUT_DATA"]["key"]
        print(f"[信息] 获取验证码成功，key: {key}")
        return base64_str, key
    except Exception as e:
        print(f"[错误] 获取验证码失败: {e}")
        exit(1)

def get_user_password(username):
    """获取用户的加密密码"""
    if username not in PASSWORD_MAP:
        print(f"[错误] 未知用户名: {username}")
        exit(1)
    return get_encrypted_password(username, PASSWORD_MAP[username], method="with_md5")

def recognize_captcha(base64_str):
    """使用ddddocr识别验证码"""
    ocr = ddddocr.DdddOcr()
    img_bytes = base64.b64decode(base64_str)
    captcha = ocr.classification(img_bytes)
    print(f"[信息] 验证码识别结果: {captcha}")
    return captcha

def login(username, captcha, key):
    url = "http://dict.gmcc.net:30722/dictWeb/gatewayService/gateway/login"
    try:
        # 获取加密后的密码
        password = get_user_password(username)
        print(f"[信息] 用户 {username} 的加密密码已获取")
        params = {
            "sysUserCode": username,
            "password": password,
            #   "L%2FijL6eTIOpp9H8Fb5mGshIobr0LPPetK8ls1EfPowZJFwdw1zUeOIQr4V2ThTvqoRceddCn6FhWxPQWECflMfF0IZnsFytraZ7zqgduW1yJo%2BV4x95utE9lkC5K0gFyt2VNyWrKFbL3vBdO3b1Zm0stSoCbiOm9zSGRmxFdk8JaI3oSluu2Pey5xRaDEm3bBqn5haHU0%2FSnmm9%2FqJ3zHICEpNNW6iYIgqyIWWff%2BKtmPJOOZ9hhSA2FEpU2hOnTEWJTXaafMpP%2FO7O6USmaO%2FWc%2FO63Vi2YJHozqCh17j5jDYeZCBDIvFMaUAqKOOMCHbhcu5nSijbzWFo4UQ01hNW4KkPyqwSxHfbef5wizgR9nHUXvipyaO%2Bciw0sjAjJ5jyMDzlNUnTGNFZWVvLVEOnX6JX38NDk5%2Fw5FqcxDILomCeHHGC%2FW5LAQBan3Dz09cQsppGkgRQGf4yYWLScRA%3D%3D",


            "smsCode": captcha,
            "key": key
        }

        # {
        #     "ROOT": {
        #         "BODY": {
        #             "RETURN_MSG": "认证成功！",
        #             "sysUserCode": "zhengdewen",
        #             "ticket": "zhengdewen*20250704231248..zhengdewen",
        #             "RETURN_CODE": "0",
        #             "sysUserId": "1000003662"
        #         }
        #     }
        # }

        session = requests.Session()
        response = session.get(url, params=params)
        response.raise_for_status()

        # 解析JSON响应
        data = response.json()
        print(f"[信息] 响应内容: {response.text}")

        # 检查登录是否成功
        body = data["ROOT"]["BODY"]
        return_msg = body.get("RETURN_MSG", "")
        return_code = body.get("RETURN_CODE", "")

        if return_msg == "认证成功！":
            print("[信息] 登录成功")
            return session.cookies
        else:
            print(f"[错误] 登录失败: {return_msg} (返回码: {return_code})")
            raise Exception(f"登录失败: {return_msg}")
    except Exception as e:
        print(f"[错误] 登录失败: {e}")
        print(f"[调试] 响应内容: {response.text if 'response' in locals() else '无响应'}")
        exit(1)

def save_cookies(cookies, filename="cookies.txt"):
    """保存cookies到文件，使用JSON格式"""
    try:
        # 创建JSON格式的cookie数据
        cookie_data = {
            "timestamp": datetime.now().isoformat(),
            "url": "http://dict.gmcc.net:30722",
            "cookies": []
        }

        for name, value in cookies.items():
            cookie_data["cookies"].append({
                "name": name,
                "value": value,
                "domain": "dict.gmcc.net",
                "path": "/",
                "expires": -1,
                "httpOnly": False,
                "secure": False,
                "sameSite": "Lax"
            })

        with open(filename, "w", encoding="utf-8") as f:
            json.dump(cookie_data, f, indent=2, ensure_ascii=False)
        print(f"[信息] Cookie已保存到 {filename} (JSON格式，共{len(cookies)}个)")
    except Exception as e:
        print(f"[错误] 保存Cookie失败: {e}")
        exit(1)

def main():
    print("🚀 开始获取登录Cookie...")
    base64_str, key = get_captcha()
    captcha_text = recognize_captcha(base64_str)
    cookies = login(USERNAME, captcha_text, key)
    save_cookies(cookies)
    print("✅ Cookie获取完成")

if __name__ == "__main__":
    main()