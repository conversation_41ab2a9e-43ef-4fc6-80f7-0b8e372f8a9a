#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析kuanbiao数据中的客户-供应商关系
"""

import pymysql
import pandas as pd

DB_CONFIG = {
    'host': '*************',
    'port': 12136,
    'user': 'root',
    'password': '5eb9a11916e3a66d',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

try:
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    print('=== 分析kuanbiao数据中的客户-供应商关系 ===')
    
    # 获取有效数据
    query = """
    SELECT DISTINCT
        `项目编码`,
        `收入侧客户`,
        `成本侧供应商`,
        `一级场景`,
        `二级场景`
    FROM kuanbiao 
    WHERE `收入侧客户` IS NOT NULL 
    AND `收入侧客户` != '' 
    AND `成本侧供应商` IS NOT NULL 
    AND `成本侧供应商` != ''
    AND `一级场景` IS NOT NULL
    AND `一级场景` != ''
    AND `二级场景` IS NOT NULL
    AND `二级场景` != ''
    LIMIT 100
    """
    
    cursor.execute(query)
    results = cursor.fetchall()
    
    print(f'获取到 {len(results)} 条样本数据')
    
    # 分析客户-供应商关系
    relationships = {}
    
    for row in results:
        project_code, customer, supplier, scene1, scene2 = row
        scene_key = f"{scene1}-{scene2}"
        
        if scene_key not in relationships:
            relationships[scene_key] = {}
        
        key = (customer, supplier)
        if key not in relationships[scene_key]:
            relationships[scene_key][key] = []
        relationships[scene_key][key].append(project_code)
    
    print('\n=== 查找潜在的ABBA关系 ===')
    abba_found = False
    
    for scene, scene_relationships in relationships.items():
        print(f'\n场景: {scene}')
        print(f'  关系数量: {len(scene_relationships)}')
        
        # 检查是否有反向关系
        for (customer_a, supplier_b), projects_ab in scene_relationships.items():
            reverse_key = (supplier_b, customer_a)
            
            if reverse_key in scene_relationships:
                projects_ba = scene_relationships[reverse_key]
                print(f'  [发现ABBA] {customer_a} ↔ {supplier_b}')
                print(f'    A→B项目: {projects_ab}')
                print(f'    B→A项目: {projects_ba}')
                abba_found = True
    
    if not abba_found:
        print('\n未发现明显的ABBA关系')
        print('\n=== 分析原因 ===')
        
        # 统计客户和供应商的重叠情况
        all_customers = set()
        all_suppliers = set()
        
        for row in results:
            project_code, customer, supplier, scene1, scene2 = row
            all_customers.add(customer)
            all_suppliers.add(supplier)
        
        overlap = all_customers.intersection(all_suppliers)
        
        print(f'总客户数: {len(all_customers)}')
        print(f'总供应商数: {len(all_suppliers)}')
        print(f'客户与供应商重叠数: {len(overlap)}')
        
        if overlap:
            print('\n重叠的企业（既是客户又是供应商）:')
            for company in list(overlap)[:10]:  # 只显示前10个
                print(f'  - {company}')
        
        # 查看一些具体的关系
        print('\n=== 样本关系 ===')
        count = 0
        for scene, scene_relationships in relationships.items():
            if count >= 3:
                break
            print(f'\n场景: {scene}')
            for (customer, supplier), projects in list(scene_relationships.items())[:3]:
                print(f'  {customer} → {supplier}: {projects}')
            count += 1
    
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f'错误: {e}')
