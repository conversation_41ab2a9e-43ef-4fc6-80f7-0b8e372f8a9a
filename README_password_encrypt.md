# 密码加密工具 - Python 3.10 实现

## 📋 概述

这是一个将 JavaScript 密码加密函数转换为 Python 3.10 的实现，完全对应原始的 JavaScript `password` 函数。

### 🔐 加密流程

1. **MD5 哈希**: 对原始密码进行 MD5 哈希
2. **RSA 公钥加密**: 使用 RSA 公钥对 MD5 结果进行加密
3. **DES 加密**: 使用登录密钥对 RSA 结果进行 DES 加密
4. **URL 编码**: 对最终结果进行 URL 编码

## 🚀 快速开始

### 1. 安装依赖

```bash
python install_requirements.py
```

或手动安装：

```bash
pip install pycryptodome
```

### 2. 基本使用

```python
from password_utils import encrypt_password

# 加密密码
encrypted = encrypt_password("123456", "login_key_from_server")
print(encrypted)
```

### 3. 详细使用示例

```python
from password_utils import encrypt_password

# 模拟登录流程
username = "testuser"
password = "123456"
login_key = "your_login_key"  # 从服务器获取

# 加密密码
encrypted_password = encrypt_password(password, login_key, debug=True)

# 构造登录请求
login_data = {
    "sysUserCode": username,
    "password": encrypted_password,
    "smsCode": "",
    "key": ""
}
```

## 📁 文件说明

### 核心文件

- **`password_utils.py`** - 主要的密码加密工具模块
- **`password_function.py`** - 直接对应 JavaScript 函数的实现
- **`password_encrypt.py`** - 面向对象的加密器类实现

### 辅助文件

- **`install_requirements.py`** - 自动安装依赖包
- **`example_usage.py`** - 使用示例和测试代码
- **`README_password_encrypt.md`** - 本说明文档

## 🔧 API 参考

### `encrypt_password(password, login_key, debug=False)`

主要的密码加密函数。

**参数:**
- `password` (str): 用户输入的原始密码
- `login_key` (str): 从服务器获取的登录密钥
- `debug` (bool): 是否打印调试信息，默认 False

**返回:**
- `str`: 加密后的密码字符串（URL 编码）

**示例:**
```python
encrypted = encrypt_password("123456", "testkey123", debug=True)
```

### 其他辅助函数

- `md5_hash(text)` - 计算 MD5 哈希
- `rsa_encrypt(text)` - RSA 公钥加密
- `des_encrypt(data, key)` - DES 加密

## 🧪 测试

### 运行测试

```bash
# 测试基本功能
python password_function.py

# 测试工具模块
python password_utils.py

# 运行使用示例
python example_usage.py
```

### 测试结果示例

```
=== 密码加密测试 ===
原始密码: 123456
登录密钥: testkey123
🔐 开始加密密码...
1️⃣ MD5 哈希: e10adc3949ba59abbe56e057f20f883e
2️⃣ RSA 加密: GCWuozEBg7p63CTIEo+HBN2+7nuOzeqnsHleuVHJZQoH9Gj3IO...
3️⃣ DES 加密: 9Eu71hvhJ5ih4BS5amaNmErwmetah/VTany3dGcTOgIMRDl8e3...
4️⃣ URL 编码: 9Eu71hvhJ5ih4BS5amaNmErwmetah/VTany3dGcTOgIMRDl8e3...
✅ 加密完成，结果长度: 490
```

## 🔒 安全特性

1. **多层加密**: MD5 + RSA + DES 三重保护
2. **动态密钥**: 使用服务器提供的动态登录密钥
3. **标准算法**: 使用成熟的加密算法和库
4. **URL 安全**: 结果经过 URL 编码，适合网络传输

## 🔄 与 JavaScript 版本对比

| 特性 | JavaScript | Python |
|------|------------|--------|
| MD5 哈希 | `k()` 函数 | `hashlib.md5()` |
| RSA 加密 | `I.encrypt()` | `PKCS1_v1_5.encrypt()` |
| DES 加密 | `C.a.DES.encrypt()` | `DES.new().encrypt()` |
| URL 编码 | `encodeURIComponent()` | `urllib.parse.quote()` |

## 🛠️ 在项目中集成

### 1. 复制文件

将 `password_utils.py` 复制到你的项目目录。

### 2. 导入使用

```python
from password_utils import encrypt_password

def login(username, password):
    # 获取登录密钥（需要根据实际 API 调整）
    login_key = get_login_key_from_server()
    
    # 加密密码
    encrypted_password = encrypt_password(password, login_key)
    
    # 发送登录请求
    response = requests.post('/api/login', {
        'sysUserCode': username,
        'password': encrypted_password,
        'smsCode': '',
        'key': ''
    })
    
    return response
```

### 3. 获取登录密钥

```python
def get_login_key_from_server():
    """从服务器获取登录密钥"""
    try:
        response = requests.get('/api/getloginKey')
        if response.status_code == 200:
            data = response.json()
            return data.get('loginKey', '')
    except Exception as e:
        print(f"获取登录密钥失败: {e}")
        return "fallback_key"
```

## ⚠️ 注意事项

1. **登录密钥**: 确保从正确的服务器接口获取登录密钥
2. **网络安全**: 建议在 HTTPS 环境下使用
3. **密钥管理**: 不要在代码中硬编码敏感的密钥信息
4. **错误处理**: 在生产环境中添加适当的错误处理机制

## 🐛 故障排除

### 常见问题

1. **导入错误**: 确保已安装 `pycryptodome` 包
2. **加密结果不一致**: 检查登录密钥是否正确
3. **编码问题**: 确保输入的密码和密钥使用 UTF-8 编码

### 调试技巧

使用 `debug=True` 参数查看详细的加密过程：

```python
result = encrypt_password("123456", "testkey", debug=True)
```

## 📞 支持

如有问题或建议，请检查：

1. 确保 Python 版本为 3.10+
2. 确保所有依赖包已正确安装
3. 检查输入参数的格式和编码
4. 对比 JavaScript 版本的加密结果进行验证
