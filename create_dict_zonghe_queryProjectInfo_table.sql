-- 创建综合查询项目基本信息表
-- 对应接口：saleCenterApp/projectManage/queryProjectInfo

DROP TABLE IF EXISTS `dict_zonghe_queryProjectInfo`;

CREATE TABLE IF NOT EXISTS `dict_zonghe_queryProjectInfo` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  
  -- 入参字段（来源于源数据表）
  `INPUT_LOGIN_NO` varchar(100) DEFAULT NULL COMMENT '入参-登录用户名',
  `INPUT_PROJECT_ID` varchar(100) DEFAULT NULL COMMENT '入参-项目ID（来源：v_distinct_project_id视图或sign_data_detail表项目编码字段）',
  
  -- 响应数据字段（基于接口返回的JSON结构）
  `PROJECT_NO` varchar(100) DEFAULT NULL COMMENT '项目编号',
  `PROJECT_NAME` text DEFAULT NULL COMMENT '项目名称',
  `BBOSS_PROJECT_ID` varchar(100) DEFAULT NULL COMMENT 'BBOSS项目ID',
  `PROJECT_TYPE` varchar(50) DEFAULT NULL COMMENT '项目类型编码',
  `PROJECT_TYPE_DESC` varchar(200) DEFAULT NULL COMMENT '项目类型描述',
  `TRADE` varchar(50) DEFAULT NULL COMMENT '行业编码',
  `TRADE_DESC` varchar(200) DEFAULT NULL COMMENT '行业描述',
  `IS_BRING_INTO_PMO` varchar(10) DEFAULT NULL COMMENT '是否纳入PMO',
  `PROJECT_CONTRACT_TYPE` varchar(50) DEFAULT NULL COMMENT '项目合同类型编码',
  `PROJECT_CONTRACT_TYPE_NAME` varchar(200) DEFAULT NULL COMMENT '项目合同类型名称',
  `IS_INVESTMENT_PROJECT` varchar(10) DEFAULT NULL COMMENT '是否投资项目',
  `PROJECT_LABLE` varchar(50) DEFAULT NULL COMMENT '项目标签编码',
  `PROJECT_LABLE_NAME` varchar(200) DEFAULT NULL COMMENT '项目标签名称',
  `SALE_OPP_ID` varchar(100) DEFAULT NULL COMMENT '销售机会ID',
  `CUST_ID` varchar(100) DEFAULT NULL COMMENT '客户ID',
  `PROJECT_ID` varchar(100) DEFAULT NULL COMMENT '项目ID',
  `ESTIMATED_CON_PERIOD` varchar(50) DEFAULT NULL COMMENT '预计合同期限',
  `IS_CONNECTED_OPP` varchar(10) DEFAULT NULL COMMENT '是否关联机会',
  `CUST_NAME` text DEFAULT NULL COMMENT '客户名称',
  `REGION_CODE` varchar(50) DEFAULT NULL COMMENT '区域编码',
  `REGION_CODE_DESC` varchar(200) DEFAULT NULL COMMENT '区域描述',
  `FIRST_SCENE` varchar(100) DEFAULT NULL COMMENT '一级场景',
  `SECOND_SCENE` varchar(100) DEFAULT NULL COMMENT '二级场景',
  `PROJECT_STAGE` varchar(50) DEFAULT NULL COMMENT '项目阶段编码',
  `PROJECT_STAGE_NAME` varchar(200) DEFAULT NULL COMMENT '项目阶段名称',
  `PROJECT_PROGRESS` varchar(50) DEFAULT NULL COMMENT '项目进度编码',
  `PROJECT_PROGRESS_NAME` varchar(200) DEFAULT NULL COMMENT '项目进度名称',
  `ESTIMATED_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT '预计金额',
  `PROJECT_SCOPE` varchar(50) DEFAULT NULL COMMENT '项目范围',
  `REQUIREMENTS_TITEL` text DEFAULT NULL COMMENT '需求标题',
  `GROUP_ID` varchar(100) DEFAULT NULL COMMENT '集团ID',
  `BUILD_MODE` varchar(50) DEFAULT NULL COMMENT '建设模式编码',
  `BUILD_MODE_DESC` varchar(200) DEFAULT NULL COMMENT '建设模式描述',
  `ELECTION_MODE` varchar(50) DEFAULT NULL COMMENT '选举模式',
  `IS_BIG_PROJECT` varchar(10) DEFAULT NULL COMMENT '是否大项目',
  `IS_EXCELLENT_PROJECT` varchar(10) DEFAULT NULL COMMENT '是否优秀项目',
  `PARENT_PROJECT_ID` varchar(100) DEFAULT NULL COMMENT '父项目ID',
  `IS_SPEC_MARKET` varchar(10) DEFAULT NULL COMMENT '是否特殊市场',
  `IS_MAINTENANCE` varchar(50) DEFAULT NULL COMMENT '是否维护',
  `PROJECT_STATUS` varchar(50) DEFAULT NULL COMMENT '项目状态',
  `PROJECT_SOURCE` varchar(50) DEFAULT NULL COMMENT '项目来源',
  `BID_FLAG` varchar(10) DEFAULT NULL COMMENT '投标标志',
  `ORG_DESC` text DEFAULT NULL COMMENT '组织描述',
  `BID_FLAG_NAME` varchar(100) DEFAULT NULL COMMENT '投标标志名称',
  `FLOW_TYPE` varchar(50) DEFAULT NULL COMMENT '流程类型',
  
  -- 系统字段
  `RUN_IP` varchar(50) DEFAULT NULL COMMENT '运行IP',
  `REQUEST_ID` varchar(100) DEFAULT NULL COMMENT '请求ID',
  `RETURN_CODE` varchar(10) DEFAULT NULL COMMENT '返回码',
  `RETURN_MSG` varchar(200) DEFAULT NULL COMMENT '返回消息',
  `USER_MSG` varchar(200) DEFAULT NULL COMMENT '用户消息',
  `DETAIL_MSG` text DEFAULT NULL COMMENT '详细消息',
  
  -- 元数据字段
  `import_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_project_id` (`INPUT_PROJECT_ID`),
  KEY `idx_project_no` (`PROJECT_NO`),
  KEY `idx_cust_name` (`CUST_NAME`(100)),
  KEY `idx_project_stage` (`PROJECT_STAGE`),
  KEY `idx_project_status` (`PROJECT_STATUS`),
  KEY `idx_import_time` (`import_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='综合查询项目基本信息表';

-- 创建索引优化查询性能
CREATE INDEX idx_region_code ON dict_zonghe_queryProjectInfo(REGION_CODE);
CREATE INDEX idx_trade ON dict_zonghe_queryProjectInfo(TRADE);
CREATE INDEX idx_project_type ON dict_zonghe_queryProjectInfo(PROJECT_TYPE);
