import os
import pandas as pd
import sqlite3

def import_to_database():
    # 数据库连接
    conn = sqlite3.connect('email_attachments.db')
    c = conn.cursor()
    
    # 创建表（如果不存在）
    c.execute('''CREATE TABLE IF NOT EXISTS attachments
                 (id INTEGER PRIMARY KEY AUTOINCREMENT,
                  filename TEXT,
                  content TEXT)''')
    
    # 读取附件并导入数据库
    attachment_dir = 'attachments'
    for filename in os.listdir(attachment_dir):
        if filename.endswith('.txt'):  # 假设附件是文本文件
            filepath = os.path.join(attachment_dir, filename)
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 插入数据
            c.execute("INSERT INTO attachments (filename, content) VALUES (?, ?)",
                      (filename, content))
            print(f'附件已导入数据库: {filename}')
    
    # 提交更改并关闭连接
    conn.commit()
    conn.close()

if __name__ == "__main__":
    import_to_database()