#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DICT_sign_data_detail_local2remote.py - 本地sign_data_detail表到远程数据库同步脚本
功能：将本地数据库的sign_data_detail表数据同步到远程数据库

表结构说明：
- 主键：id (int AUTO_INCREMENT)
- 包含140+个业务字段：签约上报日期、项目类型、项目编码等
- 字符集：utf8mb4
- 引擎：InnoDB
"""

import pymysql
import pandas as pd
from datetime import datetime
import sys
import traceback
import time

# 本地数据库配置 (数据源)
LOCAL_DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'cmcc12345',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

# 远程数据库配置 (目标数据库)
REMOTE_DB_CONFIG = {
    'host': '*************',
    'port': 12136,
    'user': 'root',
    'password': '5eb9a11916e3a66d',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

# 表名
TABLE_NAME = 'sign_data_detail'

# sign_data_detail表字段列表（排除自增主键id）
SIGN_DATA_DETAIL_COLUMNS = [
    '签约上报日期', '项目类型', '项目编码', '项目名称', '项目主流程',
    '归属区县', '是否跨区县分成', '分成区县名称', '分成占比', '项目阶段',
    '项目进度', '父项目编码', '立项方式', '商机编码', '集团编码',
    '客户名称', '项目状态', '所属行业', '一级场景', '二级场景',
    '能力成熟度（系统测算）', '能力成熟度（自评）', '分公司/省政企室', '项目经理', '项目创建时间',
    '中标时间', '竞标方式', '合同类型', '合同状态', '合同编码',
    '签约时间', '合同年限（年）', '合同开始时间', '合同结束时间', '合同含税金额（万元）',
    '合同不含税金额（万元）', '通服含税收入（万元）', '通服不含税收入（万元）', 'IT通服收入金额（万元）', 'IT通服收入金额不含税（万元）',
    '第三方收入（代收）含税（万元）', '第三方收入（代收）不含税（万元）', '专线总数', '是否招标', '是否含云',
    '合同涉云总金额_含税（万元）', '合同涉云总金额_不含税（万元）', '是否含5G', '是否含物联网', '是否含大数据',
    '是否含IDC', '是否含9one', '项目建设内容及方案简介（CT）', '项目建设内容及方案简介（IT）', '效益评估_概算__战略价值得分',
    '效益评估_概算__项目评级', '效益评估_概算__项目静态回收期（年）', '效益评估_概算__项目净现值（万元）', '效益评估_概算__收入利润率（%）', '效益评估_概算__收入利润率含代收代支%',
    '效益评估_概算__IT收入', '效益评估_概算__IT利润', '效益评估_概算__IT收入利润率', '效益评估_概算__CT收入利润率', '效益评估_概算__CT不含税投资（万元）',
    '效益评估_概算__IT不含税投资（万元）', '效益评估_概算__CT不含税成本（万元）', '效益评估_概算__IT（通服成本）不含税成本（万元）', '效益评估_概算__其他成本不含税成本（万元）', '效益评估_概算__大网分摊成本（万元）',
    '效益评估_概算__IT（含第三方代支）不含税成本（万元）', '效益评估_概算__合作商不含税投入（万元）', '效益评估_概算__运营商不含税投入（万元）', '效益评估_概算__项目不含税总投入（万元）', '效益评估_概算__CT不含税收入（万元）',
    '效益评估_概算__CT_云不含税云收入（万元）', '效益评估_概算__CT_云（ICT_云计算）不含税云收入（万元）', '效益评估_概算__CT_云（云定制化）不含税云收入（万元）', '效益评估_概算__CT_云（ICT_智算云）不含税云收入（万元）', '效益评估_概算__CT_云（ICT_大数据）不含税云收入（万元）',
    '效益评估_概算__CT_云（ICT_9one）不含税云收入（万元）', '效益评估_概算__IT（含第三方代支）不含税收入（万元）', '效益评估_概算__IT不含税云收入（万元）', '效益评估_概算__IT_云（ICT_云计算）不含税云收入（万元）', '效益评估_概算__IT_云（云定制化）不含税云收入（万元）',
    '效益评估_概算__IT_云（ICT_智算云）不含税云收入（万元）', '效益评估_概算__IT_云（ICT_大数据）不含税云收入（万元）', '效益评估_概算__IT_云（ICT_9one）不含税云收入（万元）', '效益评估_概算__项目_云不含税总收入（万元）', '效益评估_概算__合作商不含税收入（万元）',
    '效益评估_概算__运营商不含税收入（万元）', '效益评估_概算__项目不含税总收入（万元）', '效益评估_概算__项目含税总收入（万元）', '效益评估_概算__通服不含税收入（万元）', '效益评估_概算__通服含税收入（万元）',
    '效益评估_概算__其他收入（非主营收入）不含税（万元）', '效益评估_概算__其他收入（非主营收入）含税（万元）', '效益评估_预算__战略价值得分', '效益评估_预算__项目评级', '效益评估_预算__项目静态回收期（年）',
    '效益评估_预算__项目净现值（万元）', '效益评估_预算__IT收入', '效益评估_预算__IT利润', '效益评估_预算__收入利润率（%）', '效益评估_预算__收入利润率含代收代支%',
    '效益评估_预算__IT收入利润率', '效益评估_预算__CT收入利润率', '效益评估_预算__CT不含税投资（万元）', '效益评估_预算__IT不含税投资（万元）', '效益评估_预算__CT不含税成本（万元）',
    '效益评估_预算__IT（通服成本）不含税成本（万元）', '效益评估_预算__其他成本不含税成本（万元）', '效益评估_预算__大网分摊成本（万元）', '效益评估_预算__IT（含第三方代支）不含税成本（万元）', '效益评估_预算__合作商不含税投入（万元）',
    '效益评估_预算__运营商不含税投入（万元）', '效益评估_预算__项目不含税总投入（万元）', '效益评估_预算__CT不含税收入（万元）', '效益评估_预算__CT_云不含税云收入（万元）', '效益评估_预算__CT_云（ICT_云计算）不含税云收入（万元）',
    '效益评估_预算__CT_云（云定制化）不含税云收入（万元）', '效益评估_预算__CT_云（ICT_智算云）不含税云收入（万元）', '效益评估_预算__CT_云（ICT_大数据）不含税云收入（万元）', '效益评估_预算__CT_云（ICT_9one）不含税云收入（万元）', '效益评估_预算__IT（含第三方代支）不含税收入（万元）',
    '效益评估_预算__IT不含税云收入（万元）', '效益评估_预算__IT_云（ICT_云计算）不含税云收入（万元）', '效益评估_预算__IT_云（云定制化）不含税云收入（万元）', '效益评估_预算__IT_云（ICT_智算云）不含税云收入（万元）', '效益评估_预算__IT_云（ICT_大数据）不含税云收入（万元）',
    '效益评估_预算__IT_云（ICT_9one）不含税云收入（万元）', '效益评估_预算__项目_云不含税总收入（万元）', '效益评估_预算__合作商不含税收入（万元）', '效益评估_预算__运营商不含税收入（万元）', '效益评估_预算__项目不含税总收入（万元）',
    '效益评估_预算__项目含税总收入（万元）', '效益评估_预算__通服不含税收入（万元）', '效益评估_预算__通服含税收入（万元）', '效益评估_预算__其他收入（非主营收入）不含税（万元）', '效益评估_预算__其他收入（非主营收入）含税（万元）',
    '效益评估_预算__系统来源', '状态', 'import_time'
]

def connect_to_database(config, db_type="数据库"):
    """连接到数据库"""
    try:
        print(f"[信息] 正在连接{db_type} {config['host']}:{config['port']}...")
        conn = pymysql.connect(**config)
        print(f"[成功] 已连接到{db_type}: {config['database']}")
        return conn
    except Exception as e:
        print(f"[错误] 连接{db_type}失败: {e}")
        return None

def test_table_exists(conn, table_name, db_type="数据库"):
    """测试表是否存在"""
    try:
        cursor = conn.cursor()
        cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
        result = cursor.fetchone()
        cursor.close()
        
        if result:
            print(f"[信息] {db_type}中表 {table_name} 存在")
            return True
        else:
            print(f"[警告] {db_type}中表 {table_name} 不存在")
            return False
    except Exception as e:
        print(f"[错误] 检查表存在性失败: {e}")
        return False

def get_record_count(conn, table_name, db_type="数据库"):
    """获取表记录数"""
    try:
        cursor = conn.cursor()
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        cursor.close()
        print(f"[信息] {db_type} {table_name} 表当前记录数: {count}")
        return count
    except Exception as e:
        print(f"[错误] 获取{db_type}记录数失败: {e}")
        return 0

def truncate_remote_table(remote_conn, table_name):
    """清空远程表数据"""
    try:
        cursor = remote_conn.cursor()
        cursor.execute(f"TRUNCATE TABLE {table_name}")
        remote_conn.commit()
        cursor.close()
        print(f"[成功] 远程表 {table_name} 数据已清空")
        return True
    except Exception as e:
        print(f"[错误] 清空远程表失败: {e}")
        return False

def copy_data_from_local(local_conn, remote_conn, table_name, batch_size=500):
    """从本地数据库复制数据到远程数据库 - 使用CSV导出导入方式避免特殊字符问题"""
    try:
        print(f"[信息] 开始从本地数据库复制 {table_name} 表数据...")
        start_time = time.time()

        # 获取本地表记录数
        local_count = get_record_count(local_conn, table_name, "本地")
        if local_count == 0:
            print("[警告] 本地表没有数据")
            return True

        print(f"[信息] 预计处理 {local_count} 条记录，批次大小: {batch_size}")

        # 分批处理数据
        offset = 0
        total_inserted = 0
        local_cursor = local_conn.cursor()
        remote_cursor = remote_conn.cursor()

        while offset < local_count:
            # 从本地数据库读取一批数据 - 使用SELECT * 避免字段名问题
            select_sql = "SELECT * FROM " + table_name + " ORDER BY id LIMIT %s OFFSET %s"
            local_cursor.execute(select_sql, (batch_size, offset))
            batch_data = local_cursor.fetchall()

            if not batch_data:
                break

            # 获取字段数量（排除id字段）
            field_count = len(batch_data[0]) - 1  # 减去id字段
            placeholders = ', '.join(['%s'] * field_count)

            # 构建插入SQL - 使用简单的字段位置
            insert_sql = f"INSERT INTO {table_name} VALUES (NULL, " + placeholders + ")"

            # 准备数据（排除id字段）
            insert_data = []
            for row in batch_data:
                # 排除第一个字段（id）
                insert_data.append(row[1:])

            # 批量插入
            try:
                remote_cursor.executemany(insert_sql, insert_data)
                remote_conn.commit()
                inserted_in_batch = len(insert_data)
            except Exception as e:
                print(f"[警告] 批量插入失败: {e}")
                # 改用逐条插入
                inserted_in_batch = 0
                for row_data in insert_data:
                    try:
                        remote_cursor.execute(insert_sql, row_data)
                        inserted_in_batch += 1
                    except Exception as row_e:
                        print(f"[警告] 插入单条记录失败: {row_e}")
                        continue
                remote_conn.commit()

            total_inserted += inserted_in_batch
            offset += batch_size

            # 如果批次中有失败的记录，显示警告
            if inserted_in_batch < len(batch_data):
                failed_count = len(batch_data) - inserted_in_batch
                print(f"[警告] 批次中有 {failed_count} 条记录插入失败")

            # 计算进度和预估剩余时间
            progress = min(total_inserted / local_count * 100, 100)
            elapsed_time = time.time() - start_time
            if total_inserted > 0:
                estimated_total_time = elapsed_time * local_count / total_inserted
                remaining_time = max(estimated_total_time - elapsed_time, 0)
                print(f"[进度] {total_inserted}/{local_count} ({progress:.1f}%) - "
                      f"已用时: {elapsed_time:.1f}s, 预计剩余: {remaining_time:.1f}s")

        local_cursor.close()
        remote_cursor.close()

        total_time = time.time() - start_time
        print(f"[成功] 数据复制完成! 共复制 {total_inserted} 条记录")
        print(f"[统计] 总耗时: {total_time:.2f}秒, 平均速度: {total_inserted/total_time:.1f} 条/秒")

        return True
    except Exception as e:
        print(f"[错误] 数据复制失败: {e}")
        print(traceback.format_exc())
        return False

def create_remote_table_if_not_exists(remote_conn):
    """在远程数据库创建sign_data_detail表（如果不存在）"""
    try:
        cursor = remote_conn.cursor()

        # 由于字段太多，分段创建SQL
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS `sign_data_detail` (
          `id` int NOT NULL AUTO_INCREMENT,
          `签约上报日期` text COLLATE utf8mb4_general_ci,
          `项目类型` text COLLATE utf8mb4_general_ci,
          `项目编码` text COLLATE utf8mb4_general_ci,
          `项目名称` text COLLATE utf8mb4_general_ci,
          `项目主流程` text COLLATE utf8mb4_general_ci,
          `归属区县` text COLLATE utf8mb4_general_ci,
          `是否跨区县分成` text COLLATE utf8mb4_general_ci,
          `分成区县名称` text COLLATE utf8mb4_general_ci,
          `分成占比` text COLLATE utf8mb4_general_ci,
          `项目阶段` text COLLATE utf8mb4_general_ci,
          `项目进度` text COLLATE utf8mb4_general_ci,
          `父项目编码` text COLLATE utf8mb4_general_ci,
          `立项方式` text COLLATE utf8mb4_general_ci,
          `商机编码` text COLLATE utf8mb4_general_ci,
          `集团编码` text COLLATE utf8mb4_general_ci,
          `客户名称` text COLLATE utf8mb4_general_ci,
          `项目状态` text COLLATE utf8mb4_general_ci,
          `所属行业` text COLLATE utf8mb4_general_ci,
          `一级场景` text COLLATE utf8mb4_general_ci,
          `二级场景` text COLLATE utf8mb4_general_ci,
          `能力成熟度（系统测算）` text COLLATE utf8mb4_general_ci,
          `能力成熟度（自评）` text COLLATE utf8mb4_general_ci,
          `分公司/省政企室` text COLLATE utf8mb4_general_ci,
          `项目经理` text COLLATE utf8mb4_general_ci,
          `项目创建时间` text COLLATE utf8mb4_general_ci,
          `中标时间` text COLLATE utf8mb4_general_ci,
          `竞标方式` text COLLATE utf8mb4_general_ci,
          `合同类型` text COLLATE utf8mb4_general_ci,
          `合同状态` text COLLATE utf8mb4_general_ci,
          `合同编码` text COLLATE utf8mb4_general_ci,
          `签约时间` text COLLATE utf8mb4_general_ci,
          `合同年限（年）` text COLLATE utf8mb4_general_ci,
          `合同开始时间` text COLLATE utf8mb4_general_ci,
          `合同结束时间` text COLLATE utf8mb4_general_ci,
          `合同含税金额（万元）` text COLLATE utf8mb4_general_ci,
          `合同不含税金额（万元）` text COLLATE utf8mb4_general_ci,
          `通服含税收入（万元）` text COLLATE utf8mb4_general_ci,
          `通服不含税收入（万元）` text COLLATE utf8mb4_general_ci,
          `IT通服收入金额（万元）` text COLLATE utf8mb4_general_ci,
          `IT通服收入金额不含税（万元）` text COLLATE utf8mb4_general_ci,
          `第三方收入（代收）含税（万元）` text COLLATE utf8mb4_general_ci,
          `第三方收入（代收）不含税（万元）` text COLLATE utf8mb4_general_ci,
          `专线总数` text COLLATE utf8mb4_general_ci,
          `是否招标` text COLLATE utf8mb4_general_ci,
          `是否含云` text COLLATE utf8mb4_general_ci,
          `合同涉云总金额_含税（万元）` text COLLATE utf8mb4_general_ci,
          `合同涉云总金额_不含税（万元）` text COLLATE utf8mb4_general_ci,
          `是否含5G` text COLLATE utf8mb4_general_ci,
          `是否含物联网` text COLLATE utf8mb4_general_ci,
          `是否含大数据` text COLLATE utf8mb4_general_ci,
          `是否含IDC` text COLLATE utf8mb4_general_ci,
          `是否含9one` text COLLATE utf8mb4_general_ci,
          `项目建设内容及方案简介（CT）` text COLLATE utf8mb4_general_ci,
          `项目建设内容及方案简介（IT）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__战略价值得分` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__项目评级` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__项目静态回收期（年）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__项目净现值（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__收入利润率（%）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__收入利润率含代收代支%` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__IT收入` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__IT利润` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__IT收入利润率` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__CT收入利润率` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__CT不含税投资（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__IT不含税投资（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__CT不含税成本（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__IT（通服成本）不含税成本（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__其他成本不含税成本（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__大网分摊成本（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__IT（含第三方代支）不含税成本（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__合作商不含税投入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__运营商不含税投入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__项目不含税总投入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__CT不含税收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__CT_云不含税云收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__CT_云（ICT_云计算）不含税云收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__CT_云（云定制化）不含税云收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__CT_云（ICT_智算云）不含税云收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__CT_云（ICT_大数据）不含税云收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__CT_云（ICT_9one）不含税云收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__IT（含第三方代支）不含税收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__IT不含税云收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__IT_云（ICT_云计算）不含税云收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__IT_云（云定制化）不含税云收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__IT_云（ICT_智算云）不含税云收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__IT_云（ICT_大数据）不含税云收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__IT_云（ICT_9one）不含税云收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__项目_云不含税总收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__合作商不含税收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__运营商不含税收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__项目不含税总收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__项目含税总收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__通服不含税收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__通服含税收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__其他收入（非主营收入）不含税（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_概算__其他收入（非主营收入）含税（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__战略价值得分` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__项目评级` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__项目静态回收期（年）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__项目净现值（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__IT收入` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__IT利润` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__收入利润率（%）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__收入利润率含代收代支%` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__IT收入利润率` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__CT收入利润率` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__CT不含税投资（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__IT不含税投资（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__CT不含税成本（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__IT（通服成本）不含税成本（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__其他成本不含税成本（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__大网分摊成本（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__IT（含第三方代支）不含税成本（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__合作商不含税投入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__运营商不含税投入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__项目不含税总投入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__CT不含税收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__CT_云不含税云收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__CT_云（ICT_云计算）不含税云收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__CT_云（云定制化）不含税云收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__CT_云（ICT_智算云）不含税云收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__CT_云（ICT_大数据）不含税云收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__CT_云（ICT_9one）不含税云收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__IT（含第三方代支）不含税收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__IT不含税云收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__IT_云（ICT_云计算）不含税云收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__IT_云（云定制化）不含税云收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__IT_云（ICT_智算云）不含税云收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__IT_云（ICT_大数据）不含税云收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__IT_云（ICT_9one）不含税云收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__项目_云不含税总收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__合作商不含税收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__运营商不含税收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__项目不含税总收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__项目含税总收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__通服不含税收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__通服含税收入（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__其他收入（非主营收入）不含税（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__其他收入（非主营收入）含税（万元）` text COLLATE utf8mb4_general_ci,
          `效益评估_预算__系统来源` text COLLATE utf8mb4_general_ci,
          `状态` text COLLATE utf8mb4_general_ci,
          `import_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        """

        cursor.execute(create_table_sql)
        remote_conn.commit()
        cursor.close()
        print(f"[成功] 远程数据库表 {TABLE_NAME} 创建完成")
        return True
    except Exception as e:
        print(f"[错误] 创建远程表失败: {e}")
        return False

def validate_sync_results(local_conn, remote_conn, table_name):
    """验证同步结果"""
    try:
        print(f"[信息] 正在验证同步结果...")

        # 比较记录数
        local_count = get_record_count(local_conn, table_name, "本地")
        remote_count = get_record_count(remote_conn, table_name, "远程")

        if local_count == remote_count:
            print(f"[验证] ✅ 记录数匹配: {local_count} 条")
        else:
            print(f"[验证] ❌ 记录数不匹配: 本地={local_count}, 远程={remote_count}")
            return False

        # 抽样验证数据内容（验证前5条记录）
        local_cursor = local_conn.cursor()
        remote_cursor = remote_conn.cursor()

        # 获取本地前5条记录的关键字段 - 避免使用f-string格式化
        local_sql = "SELECT `项目编码`, `项目名称`, `合同编码` FROM " + table_name + " ORDER BY id LIMIT 5"
        local_cursor.execute(local_sql)
        local_samples = local_cursor.fetchall()

        # 获取远程前5条记录的关键字段 - 避免使用f-string格式化
        remote_sql = "SELECT `项目编码`, `项目名称`, `合同编码` FROM " + table_name + " ORDER BY id LIMIT 5"
        remote_cursor.execute(remote_sql)
        remote_samples = remote_cursor.fetchall()

        local_cursor.close()
        remote_cursor.close()

        # 比较样本数据
        if local_samples == remote_samples:
            print(f"[验证] ✅ 抽样数据匹配")
            return True
        else:
            print(f"[验证] ❌ 抽样数据不匹配")
            return False

    except Exception as e:
        print(f"[错误] 验证同步结果失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 80)
    print("DICT_sign_data_detail_local2remote.py - 本地sign_data_detail表到远程数据库同步")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)

    # 连接本地数据库
    print("\n1. 连接本地数据库...")
    local_conn = connect_to_database(LOCAL_DB_CONFIG, "本地数据库")
    if not local_conn:
        print("[错误] 无法连接本地数据库，程序退出")
        sys.exit(1)

    # 连接远程数据库
    print("\n2. 连接远程数据库...")
    remote_conn = connect_to_database(REMOTE_DB_CONFIG, "远程数据库")
    if not remote_conn:
        print("[错误] 无法连接远程数据库，程序退出")
        local_conn.close()
        sys.exit(1)

    try:
        # 检查本地表是否存在
        print(f"\n3. 检查本地表 {TABLE_NAME}...")
        if not test_table_exists(local_conn, TABLE_NAME, "本地数据库"):
            print(f"[错误] 本地数据库中不存在表 {TABLE_NAME}")
            return False

        # 检查远程表是否存在，如果不存在则创建
        print(f"\n4. 检查远程表 {TABLE_NAME}...")
        if not test_table_exists(remote_conn, TABLE_NAME, "远程数据库"):
            print(f"[信息] 远程数据库中不存在表 {TABLE_NAME}，正在创建...")
            if not create_remote_table_if_not_exists(remote_conn):
                print("[错误] 创建远程表失败")
                return False
        else:
            print(f"[信息] 远程表 {TABLE_NAME} 已存在")

        # 清空远程表
        print(f"\n5. 清空远程表 {TABLE_NAME}...")
        if not truncate_remote_table(remote_conn, TABLE_NAME):
            print("[错误] 清空远程表失败")
            return False

        # 复制数据
        print(f"\n6. 从本地复制数据到远程...")
        if not copy_data_from_local(local_conn, remote_conn, TABLE_NAME):
            print("[错误] 数据复制失败")
            return False

        # 验证同步结果
        print(f"\n7. 验证同步结果...")
        if validate_sync_results(local_conn, remote_conn, TABLE_NAME):
            print("[验证] ✅ 同步结果验证通过")
        else:
            print("[验证] ❌ 同步结果验证未通过，请检查数据")

        print("\n" + "=" * 80)
        print("✅ sign_data_detail表数据同步完成!")
        print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        return True

    except Exception as e:
        print(f"\n[错误] 程序执行过程中出现异常: {e}")
        print(traceback.format_exc())
        return False
    finally:
        # 关闭数据库连接
        if local_conn:
            local_conn.close()
            print("[信息] 本地数据库连接已关闭")
        if remote_conn:
            remote_conn.close()
            print("[信息] 远程数据库连接已关闭")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
