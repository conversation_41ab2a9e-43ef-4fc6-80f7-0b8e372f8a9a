-- 创建综合查询团队信息表
-- 基于JSON数据结构分析创建

CREATE TABLE IF NOT EXISTS `dict_zonghe_queryTeamMember` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `PROJECT_ID` varchar(100) DEFAULT NULL COMMENT '项目ID',
  `TEAM_TYPE` varchar(20) DEFAULT NULL COMMENT '团队类型',
  `STAFF_NAME` varchar(100) DEFAULT NULL COMMENT '员工姓名',
  `POST_NAME` varchar(100) DEFAULT NULL COMMENT '岗位名称',
  `DEPT_NAME` varchar(200) DEFAULT NULL COMMENT '部门名称',
  `PHONE` varchar(50) DEFAULT NULL COMMENT '电话号码',
  `JOIN_TIME` varchar(50) DEFAULT NULL COMMENT '加入时间',
  `STAFF_ID` varchar(100) DEFAULT NULL COMMENT '员工ID',
  `MEMBER_ID` varchar(100) DEFAULT NULL COMMENT '成员ID',
  `DEPT_ID` varchar(100) DEFAULT NULL COMMENT '部门ID',
  `POST_ID` varchar(100) DEFAULT NULL COMMENT '岗位ID',
  `STAFF_TYPE` varchar(20) DEFAULT NULL COMMENT '员工类型',
  `IS_CHIEF` varchar(10) DEFAULT NULL COMMENT '是否负责人',
  `CONTRIBUTE_RATIO` varchar(20) DEFAULT NULL COMMENT '贡献比例',
  `import_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
  PRIMARY KEY (`id`),
  KEY `idx_project_id` (`PROJECT_ID`),
  KEY `idx_staff_id` (`STAFF_ID`),
  KEY `idx_team_type` (`TEAM_TYPE`),
  KEY `idx_import_time` (`import_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='综合查询团队信息表';
