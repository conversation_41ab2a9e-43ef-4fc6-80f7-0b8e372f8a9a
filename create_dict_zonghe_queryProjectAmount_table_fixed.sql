-- 创建综合查询项目金额信息表
-- 对应接口：saleCenterApp/projectAmount/queryProjectAmount

DROP TABLE IF EXISTS `dict_zonghe_queryProjectAmount`;

CREATE TABLE IF NOT EXISTS `dict_zonghe_queryProjectAmount` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  
  -- 入参字段（来源于源数据表）
  `INPUT_LOGIN_NO` varchar(100) DEFAULT NULL COMMENT '入参-登录用户名',
  `INPUT_PROJECT_ID` varchar(100) DEFAULT NULL COMMENT '入参-项目ID（来源：v_distinct_project_id视图或sign_data_detail表项目编码字段）',
  `INPUT_PROJECT_STAGE` varchar(50) DEFAULT NULL COMMENT '入参-项目阶段',
  
  -- 响应数据字段（基于接口返回的JSON结构）
  `AMOUNT_ID` varchar(100) DEFAULT NULL COMMENT '金额ID',
  `PROJECT_ID` varchar(100) DEFAULT NULL COMMENT '项目ID',
  `BENEFIT_TYPE` varchar(50) DEFAULT NULL COMMENT '收益类型',
  `INC_CON_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT '增量合同金额',
  `CT_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'CT金额',
  `IT_CISERV_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'IT CI服务金额',
  `PLATFORM9ONE_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT '平台9ONE金额',
  `CT_PNET5G_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'CT专网5G金额',
  `IT_PNET5G_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'IT专网5G金额',
  `MOBILE_CLOUD_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT '移动云金额',
  `MOBILE_CLOUD_IP_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT '移动云IP金额',
  `IDC_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'IDC金额',
  `LINE_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT '线路金额',
  `IOT_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'IOT金额',
  `STATUS_CD` varchar(50) DEFAULT NULL COMMENT '状态编码',
  `STATUS_DATE` varchar(50) DEFAULT NULL COMMENT '状态日期',
  `CREATE_STAFF` varchar(100) DEFAULT NULL COMMENT '创建员工',
  `CREATE_NAME` varchar(200) DEFAULT NULL COMMENT '创建员工姓名',
  `CREATE_DATE` varchar(50) DEFAULT NULL COMMENT '创建日期',
  `UPDATE_STAFF` varchar(100) DEFAULT NULL COMMENT '更新员工',
  `UPDATE_DATE` varchar(50) DEFAULT NULL COMMENT '更新日期',
  `IT_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'IT金额',
  `TD_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'TD金额',
  `JG_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'JG金额',
  `LL_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'LL金额',
  `RZ_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'RZ金额',
  `MC_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'MC金额',
  `SALE_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT '销售金额',
  `VN_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'VN金额',
  `IB_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'IB金额',
  `AI_AMOUNT` decimal(15,2) DEFAULT NULL COMMENT 'AI金额',
  
  -- 系统字段
  `RUN_IP` varchar(50) DEFAULT NULL COMMENT '运行IP',
  `REQUEST_ID` varchar(100) DEFAULT NULL COMMENT '请求ID',
  `RETURN_CODE` varchar(10) DEFAULT NULL COMMENT '返回码',
  `RETURN_MSG` varchar(200) DEFAULT NULL COMMENT '返回消息',
  `USER_MSG` varchar(200) DEFAULT NULL COMMENT '用户消息',
  `DETAIL_MSG` text DEFAULT NULL COMMENT '详细消息',
  
  -- 元数据字段
  `import_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_input_project_id` (`INPUT_PROJECT_ID`),
  KEY `idx_amount_id` (`AMOUNT_ID`),
  KEY `idx_project_id` (`PROJECT_ID`),
  KEY `idx_benefit_type` (`BENEFIT_TYPE`),
  KEY `idx_status_cd` (`STATUS_CD`),
  KEY `idx_import_time` (`import_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='综合查询项目金额信息表';
