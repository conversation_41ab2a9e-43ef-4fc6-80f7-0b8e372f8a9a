#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
创建剩余复杂接口的数据表和爬虫程序
"""

import os

# 剩余复杂接口配置
COMPLEX_INTERFACES = [
    {
        'name': 'saleBiddingInfo',
        'chinese_name': '查询销售投标信息',
        'api_path': 'saleCenterApp//biddingSupport/saleBiddingInfo',
        'table_name': 'dict_zonghe_saleBiddingInfo',
        'params': ['LOGIN_NO', 'PROJECT_ID'],
        'key_fields': [
            'BIDDING_ID', 'PROJECT_ID', 'BIDDING_TYPE', 'BIDDING_TYPE_DESC', 'BIDDING_STATUS',
            'BIDDING_STATUS_DESC', 'BIDDING_AMOUNT', 'BIDDING_DATE', 'SUBMIT_DATE', 'RESULT_DATE',
            'BIDDING_RESULT', 'BIDDING_RESULT_DESC', 'WIN_AMOUNT', 'COMPETITOR_INFO', 'BIDDING_STRATEGY',
            'RISK_ASSESSMENT', 'SUCCESS_RATE', 'CREATE_STAFF', 'CREATE_DATE', 'UPDATE_STAFF', 'UPDATE_DATE'
        ]
    },
    {
        'name': 'qryWoListByProject',
        'chinese_name': '按项目查询工单列表',
        'api_path': 'iom-app-svc/iom/api/wo/qryWoListByProject',
        'table_name': 'dict_zonghe_qryWoListByProject',
        'params': ['projectId', 'commodityID', 'qryType', 'currPage', 'pageSize', 'pageNum'],
        'key_fields': [
            'WO_ID', 'WO_NO', 'WO_TYPE', 'WO_TYPE_DESC', 'WO_STATUS', 'WO_STATUS_DESC',
            'PROJECT_ID', 'COMMODITY_ID', 'COMMODITY_NAME', 'CREATE_TIME', 'ASSIGN_TIME',
            'COMPLETE_TIME', 'ASSIGN_STAFF', 'ASSIGN_STAFF_NAME', 'COMPLETE_STAFF', 'COMPLETE_STAFF_NAME',
            'WO_DESC', 'PRIORITY', 'PRIORITY_DESC', 'ESTIMATED_HOURS', 'ACTUAL_HOURS'
        ]
    },
    {
        'name': 'getTodo',
        'chinese_name': '获取待办工单',
        'api_path': 'iom-app-svc/iom/api/wo/getTodo',
        'table_name': 'dict_zonghe_getTodo',
        'params': ['loginNo', 'stepId', 'prodId', 'pageSize', 'pageNum', 'opCodes'],
        'key_fields': [
            'TODO_ID', 'TODO_TYPE', 'TODO_TYPE_DESC', 'TODO_STATUS', 'TODO_STATUS_DESC',
            'PROJECT_ID', 'PROD_ID', 'PROD_NAME', 'STEP_ID', 'STEP_NAME', 'CREATE_TIME',
            'ASSIGN_TIME', 'DUE_TIME', 'ASSIGN_STAFF', 'ASSIGN_STAFF_NAME', 'TODO_DESC',
            'PRIORITY', 'PRIORITY_DESC', 'OP_CODES'
        ]
    },
    {
        'name': 'queryProjectProdprcDict',
        'chinese_name': '查询项目产品流程字典',
        'api_path': 'saleCenterApp/projectImplement/queryProjectProdprcDict',
        'table_name': 'dict_zonghe_queryProjectProdprcDict',
        'params': ['LOGIN_NO', 'SUBJECT_LABLES'],
        'key_fields': [
            'DICT_ID', 'DICT_TYPE', 'DICT_CODE', 'DICT_NAME', 'DICT_DESC', 'PARENT_CODE',
            'DICT_LEVEL', 'SORT_ORDER', 'IS_ACTIVE', 'CREATE_TIME', 'UPDATE_TIME',
            'SUBJECT_LABLES', 'PROD_TYPE', 'PROCESS_TYPE'
        ]
    },
    {
        'name': 'batchLoadCodeList',
        'chinese_name': '批量加载数据字典代码列表',
        'api_path': 'saleCenterApp/common/dataDictService/batchLoadCodeList',
        'table_name': 'dict_zonghe_batchLoadCodeList',
        'params': ['LOGIN_NO', 'TABLE_EN_NAME', 'FIELD_EN_NAME'],
        'key_fields': [
            'CODE_ID', 'TABLE_EN_NAME', 'FIELD_EN_NAME', 'CODE_VALUE', 'CODE_NAME',
            'CODE_DESC', 'PARENT_CODE', 'CODE_LEVEL', 'SORT_ORDER', 'IS_ACTIVE',
            'CREATE_TIME', 'UPDATE_TIME'
        ]
    },
    {
        'name': 'loadCodeList',
        'chinese_name': '加载数据字典代码列表',
        'api_path': 'saleCenterApp/common/dataDictService/loadCodeList',
        'table_name': 'dict_zonghe_loadCodeList',
        'params': ['LOGIN_NO', 'TABLE_EN_NAME', 'FIELD_EN_NAME'],
        'key_fields': [
            'CODE_ID', 'TABLE_EN_NAME', 'FIELD_EN_NAME', 'CODE_VALUE', 'CODE_NAME',
            'CODE_DESC', 'PARENT_CODE', 'CODE_LEVEL', 'SORT_ORDER', 'IS_ACTIVE',
            'CREATE_TIME', 'UPDATE_TIME'
        ]
    }
]

def create_sql_file(interface):
    """创建SQL建表文件"""
    sql_content = f"""-- 创建综合查询{interface['chinese_name']}表
-- 对应接口：{interface['api_path']}

DROP TABLE IF EXISTS `{interface['table_name']}`;

CREATE TABLE IF NOT EXISTS `{interface['table_name']}` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  
  -- 入参字段（来源于源数据表）
  `INPUT_LOGIN_NO` varchar(100) DEFAULT NULL COMMENT '入参-登录用户名',
"""
    
    # 根据不同接口添加不同的入参字段
    if interface['name'] in ['saleBiddingInfo', 'queryProjectProdprcDict']:
        sql_content += "  `INPUT_PROJECT_ID` varchar(100) DEFAULT NULL COMMENT '入参-项目ID（来源：v_distinct_project_id视图或sign_data_detail表项目编码字段）',\n"
    elif interface['name'] == 'qryWoListByProject':
        sql_content += """  `INPUT_PROJECT_ID` varchar(100) DEFAULT NULL COMMENT '入参-项目ID',
  `INPUT_COMMODITY_ID` varchar(100) DEFAULT NULL COMMENT '入参-商品ID',
  `INPUT_QRY_TYPE` varchar(50) DEFAULT NULL COMMENT '入参-查询类型',
  `INPUT_CURR_PAGE` int DEFAULT NULL COMMENT '入参-当前页',
  `INPUT_PAGE_SIZE` int DEFAULT NULL COMMENT '入参-页大小',
  `INPUT_PAGE_NUM` int DEFAULT NULL COMMENT '入参-页码',
"""
    elif interface['name'] == 'getTodo':
        sql_content += """  `INPUT_STEP_ID` varchar(100) DEFAULT NULL COMMENT '入参-步骤ID',
  `INPUT_PROD_ID` varchar(100) DEFAULT NULL COMMENT '入参-产品ID',
  `INPUT_PAGE_SIZE` int DEFAULT NULL COMMENT '入参-页大小',
  `INPUT_PAGE_NUM` int DEFAULT NULL COMMENT '入参-页码',
  `INPUT_OP_CODES` varchar(200) DEFAULT NULL COMMENT '入参-操作代码',
"""
    elif interface['name'] in ['batchLoadCodeList', 'loadCodeList']:
        sql_content += """  `INPUT_TABLE_EN_NAME` varchar(100) DEFAULT NULL COMMENT '入参-表英文名',
  `INPUT_FIELD_EN_NAME` varchar(100) DEFAULT NULL COMMENT '入参-字段英文名',
"""
    elif interface['name'] == 'queryProjectProdprcDict':
        sql_content += "  `INPUT_SUBJECT_LABLES` varchar(200) DEFAULT NULL COMMENT '入参-主题标签',\n"
    
    sql_content += """  
  -- 响应数据字段（基于接口返回的JSON结构）
"""
    
    # 添加关键字段
    for field in interface['key_fields']:
        if 'AMOUNT' in field or 'MONEY' in field or 'HOURS' in field:
            sql_content += f"  `{field}` decimal(15,2) DEFAULT NULL COMMENT '{field}',\n"
        elif 'DATE' in field or 'TIME' in field:
            sql_content += f"  `{field}` varchar(50) DEFAULT NULL COMMENT '{field}',\n"
        elif 'DESC' in field or 'NAME' in field or 'CONTENT' in field or 'INFO' in field:
            sql_content += f"  `{field}` text DEFAULT NULL COMMENT '{field}',\n"
        else:
            sql_content += f"  `{field}` varchar(200) DEFAULT NULL COMMENT '{field}',\n"
    
    sql_content += """  
  -- 系统字段
  `RUN_IP` varchar(50) DEFAULT NULL COMMENT '运行IP',
  `REQUEST_ID` varchar(100) DEFAULT NULL COMMENT '请求ID',
  `RETURN_CODE` varchar(10) DEFAULT NULL COMMENT '返回码',
  `RETURN_MSG` varchar(200) DEFAULT NULL COMMENT '返回消息',
  `USER_MSG` varchar(200) DEFAULT NULL COMMENT '用户消息',
  `DETAIL_MSG` text DEFAULT NULL COMMENT '详细消息',
  
  -- 元数据字段
  `import_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_import_time` (`import_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='综合查询{interface['chinese_name']}表';
"""
    
    filename = f"create_{interface['table_name']}_table.sql"
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(sql_content)
    
    print(f"[生成] {filename}")

def main():
    """主函数"""
    print("=" * 60)
    print("创建剩余复杂接口的数据表")
    print("=" * 60)
    
    for interface in COMPLEX_INTERFACES:
        print(f"\n正在生成接口: {interface['chinese_name']}")
        create_sql_file(interface)
    
    print("\n=" * 60)
    print("复杂接口SQL文件生成完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
