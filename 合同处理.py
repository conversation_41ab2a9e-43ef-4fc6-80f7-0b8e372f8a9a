#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import time
import subprocess
from datetime import datetime

def run_script(script_name, description=None):
    """运行指定的Python脚本并等待其完成"""
    if description:
        print(f"\n{'='*50}")
        print(f"🚀 {description}")
        print(f"{'='*50}")
    
    print(f"[信息] 正在运行 {script_name}...")
    
    try:
        # 使用subprocess运行脚本
        result = subprocess.run([sys.executable, script_name], 
                               capture_output=True, 
                               text=True, 
                               encoding='utf-8',
                               check=True)
        
        # 打印脚本的输出
        if result.stdout:
            print(result.stdout)
        
        if result.returncode == 0:
            print(f"[成功] {script_name} 运行完成")
            return True
        else:
            print(f"[错误] {script_name} 运行失败，返回码: {result.returncode}")
            if result.stderr:
                print(f"错误信息: {result.stderr}")
            return False
    
    except subprocess.CalledProcessError as e:
        print(f"[错误] 运行 {script_name} 时出错: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        return False
    except Exception as e:
        print(f"[错误] 运行 {script_name} 时发生异常: {e}")
        return False

def main():
    """主函数，按顺序执行所有脚本"""
    start_time = time.time()
    print(f"📊 开始执行合同数据采集与导入流程 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 步骤1: 运行getcookie.py，更新cookie
    # if not run_script("getcookie.py", "步骤1: 更新登录Cookie"):
    #     print("[错误] 更新Cookie失败，流程终止")
    #     return
    
    # 步骤2: 运行get_hetong_all.py，获取合同信息
    if not run_script("get_hetong_all.py", "步骤2: 获取合同信息"):
        print("[错误] 获取合同信息失败，流程终止")
        return
    
    # 查找最新的合同信息列表文件
    hetong_files = [f for f in os.listdir('.') if f.startswith('合同信息列表_') and f.endswith('.xlsx')]
    if not hetong_files:
        print("[错误] 未找到合同信息列表文件，流程终止")
        return
    
    latest_hetong_file = max(hetong_files, key=os.path.getmtime)
    print(f"[信息] 找到最新的合同信息列表文件: {latest_hetong_file}")
    
    # 步骤3: 运行import_hetong_to_mysql.py，将合同信息导入数据库
    if not run_script("import_hetong_to_mysql.py", "步骤3: 导入合同信息到MySQL"):
        print("[错误] 导入合同信息到MySQL失败，流程终止")
        return
    
    # 计算总耗时
    end_time = time.time()
    duration = end_time - start_time
    minutes, seconds = divmod(duration, 60)
    
    print(f"\n{'='*50}")
    print(f"✅ 合同数据处理")
    print(f"总耗时: {int(minutes)}分{int(seconds)}秒")
    print(f"{'='*50}")

if __name__ == "__main__":
    main()
