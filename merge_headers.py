import pandas as pd
import numpy as np
import warnings
import sys
import os
from datetime import datetime

# 忽略 openpyxl 的样式警告
warnings.filterwarnings("ignore", category=UserWarning,
                        module="openpyxl.styles.stylesheet")

def merge_headers(input_file=None):
    # 如果没有提供输入文件，尝试使用命令行参数
    if input_file is None:
        if len(sys.argv) > 1:
            input_file = sys.argv[1]
        else:
            # 查找最新的temp_签约项目明细文件
            files = [f for f in os.listdir('.') if f.startswith('temp_签约项目明细_') and f.endswith('.xlsx')]
            if not files:
                print("[错误] 未找到签约项目明细文件，请先运行get_sign_detail.py")
                return None
            # 按修改时间排序，获取最新的文件
            input_file = max(files, key=os.path.getmtime)
            print(f"[信息] 自动选择最新的文件: {input_file}")

    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"[错误] 文件不存在: {input_file}")
        return None

    print(f"[信息] 正在处理文件: {input_file}")

    # 读取Excel文件，不指定header
    df = pd.read_excel(input_file, header=None)

    # 获取前三行作为表头
    header_rows = df.iloc[:3]

    # 创建新的表头列表
    new_headers = []
    for col in range(header_rows.shape[1]):
        # 获取当前列的前三行
        col_values = header_rows.iloc[:, col].tolist()
        # 去除NaN值
        col_values = [str(val).strip().replace("-", "_") for val in col_values if not pd.isna(val)]

        # 去除重复值，只保留第一次出现的值
        unique_values = []
        for val in col_values:
            if val not in unique_values:
                unique_values.append(val)

        # 用双下划线"__"连接
        header = '__'.join(unique_values)

        # 如果整列都是NaN，使用默认列名
        if header == '':
            header = f'Column_{col}'
        new_headers.append(header)

    # 创建新的DataFrame，使用合并后的表头，数据从第4行开始
    new_df = pd.DataFrame(df.iloc[3:].values, columns=new_headers)

    # 保存到新的Excel文件
    output_file = 'merged_header_' + os.path.basename(input_file)
    new_df.to_excel(output_file, index=False)

    print(f"[成功] 表头合并完成，已保存到{output_file}")
    return output_file

if __name__ == "__main__":
    merge_headers()
