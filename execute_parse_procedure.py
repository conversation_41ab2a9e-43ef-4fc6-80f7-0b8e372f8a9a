#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
执行解析欠费数据的存储过程
"""

import sys
import os
import pymysql

# 添加配置文件路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'dict_romte'))
from dict_romte.config import get_db_config

def execute_sql_file(sql_file_path):
    """执行SQL文件"""
    try:
        # 获取数据库配置
        DB_CONFIG = get_db_config('default')
        
        # 读取SQL文件
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 连接数据库
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        print("[信息] 开始执行SQL脚本...")
        
        # 分割SQL语句（按照DELIMITER分割）
        sql_statements = []
        current_statement = ""
        delimiter = ";"
        
        lines = sql_content.split('\n')
        for line in lines:
            line = line.strip()
            
            # 跳过注释和空行
            if line.startswith('--') or line.startswith('#') or not line:
                continue
            
            # 处理DELIMITER命令
            if line.upper().startswith('DELIMITER'):
                delimiter = line.split()[-1]
                continue
            
            current_statement += line + "\n"
            
            # 检查是否到达语句结束
            if line.endswith(delimiter):
                if delimiter != ";":
                    # 移除自定义分隔符
                    current_statement = current_statement.replace(delimiter, "")
                
                sql_statements.append(current_statement.strip())
                current_statement = ""
        
        # 如果还有剩余语句
        if current_statement.strip():
            sql_statements.append(current_statement.strip())
        
        # 执行每个SQL语句
        for i, statement in enumerate(sql_statements):
            if statement:
                try:
                    print(f"[执行] 语句 {i+1}/{len(sql_statements)}")
                    cursor.execute(statement)
                    conn.commit()
                    print(f"[成功] 语句 {i+1} 执行成功")
                except Exception as e:
                    print(f"[错误] 语句 {i+1} 执行失败: {e}")
                    print(f"[语句] {statement[:100]}...")
        
        print("[完成] SQL脚本执行完成")
        
        # 关闭连接
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"[错误] 执行SQL文件失败: {e}")
        return False

def call_parse_procedure():
    """调用解析存储过程"""
    try:
        # 获取数据库配置
        DB_CONFIG = get_db_config('default')
        
        # 连接数据库
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        print("[信息] 开始调用存储过程 parse_arrears_data...")
        
        # 调用存储过程
        cursor.callproc('parse_arrears_data')
        
        # 获取结果
        results = cursor.fetchall()
        if results:
            print("[结果] 存储过程执行结果:")
            for row in results:
                print(f"  状态: {row[0]}")
                print(f"  总记录数: {row[1]}")
                print(f"  总欠费金额: {row[2]}")
                print(f"  最早账单: {row[3]}")
                print(f"  最新账单: {row[4]}")
        
        conn.commit()
        
        # 查询解析后的数据样例
        cursor.execute("SELECT COUNT(*) FROM dict_zonghe_qiye_qianfei")
        count = cursor.fetchone()[0]
        print(f"[统计] dict_zonghe_qiye_qianfei表中共有 {count} 条记录")
        
        if count > 0:
            cursor.execute("""
                SELECT cust_name, cust_id, owe_amount, bill_mon 
                FROM dict_zonghe_qiye_qianfei 
                ORDER BY owe_amount DESC 
                LIMIT 5
            """)
            top_records = cursor.fetchall()
            print("[样例] 欠费金额最高的5条记录:")
            for i, record in enumerate(top_records, 1):
                print(f"  {i}. {record[0]} - {record[1]} - ¥{record[2]} - {record[3]}")
        
        # 关闭连接
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"[错误] 调用存储过程失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("MySQL 8 欠费数据解析存储过程执行器")
    print("=" * 60)
    
    # 1. 执行SQL文件创建存储过程
    print("\n步骤1: 创建存储过程...")
    if execute_sql_file('create_parse_arrears_procedure.sql'):
        print("[成功] 存储过程创建完成")
    else:
        print("[失败] 存储过程创建失败")
        return
    
    # 2. 调用存储过程解析数据
    print("\n步骤2: 执行数据解析...")
    if call_parse_procedure():
        print("[成功] 数据解析完成")
    else:
        print("[失败] 数据解析失败")
        return
    
    print("\n" + "=" * 60)
    print("所有操作完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()
