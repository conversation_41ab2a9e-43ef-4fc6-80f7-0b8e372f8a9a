#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
detect_abba_relaxed.py - 宽松版ABBA项目关系检测脚本
功能：基于kuanbiao视图数据，使用更宽松的条件检测"疑似ABBA"的项目关系

宽松条件：
1. 不要求完全相同的一级场景和二级场景，允许相关场景
2. 重点关注既是客户又是供应商的企业
3. 基于项目内容相似度进行进一步筛选
"""

import pymysql
import pandas as pd
from datetime import datetime
import sys
import traceback
import argparse
from difflib import SequenceMatcher

# 数据库配置
DB_CONFIG = {
    'host': '*************',
    'port': 12136,
    'user': 'root',
    'password': '5eb9a11916e3a66d',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='宽松版ABBA项目关系检测')
    parser.add_argument('--ct-threshold', type=float, default=0.5, 
                       help='CT内容相似度阈值 (默认: 0.5)')
    parser.add_argument('--it-threshold', type=float, default=0.5,
                       help='IT内容相似度阈值 (默认: 0.5)')
    return parser.parse_args()

def connect_to_database():
    """连接到数据库"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        print(f"[成功] 已连接到数据库: {DB_CONFIG['database']}")
        return conn
    except Exception as e:
        print(f"[错误] 连接数据库失败: {e}")
        return None

def create_abba_table_relaxed(conn):
    """创建宽松版dict_prj_abba表"""
    cursor = conn.cursor()
    try:
        # 删除已存在的表
        cursor.execute("DROP TABLE IF EXISTS dict_prj_abba")
        
        # 创建新表
        create_table_sql = """
        CREATE TABLE dict_prj_abba (
            id INT AUTO_INCREMENT PRIMARY KEY,
            企业A VARCHAR(500) COMMENT '企业A名称',
            企业B VARCHAR(500) COMMENT '企业B名称',
            A作为客户场景 TEXT COMMENT 'A作为客户的场景列表',
            A作为客户项目编码 TEXT COMMENT 'A作为客户的项目编码列表',
            A作为客户项目数量 INT COMMENT 'A作为客户的项目数量',
            B作为供应商场景 TEXT COMMENT 'B作为供应商的场景列表',
            B作为供应商项目编码 TEXT COMMENT 'B作为供应商的项目编码列表',
            B作为供应商项目数量 INT COMMENT 'B作为供应商的项目数量',
            CT内容相似度 DECIMAL(5,4) COMMENT 'CT内容最高相似度',
            IT内容相似度 DECIMAL(5,4) COMMENT 'IT内容最高相似度',
            相似项目对 TEXT COMMENT '相似的项目对信息',
            ABBA风险等级 VARCHAR(20) COMMENT '风险等级：高/中/低',
            CT阈值 DECIMAL(3,2) COMMENT '使用的CT相似度阈值',
            IT阈值 DECIMAL(3,2) COMMENT '使用的IT相似度阈值',
            检测时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '检测时间',
            备注 TEXT COMMENT '备注信息'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='宽松版疑似ABBA项目关系表'
        """
        
        cursor.execute(create_table_sql)
        conn.commit()
        print("[成功] dict_prj_abba表创建完成")
        return True
        
    except Exception as e:
        print(f"[错误] 创建dict_prj_abba表失败: {e}")
        traceback.print_exc()
        return False
    finally:
        cursor.close()

def get_kuanbiao_data_relaxed(conn):
    """获取kuanbiao视图数据（宽松版）"""
    try:
        print("[信息] 正在获取kuanbiao视图数据...")
        
        query = """
        SELECT DISTINCT
            `签约上报日期`,
            `项目编码`,
            `项目名称`,
            `合同含税金额（万元）`,
            `收入侧客户`,
            `收入侧合同编码`,
            `项目建设内容及方案简介（CT）`,
            `项目建设内容及方案简介（IT）`,
            `一级场景`,
            `二级场景`,
            `后向合同签约时间`,
            `成本侧供应商`
        FROM kuanbiao 
        WHERE `收入侧客户` IS NOT NULL 
        AND `收入侧客户` != '' 
        AND `成本侧供应商` IS NOT NULL 
        AND `成本侧供应商` != ''
        """
        
        df = pd.read_sql(query, conn)
        print(f"[信息] 获取到 {len(df)} 条有效数据")
        return df
        
    except Exception as e:
        print(f"[错误] 获取kuanbiao数据失败: {e}")
        traceback.print_exc()
        return None

def calculate_similarity(text1, text2):
    """计算两个文本的相似度"""
    if pd.isna(text1) or pd.isna(text2) or text1 == '' or text2 == '':
        return 0.0
    
    # 清理文本
    text1 = str(text1).strip()
    text2 = str(text2).strip()
    
    if text1 == text2:
        return 1.0
    
    # 使用SequenceMatcher计算相似度
    similarity = SequenceMatcher(None, text1, text2).ratio()
    return similarity

def detect_abba_relaxed(df, ct_threshold, it_threshold):
    """宽松版ABBA关系检测"""
    print("[信息] 开始宽松版ABBA关系检测...")
    print(f"[配置] CT相似度阈值: {ct_threshold*100}%")
    print(f"[配置] IT相似度阈值: {it_threshold*100}%")
    
    abba_results = []
    
    # 找出既是客户又是供应商的企业
    all_customers = set(df['收入侧客户'].dropna())
    all_suppliers = set(df['成本侧供应商'].dropna())
    overlap_companies = all_customers.intersection(all_suppliers)
    
    print(f"[信息] 发现 {len(overlap_companies)} 个既是客户又是供应商的企业")
    
    if len(overlap_companies) < 2:
        print("[警告] 重叠企业数量不足，无法进行ABBA分析")
        return abba_results
    
    # 为每个重叠企业收集其作为客户和供应商的项目信息
    company_projects = {}
    
    for company in overlap_companies:
        company_projects[company] = {
            'as_customer': df[df['收入侧客户'] == company].to_dict('records'),
            'as_supplier': df[df['成本侧供应商'] == company].to_dict('records')
        }
    
    # 分析企业间的ABBA关系
    overlap_list = list(overlap_companies)
    
    for i in range(len(overlap_list)):
        for j in range(i + 1, len(overlap_list)):
            company_a = overlap_list[i]
            company_b = overlap_list[j]
            
            # 检查A作为客户，B作为供应商的项目
            a_customer_projects = [p for p in company_projects[company_a]['as_customer'] 
                                 if p['成本侧供应商'] == company_b]
            
            # 检查B作为客户，A作为供应商的项目
            b_customer_projects = [p for p in company_projects[company_b]['as_customer'] 
                                 if p['成本侧供应商'] == company_a]
            
            # 如果存在双向关系，进行相似度分析
            if a_customer_projects and b_customer_projects:
                print(f"[发现] 双向关系: {company_a} ↔ {company_b}")
                print(f"  - A作为客户: {len(a_customer_projects)}个项目")
                print(f"  - B作为客户: {len(b_customer_projects)}个项目")
                
                # 计算项目间的最高相似度
                max_ct_similarity = 0.0
                max_it_similarity = 0.0
                similar_pairs = []
                
                for proj_a in a_customer_projects:
                    for proj_b in b_customer_projects:
                        # 计算CT相似度
                        ct_sim = calculate_similarity(
                            proj_a.get('项目建设内容及方案简介（CT）'),
                            proj_b.get('项目建设内容及方案简介（CT）')
                        )
                        
                        # 计算IT相似度
                        it_sim = calculate_similarity(
                            proj_a.get('项目建设内容及方案简介（IT）'),
                            proj_b.get('项目建设内容及方案简介（IT）')
                        )
                        
                        if ct_sim > max_ct_similarity:
                            max_ct_similarity = ct_sim
                        
                        if it_sim > max_it_similarity:
                            max_it_similarity = it_sim
                        
                        # 如果相似度超过阈值，记录这对项目
                        if ct_sim >= ct_threshold or it_sim >= it_threshold:
                            similar_pairs.append({
                                'proj_a_code': proj_a.get('项目编码'),
                                'proj_a_name': proj_a.get('项目名称'),
                                'proj_b_code': proj_b.get('项目编码'),
                                'proj_b_name': proj_b.get('项目名称'),
                                'ct_similarity': ct_sim,
                                'it_similarity': it_sim
                            })
                
                # 记录ABBA关系（不管是否有相似项目对）
                total_projects = len(a_customer_projects) + len(b_customer_projects)
                avg_similarity = (max_ct_similarity + max_it_similarity) / 2
                
                if total_projects >= 4 and avg_similarity >= 0.7:
                    risk_level = "高"
                elif total_projects >= 2 and avg_similarity >= 0.5:
                    risk_level = "中"
                else:
                    risk_level = "低"
                
                # 构建场景信息
                a_scenes = list(set([f"{p.get('一级场景', '')}-{p.get('二级场景', '')}" for p in a_customer_projects]))
                b_scenes = list(set([f"{p.get('一级场景', '')}-{p.get('二级场景', '')}" for p in b_customer_projects]))
                
                # 构建相似项目对信息
                pair_info = []
                for pair in similar_pairs:
                    pair_info.append(f"{pair['proj_a_code']}↔{pair['proj_b_code']}(CT:{pair['ct_similarity']:.2f},IT:{pair['it_similarity']:.2f})")
                
                abba_result = {
                    '企业A': company_a,
                    '企业B': company_b,
                    'A作为客户场景': ';'.join(a_scenes),
                    'A作为客户项目编码': ','.join([p.get('项目编码', '') for p in a_customer_projects]),
                    'A作为客户项目数量': len(a_customer_projects),
                    'B作为供应商场景': ';'.join(b_scenes),
                    'B作为供应商项目编码': ','.join([p.get('项目编码', '') for p in b_customer_projects]),
                    'B作为供应商项目数量': len(b_customer_projects),
                    'CT内容相似度': max_ct_similarity,
                    'IT内容相似度': max_it_similarity,
                    '相似项目对': ';'.join(pair_info) if pair_info else '无',
                    'ABBA风险等级': risk_level,
                    'CT阈值': ct_threshold,
                    'IT阈值': it_threshold,
                    '备注': f'发现真实的ABBA关系，共{len(similar_pairs)}对相似项目'
                }
                
                abba_results.append(abba_result)
                
                print(f"  - 最高CT相似度: {max_ct_similarity:.2f}, 最高IT相似度: {max_it_similarity:.2f}")
                print(f"  - 风险等级: {risk_level}")
                print(f"  - 相似项目对数: {len(similar_pairs)}")
    
    print(f"[完成] 共检测到 {len(abba_results)} 个真实的ABBA关系")
    return abba_results

def save_abba_results_relaxed(conn, abba_results):
    """保存ABBA检测结果到数据库（宽松版）"""
    if not abba_results:
        print("[信息] 没有检测到ABBA关系，无需保存")
        return True
    
    cursor = conn.cursor()
    try:
        print(f"[信息] 正在保存 {len(abba_results)} 条ABBA检测结果...")
        
        insert_sql = """
        INSERT INTO dict_prj_abba (
            企业A, 企业B, A作为客户场景, A作为客户项目编码, A作为客户项目数量,
            B作为供应商场景, B作为供应商项目编码, B作为供应商项目数量,
            CT内容相似度, IT内容相似度, 相似项目对,
            ABBA风险等级, CT阈值, IT阈值, 备注
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        for result in abba_results:
            cursor.execute(insert_sql, (
                result['企业A'],
                result['企业B'],
                result['A作为客户场景'],
                result['A作为客户项目编码'],
                result['A作为客户项目数量'],
                result['B作为供应商场景'],
                result['B作为供应商项目编码'],
                result['B作为供应商项目数量'],
                result['CT内容相似度'],
                result['IT内容相似度'],
                result['相似项目对'],
                result['ABBA风险等级'],
                result['CT阈值'],
                result['IT阈值'],
                result['备注']
            ))
        
        conn.commit()
        print(f"[成功] 已保存 {len(abba_results)} 条ABBA检测结果")
        return True
        
    except Exception as e:
        print(f"[错误] 保存ABBA结果失败: {e}")
        traceback.print_exc()
        conn.rollback()
        return False
    finally:
        cursor.close()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    print("="*80)
    print("宽松版疑似ABBA项目关系检测程序")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"CT相似度阈值: {args.ct_threshold*100}%")
    print(f"IT相似度阈值: {args.it_threshold*100}%")
    print("="*80)
    
    # 连接数据库
    conn = connect_to_database()
    if not conn:
        print("[错误] 无法连接数据库，程序退出")
        sys.exit(1)
    
    try:
        # 创建结果表
        if not create_abba_table_relaxed(conn):
            print("[错误] 创建结果表失败，程序退出")
            return
        
        # 获取数据
        df = get_kuanbiao_data_relaxed(conn)
        if df is None or len(df) == 0:
            print("[警告] 没有获取到有效数据")
            return
        
        # 检测ABBA关系
        abba_results = detect_abba_relaxed(df, args.ct_threshold, args.it_threshold)
        
        # 保存结果
        if save_abba_results_relaxed(conn, abba_results):
            print(f"\n[完成] 程序执行完成: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"[结果] 共检测到 {len(abba_results)} 个真实的ABBA关系")
        
    except Exception as e:
        print(f"[错误] 程序执行失败: {e}")
        traceback.print_exc()
    finally:
        conn.close()
        print("[信息] 数据库连接已关闭")

if __name__ == "__main__":
    main()
