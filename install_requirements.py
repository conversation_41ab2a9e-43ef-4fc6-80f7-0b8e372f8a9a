#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装密码加密所需的依赖包
"""

import subprocess
import sys


def install_package(package_name):
    """安装指定的包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ 成功安装: {package_name}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {package_name} - {e}")
        return False


def main():
    """安装所需依赖"""
    print("=== 安装密码加密依赖包 ===")
    
    # 需要安装的包
    packages = [
        "pycryptodome",  # 加密库
    ]
    
    success_count = 0
    for package in packages:
        print(f"\n正在安装: {package}")
        if install_package(package):
            success_count += 1
    
    print(f"\n=== 安装完成 ===")
    print(f"成功安装: {success_count}/{len(packages)} 个包")
    
    if success_count == len(packages):
        print("✅ 所有依赖安装成功！")
        print("\n现在可以运行密码加密脚本了:")
        print("python password_function.py")
    else:
        print("❌ 部分依赖安装失败，请检查网络连接或手动安装")


if __name__ == "__main__":
    main()
