-- 创建综合查询获取待办工单表
-- 对应接口：iom-app-svc/iom/api/wo/getTodo

DROP TABLE IF EXISTS `dict_zonghe_getTodo`;

CREATE TABLE IF NOT EXISTS `dict_zonghe_getTodo` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  
  -- 入参字段（来源于源数据表）
  `INPUT_LOGIN_NO` varchar(100) DEFAULT NULL COMMENT '入参-登录用户名',
  `INPUT_STEP_ID` varchar(100) DEFAULT NULL COMMENT '入参-步骤ID',
  `INPUT_PROD_ID` varchar(100) DEFAULT NULL COMMENT '入参-产品ID',
  `INPUT_PAGE_SIZE` int DEFAULT NULL COMMENT '入参-页大小',
  `INPUT_PAGE_NUM` int DEFAULT NULL COMMENT '入参-页码',
  `INPUT_OP_CODES` varchar(200) DEFAULT NULL COMMENT '入参-操作代码',
  
  -- 响应数据字段（基于接口返回的JSON结构）
  `TODO_ID` varchar(200) DEFAULT NULL COMMENT 'TODO_ID',
  `TODO_TYPE` varchar(200) DEFAULT NULL COMMENT 'TODO_TYPE',
  `TODO_TYPE_DESC` text DEFAULT NULL COMMENT 'TODO_TYPE_DESC',
  `TODO_STATUS` varchar(200) DEFAULT NULL COMMENT 'TODO_STATUS',
  `TODO_STATUS_DESC` text DEFAULT NULL COMMENT 'TODO_STATUS_DESC',
  `PROJECT_ID` varchar(200) DEFAULT NULL COMMENT 'PROJECT_ID',
  `PROD_ID` varchar(200) DEFAULT NULL COMMENT 'PROD_ID',
  `PROD_NAME` text DEFAULT NULL COMMENT 'PROD_NAME',
  `STEP_ID` varchar(200) DEFAULT NULL COMMENT 'STEP_ID',
  `STEP_NAME` text DEFAULT NULL COMMENT 'STEP_NAME',
  `CREATE_TIME` varchar(50) DEFAULT NULL COMMENT 'CREATE_TIME',
  `ASSIGN_TIME` varchar(50) DEFAULT NULL COMMENT 'ASSIGN_TIME',
  `DUE_TIME` varchar(50) DEFAULT NULL COMMENT 'DUE_TIME',
  `ASSIGN_STAFF` varchar(200) DEFAULT NULL COMMENT 'ASSIGN_STAFF',
  `ASSIGN_STAFF_NAME` text DEFAULT NULL COMMENT 'ASSIGN_STAFF_NAME',
  `TODO_DESC` text DEFAULT NULL COMMENT 'TODO_DESC',
  `PRIORITY` varchar(200) DEFAULT NULL COMMENT 'PRIORITY',
  `PRIORITY_DESC` text DEFAULT NULL COMMENT 'PRIORITY_DESC',
  `OP_CODES` varchar(200) DEFAULT NULL COMMENT 'OP_CODES',
  
  -- 系统字段
  `RUN_IP` varchar(50) DEFAULT NULL COMMENT '运行IP',
  `REQUEST_ID` varchar(100) DEFAULT NULL COMMENT '请求ID',
  `RETURN_CODE` varchar(10) DEFAULT NULL COMMENT '返回码',
  `RETURN_MSG` varchar(200) DEFAULT NULL COMMENT '返回消息',
  `USER_MSG` varchar(200) DEFAULT NULL COMMENT '用户消息',
  `DETAIL_MSG` text DEFAULT NULL COMMENT '详细消息',
  
  -- 元数据字段
  `import_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_import_time` (`import_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='综合查询表';
