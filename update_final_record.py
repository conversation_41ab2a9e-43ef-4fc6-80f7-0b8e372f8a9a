#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
更新最后一条记录的状态和回答
"""

import sys
import os
import pymysql

sys.path.append(os.path.join(os.path.dirname(__file__), 'dict_romte'))
from dict_romte.config import get_db_config

def update_final_record():
    """更新最后一条记录"""
    try:
        DB_CONFIG = get_db_config('default')
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 更新最后一条记录
        update_sql = """
        UPDATE dict_conversation_records 
        SET response = %s, 
            operations = %s,
            files_created = %s,
            status = %s
        WHERE sequence_no = 9 
        AND session_id = (
            SELECT session_id FROM (
                SELECT session_id FROM dict_conversation_records 
                ORDER BY create_time DESC LIMIT 1
            ) as latest
        )
        """
        
        response_text = """成功创建对话记录表和项目总结表，完整保存了用户指令、AI回答、操作记录、创建文件等信息。
包含9条对话记录和1条项目总结，记录了从发现86个接口到完成全部开发的完整过程。
所有记录已保存到数据库，可通过SQL查询获取详细信息。"""
        
        operations_text = """创建dict_conversation_records表和dict_project_summary表，保存完整对话历史和项目总结，包括用户指令、AI回答、操作记录、文件创建记录等"""
        
        files_created_text = """save_conversation_to_db.py, verify_conversation_records.py, update_final_record.py, dict_conversation_records表, dict_project_summary表"""
        
        cursor.execute(update_sql, (response_text, operations_text, files_created_text, 'SUCCESS'))
        conn.commit()
        
        cursor.close()
        conn.close()
        
        print("✅ 最后一条记录更新成功")
        return True
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        return False

if __name__ == "__main__":
    update_final_record()
