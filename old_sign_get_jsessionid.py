import requests

url = "http://dict.gmcc.net:30722/analysisService/gdyddict/yyfxSSO"

params = {
  'cityId': "1000008498",
  'limitSec': "300",
  'modId': "2400015082",
  'oaAcct': "zhengdewen",
  'postId': "10061760",
  'postOrgId': "1000010331",
  'sysId': "gdydDict",
  'sysUserCode': "zhengdewen",
  'timestamp': "20250505224448",
  'sign': "be47b5754b53ae34a773be72f0f5b3d0"
}

headers = {
  'Host': "dict.gmcc.net:30722",
  'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  'Accept': "application/json, text/plain, */*",
  'Accept-Encoding': "gzip, deflate",
  'Pragma': "no-cache",
  'Cache-Control': "no-cache",
  'Referer': "http://dict.gmcc.net:30722/dictWeb/iframeTemplate/iframeAnalysis?index=1&title=%E7%AD%BE%E7%BA%A6%E9%A1%B9%E7%9B%AE%E6%98%8E%E7%BB%86",
  'Accept-Language': "zh-CN,zh;q=0.9",
  'Cookie': "BSS-SESSION=NGZhMDQ4ZTUtMjI5ZC00YzU0LThmMGQtZTlhMjkwZjI2ZmNi; "
}

response = requests.get(url, params=params, headers=headers)

print(response.headers)