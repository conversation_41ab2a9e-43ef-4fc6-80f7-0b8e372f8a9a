-- 创建临时表存储可能的拆分立项项目对
CREATE TEMPORARY TABLE potential_split_projects AS
SELECT 
    t1.`项目名称` AS project1,
    t2.`项目名称` AS project2,
    t1.`客户名称` AS customer,
    t1.`签约时间` AS sign_date1,
    t2.`签约时间` AS sign_date2,
    ABS(DATEDIFF(STR_TO_DATE(t1.`签约时间`, '%Y/%m/%d'), 
                STR_TO_DATE(t2.`签约时间`, '%Y/%m/%d'))) AS date_diff
FROM 
    BB t1
JOIN 
    BB t2 ON t1.`客户名称` = t2.`客户名称` 
        AND t1.`项目名称` <> t2.`项目名称`
WHERE 
    -- 签约时间相近（1个月内）
    ABS(DATEDIFF(STR_TO_DATE(t1.`签约时间`, '%Y/%m/%d'), 
                STR_TO_DATE(t2.`签约时间`, '%Y/%m/%d'))) <= 30;

-- 创建临时函数计算Jaccard相似度
DELIMITER //
CREATE FUNCTION IF NOT EXISTS jaccard_similarity(str1 TEXT, str2 TEXT) 
RETURNS FLOAT DETERMINISTIC
BEGIN
    DECLARE words1 TEXT;
    DECLARE words2 TEXT;
    DECLARE common_count INT;
    DECLARE total_count INT;
    
    -- 将文本转换为小写并分词（简化处理，以空格分隔）
    SET words1 = LOWER(str1);
    SET words2 = LOWER(str2);
    
    -- 计算共有词的数量（简化实现）
    SET common_count = (
        LENGTH(words1) - LENGTH(REPLACE(words1, words2, '')) + 
        LENGTH(words2) - LENGTH(REPLACE(words2, words1, ''))
    ) / 2;
    
    -- 计算总词数
    SET total_count = LENGTH(words1) + LENGTH(words2) - common_count;
    
    -- 计算Jaccard相似度
    IF total_count = 0 THEN
        RETURN 0;
    ELSE
        RETURN common_count / total_count;
    END IF;
END //
DELIMITER ;

-- 查询结果：找出客户相同、签约时间相近且建设内容相似的项目对
SELECT 
    p.project1,
    p.project2,
    p.customer AS `收入侧客户`,
    p.sign_date1 AS `项目1签约时间`,
    p.sign_date2 AS `项目2签约时间`,
    p.date_diff AS `签约时间差(天)`,
    t1.`项目建设内容及方案简介（CT）` AS `项目1_CT内容`,
    t2.`项目建设内容及方案简介（CT）` AS `项目2_CT内容`,
    t1.`项目建设内容及方案简介（IT）` AS `项目1_IT内容`,
    t2.`项目建设内容及方案简介（IT）` AS `项目2_IT内容`,
    -- 计算CT内容相似度
    ROUND(
        jaccard_similarity(
            IFNULL(t1.`项目建设内容及方案简介（CT）`, ''), 
            IFNULL(t2.`项目建设内容及方案简介（CT）`, '')
        ) * 100, 
    2) AS `CT内容相似度(%)`,
    -- 计算IT内容相似度
    ROUND(
        jaccard_similarity(
            IFNULL(t1.`项目建设内容及方案简介（IT）`, ''), 
            IFNULL(t2.`项目建设内容及方案简介（IT）`, '')
        ) * 100, 
    2) AS `IT内容相似度(%)`,
    -- 综合判断
    CASE 
        WHEN (
            -- 使用Jaccard相似度判断，阈值设为30%
            jaccard_similarity(
                IFNULL(t1.`项目建设内容及方案简介（CT）`, ''), 
                IFNULL(t2.`项目建设内容及方案简介（CT）`, '')
            ) > 0.3
            OR
            jaccard_similarity(
                IFNULL(t1.`项目建设内容及方案简介（IT）`, ''), 
                IFNULL(t2.`项目建设内容及方案简介（IT）`, '')
            ) > 0.3
        ) THEN '是'
        ELSE '否'
    END AS `疑似拆分立项`
FROM 
    potential_split_projects p
JOIN 
    BB t1 ON p.project1 = t1.`项目名称`
JOIN 
    BB t2 ON p.project2 = t2.`项目名称`
ORDER BY 
    p.customer, p.date_diff;

-- 删除临时函数和临时表
DROP FUNCTION IF EXISTS jaccard_similarity;
DROP TEMPORARY TABLE IF EXISTS potential_split_projects;