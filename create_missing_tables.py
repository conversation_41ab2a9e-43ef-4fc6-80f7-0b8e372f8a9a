#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
为缺失的爬虫程序创建对应的数据表
"""

import pymysql
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'dict_romte'))
from dict_romte.config import get_db_config

def get_missing_tables():
    """获取缺失的数据表列表"""
    try:
        # 检查数据库中的表
        DB_CONFIG = get_db_config('default')
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        cursor.execute("SHOW TABLES LIKE 'dict_zonghe_%'")
        db_tables = [row[0].lower() for row in cursor.fetchall()]
        
        cursor.close()
        conn.close()
        
        # 检查爬虫程序文件
        py_files = [f for f in os.listdir('.') if f.startswith('dict_zonghe_') and f.endswith('.py')]
        
        # 找出缺失的表
        missing_tables = []
        for py_file in py_files:
            table_name = py_file.replace('.py', '').lower()
            if table_name not in db_tables:
                missing_tables.append(table_name)
        
        return missing_tables
        
    except Exception as e:
        print(f"[错误] 获取缺失表列表失败: {e}")
        return []

def create_standard_table(table_name):
    """创建标准的数据表结构"""
    try:
        DB_CONFIG = get_db_config('default')
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 标准表结构SQL
        create_sql = f"""
        CREATE TABLE IF NOT EXISTS `{table_name}` (
          `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
          
          -- 入参字段
          `INPUT_LOGIN_NO` varchar(100) DEFAULT NULL COMMENT '入参-登录用户名',
          `INPUT_PROJECT_ID` varchar(100) DEFAULT NULL COMMENT '入参-项目ID',
          
          -- 响应数据字段
          `RESPONSE_DATA` text DEFAULT NULL COMMENT '接口返回的完整JSON数据',
          `RESPONSE_COUNT` int DEFAULT 0 COMMENT '返回数据条数',
          
          -- 系统字段
          `RUN_IP` varchar(50) DEFAULT NULL COMMENT '运行IP',
          `REQUEST_ID` varchar(100) DEFAULT NULL COMMENT '请求ID',
          `RETURN_CODE` varchar(10) DEFAULT NULL COMMENT '返回码',
          `RETURN_MSG` varchar(200) DEFAULT NULL COMMENT '返回消息',
          `USER_MSG` varchar(200) DEFAULT NULL COMMENT '用户消息',
          `DETAIL_MSG` text DEFAULT NULL COMMENT '详细消息',
          
          -- 元数据字段
          `import_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
          `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          
          PRIMARY KEY (`id`),
          KEY `idx_input_project_id` (`INPUT_PROJECT_ID`),
          KEY `idx_import_time` (`import_time`),
          KEY `idx_return_code` (`RETURN_CODE`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='综合查询数据表-{table_name}';
        """
        
        cursor.execute(create_sql)
        conn.commit()
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"[错误] 创建表 {table_name} 失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 80)
    print("为缺失的爬虫程序创建对应的数据表")
    print("=" * 80)
    
    # 获取缺失的表
    missing_tables = get_missing_tables()
    
    if not missing_tables:
        print("✅ 所有爬虫程序都有对应的数据表")
        return
    
    print(f"需要创建 {len(missing_tables)} 个数据表")
    
    # 询问用户是否继续
    response = input(f"\\n是否继续创建这些数据表？(y/n): ")
    if response.lower() != 'y':
        print("[取消] 用户取消操作")
        return
    
    # 批量创建表
    success_count = 0
    error_count = 0
    
    for i, table_name in enumerate(missing_tables, 1):
        print(f"[{i}/{len(missing_tables)}] 创建表: {table_name}...", end=" ")
        
        if create_standard_table(table_name):
            print("✅")
            success_count += 1
        else:
            print("❌")
            error_count += 1
    
    print(f"\\n=" * 80)
    print("数据表创建完成")
    print("=" * 80)
    print(f"成功创建: {success_count} 个表")
    print(f"创建失败: {error_count} 个表")
    print(f"总计: {len(missing_tables)} 个表")
    
    if error_count == 0:
        print("\\n🎉 所有数据表创建成功！")
        print("现在可以运行: python project_status_check.py 检查状态")
    else:
        print(f"\\n⚠️ 有 {error_count} 个表创建失败，请检查错误信息")

if __name__ == "__main__":
    main()
