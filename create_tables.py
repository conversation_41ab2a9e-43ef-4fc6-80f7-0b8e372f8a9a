#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
创建数据表脚本
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'dict_romte'))
from dict_romte.config import get_db_config
import pymysql

def execute_sql_file(sql_file):
    """执行SQL文件"""
    DB_CONFIG = get_db_config('default')
    
    try:
        # 读取SQL文件
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 连接数据库
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 分割SQL语句并执行
        sql_statements = sql_content.split(';')
        
        for sql in sql_statements:
            sql = sql.strip()
            if sql and not sql.startswith('--'):
                try:
                    cursor.execute(sql)
                    print(f"[成功] 执行SQL: {sql[:50]}...")
                except Exception as e:
                    print(f"[错误] 执行SQL失败: {sql[:50]}... 错误: {e}")
        
        conn.commit()
        cursor.close()
        conn.close()
        print(f"[完成] SQL文件 {sql_file} 执行完成")
        return True
        
    except Exception as e:
        print(f"[错误] 执行SQL文件失败: {e}")
        return False

def main():
    """主函数"""
    # 获取所有SQL文件
    sql_files = []
    for file in os.listdir('.'):
        if file.startswith('create_dict_zonghe_') and file.endswith('.sql'):
            sql_files.append(file)

    # 排序确保一致的执行顺序
    sql_files.sort()
    
    for sql_file in sql_files:
        if os.path.exists(sql_file):
            print(f"\n正在执行: {sql_file}")
            execute_sql_file(sql_file)
        else:
            print(f"[警告] 文件不存在: {sql_file}")

if __name__ == "__main__":
    main()
