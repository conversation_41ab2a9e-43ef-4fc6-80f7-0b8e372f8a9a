#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查kuanbiao视图的字段结构
"""

import pymysql

DB_CONFIG = {
    'host': '*************',
    'port': 12136,
    'user': 'root',
    'password': '5eb9a11916e3a66d',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

try:
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    print('=== 检查kuanbiao视图的字段 ===')
    cursor.execute('DESCRIBE kuanbiao')
    columns = cursor.fetchall()
    
    target_fields = [
        '签约上报日期', '项目编码', '项目名称', '合同含税金额（万元）', 
        '收入侧客户', '收入侧合同编码', '项目建设内容及方案简介（CT）', 
        '项目建设内容及方案简介（IT）', '一级场景', '二级场景', 
        '后向合同签约时间', '成本侧供应商'
    ]
    
    existing_fields = []
    missing_fields = []
    
    column_names = [col[0] for col in columns]
    
    for field in target_fields:
        if field in column_names:
            existing_fields.append(field)
        else:
            missing_fields.append(field)
    
    print('\n=== 存在的字段 ===')
    for field in existing_fields:
        print(f'✓ {field}')
    
    print('\n=== 缺失的字段 ===')
    for field in missing_fields:
        print(f'✗ {field}')
    
    # 检查成本侧供应商数据情况
    print('\n=== 检查成本侧供应商数据 ===')
    cursor.execute('SELECT COUNT(*) FROM kuanbiao WHERE 成本侧供应商 IS NOT NULL AND 成本侧供应商 != ""')
    supplier_count = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM kuanbiao')
    total_count = cursor.fetchone()[0]
    
    print(f'kuanbiao视图总记录数: {total_count}')
    print(f'有成本侧供应商的记录数: {supplier_count} ({supplier_count/total_count*100:.1f}%)')
    
    if supplier_count > 0:
        print('\n=== 成本侧供应商样本数据 ===')
        cursor.execute('SELECT 项目编码, 收入侧客户, 成本侧供应商, 一级场景, 二级场景 FROM kuanbiao WHERE 成本侧供应商 IS NOT NULL AND 成本侧供应商 != "" LIMIT 5')
        results = cursor.fetchall()
        for row in results:
            print(f'项目: {row[0]}, 客户: {row[1]}, 供应商: {row[2]}, 一级场景: {row[3]}, 二级场景: {row[4]}')
    
    # 检查一级场景和二级场景数据
    print('\n=== 检查场景数据 ===')
    cursor.execute('SELECT COUNT(*) FROM kuanbiao WHERE 一级场景 IS NOT NULL AND 一级场景 != ""')
    scene1_count = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM kuanbiao WHERE 二级场景 IS NOT NULL AND 二级场景 != ""')
    scene2_count = cursor.fetchone()[0]
    
    print(f'有一级场景的记录数: {scene1_count} ({scene1_count/total_count*100:.1f}%)')
    print(f'有二级场景的记录数: {scene2_count} ({scene2_count/total_count*100:.1f}%)')
    
    if scene1_count > 0:
        print('\n=== 一级场景分布 ===')
        cursor.execute('SELECT 一级场景, COUNT(*) FROM kuanbiao WHERE 一级场景 IS NOT NULL AND 一级场景 != "" GROUP BY 一级场景 ORDER BY COUNT(*) DESC LIMIT 10')
        scene1_stats = cursor.fetchall()
        for scene, count in scene1_stats:
            print(f'  {scene}: {count}条')
    
    if scene2_count > 0:
        print('\n=== 二级场景分布 ===')
        cursor.execute('SELECT 二级场景, COUNT(*) FROM kuanbiao WHERE 二级场景 IS NOT NULL AND 二级场景 != "" GROUP BY 二级场景 ORDER BY COUNT(*) DESC LIMIT 10')
        scene2_stats = cursor.fetchall()
        for scene, count in scene2_stats:
            print(f'  {scene}: {count}条')
    
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f'错误: {e}')
