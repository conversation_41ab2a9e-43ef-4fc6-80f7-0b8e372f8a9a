import requests
import json
import pymysql
import os
import subprocess
import sys  # 添加sys模块导入
from datetime import datetime

# 数据库连接配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'cmcc12345',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

# API请求配置
url = "http://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp//preparation/queryProjectStart"

def run_getcookie():
    """运行getcookie.py获取新的Cookie"""
    print("🚀 开始获取新的登录Cookie...")
    try:
        # 先检查并安装必要的库
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "requests"], check=True)
            print("✅ requests库安装成功")
            
            # 检查并安装ddddocr库（getcookie.py中需要）
            try:
                import ddddocr
            except ImportError:
                print("⚠️ 检测到未安装ddddocr库，正在尝试安装...")
                subprocess.run([sys.executable, "-m", "pip", "install", "ddddocr"], check=True)
                print("✅ ddddocr库安装成功")
        except Exception as e:
            print(f"⚠️ 安装依赖库失败: {e}")
        
        # 运行getcookie.py
        result = subprocess.run(["python", "getcookie.py"], check=True)
        if result.returncode == 0:
            print("✅ 成功获取新的Cookie")
            return True
        else:
            print("❌ 获取Cookie失败")
            return False
    except Exception as e:
        print(f"❌ 运行getcookie.py时出错: {e}")
        return False

def load_cookies(filename="cookies.txt"):
    """从cookies.txt文件中加载cookie，支持JSON格式和简单键值对格式"""
    cookies = {}
    try:
        if os.path.exists(filename):
            with open(filename, "r", encoding="utf-8") as f:
                content = f.read().strip()

                # 判断是否为JSON格式
                if content.startswith('{') and content.endswith('}'):
                    # JSON格式解析
                    data = json.loads(content)
                    if "cookies" in data and isinstance(data["cookies"], list):
                        for cookie in data["cookies"]:
                            if "name" in cookie and "value" in cookie:
                                cookies[cookie["name"]] = cookie["value"]
                        print(f"[信息] 已从{filename}加载Cookie (JSON格式，共{len(cookies)}个)")
                    else:
                        print(f"[错误] JSON格式不正确，缺少cookies数组")
                        exit(1)
                else:
                    # 简单键值对格式解析
                    for line in content.split('\n'):
                        if "=" in line:
                            name, value = line.strip().split("=", 1)
                            cookies[name] = value
                    print(f"[信息] 已从{filename}加载Cookie (键值对格式，共{len(cookies)}个)")

            return cookies
        else:
            print(f"[警告] Cookie文件{filename}不存在，请先运行getcookie.py获取Cookie")
            exit(1)
    except json.JSONDecodeError as e:
        print(f"[错误] JSON格式解析失败: {e}")
        exit(1)
    except Exception as e:
        print(f"[错误] 加载Cookie失败: {e}")
        exit(1)

def get_project_codes():
    """从MySQL获取项目编码列表"""
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    try:
        # 从开工时间视图获取项目编码
        cursor.execute("SELECT 项目编码 FROM 开工时间视图")
        project_codes = [row[0] for row in cursor.fetchall()]
        print(f"获取到 {len(project_codes)} 个项目编码")
        return project_codes
    except Exception as e:
        print(f"获取项目编码失败: {e}")
        return []
    finally:
        cursor.close()
        conn.close()

def get_start_date(project_code, cookies_dict):
    """获取项目的开工日期"""
    # 构建请求头，使用从cookies.txt加载的Cookie
    cookie_str = "; ".join([f"{name}={value}" for name, value in cookies_dict.items()])
    
    headers = {
        'Host': "dict.gmcc.net:30722",
        'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        'Accept': "application/json, text/plain, */*",
        'Accept-Encoding': "gzip, deflate",
        'Content-Type': "application/json",
        'Pragma': "no-cache",
        'Cache-Control': "no-cache",
        'Origin': "http://dict.gmcc.net:30722",
        'Referer': "http://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/",
        'Accept-Language': "zh-CN,zh;q=0.9",
        'Cookie': cookie_str
    }
    
    payload = {
        "ROOT": {
            "HEADER": {
                "OPR_INFO": {
                    "LOGIN_NO": "zhengdewen"   # cookies_dict.get("userCode", "")
                }
            },
            "BODY": {
                "PROJECT_ID": project_code
            }
        }
    }
    
    try:
        response = requests.post(url, data=json.dumps(payload), headers=headers)
        data = response.json()
        print(f"响应数据data: {data}")
        # 从响应中提取开工日期
        start_date = data.get('ROOT', {}).get('BODY', {}).get('OUT_DATA', {}).get('PLAN_START_DATE', '')
        return start_date
    except Exception as e:
        print(f"获取项目 {project_code} 的开工日期失败: {e}")
        return None

def save_start_date(project_code, start_date):
    """保存项目开工日期到数据库"""
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    current_date = datetime.now().strftime("%Y-%m-%d")
    
    try:
        # 使用INSERT INTO语句，按照已有表结构插入数据
        cursor.execute("""
        INSERT INTO project_start_date (项目编码, 开工时间, 查询日期)
        VALUES (%s, %s, %s)
        ON DUPLICATE KEY UPDATE 开工时间 = %s, 查询日期 = %s
        """, (project_code, start_date, current_date, start_date, current_date))
        conn.commit()
    except Exception as e:
        conn.rollback()
        print(f"保存项目 {project_code} 的开工日期失败: {e}")
    finally:
        cursor.close()
        conn.close()

def main():
    """主函数，先获取新Cookie，再处理开工日期"""
    print(f"📊 开始执行开工日期数据采集 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 步骤1: 运行getcookie.py，获取新cookie
    if not run_getcookie():
        print("[错误] 获取Cookie失败，流程终止")
        # 尝试使用现有Cookie继续
        print("⚠️ 尝试使用现有Cookie继续...")
        if not os.path.exists("cookies.txt"):
            print("❌ cookies.txt文件不存在，无法继续")
            return
    
    # 加载Cookie
    cookies_dict = load_cookies()
    if not cookies_dict:
        print("❌ 无法加载Cookie，流程终止")
        return
    print(f"已加载Cookie: {len(cookies_dict)}个")
    
    # 获取所有项目编码
    project_codes = get_project_codes()
    print(f"获取到 {len(project_codes)} 个项目编码")
    
    # 获取并保存每个项目的开工日期
    for i, project_code in enumerate(project_codes):
        print(f"处理项目 {i+1}/{len(project_codes)}: {project_code}")
        start_date = get_start_date(project_code, cookies_dict)
        if start_date:
            save_start_date(project_code, start_date)
            print(f"项目 {project_code} 的开工日期: {start_date}")
        else:
            print(f"项目 {project_code} 未获取到开工日期")
    
    print(f"✅ 开工日期数据采集完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()

