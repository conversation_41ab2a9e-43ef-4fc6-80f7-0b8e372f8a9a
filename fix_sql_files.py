#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复SQL文件中的格式化字符串错误
"""

import os
import re

def fix_sql_file(filename):
    """修复单个SQL文件"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复格式化字符串错误
        content = re.sub(r"综合查询\{interface\['chinese_name'\]\}表", "综合查询表", content)
        
        # 写回文件
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"[修复] {filename}")
        return True
    except Exception as e:
        print(f"[错误] 修复 {filename} 失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("修复SQL文件中的格式化字符串错误")
    print("=" * 60)
    
    # 需要修复的文件列表
    files_to_fix = [
        'create_dict_zonghe_saleBiddingInfo_table.sql',
        'create_dict_zonghe_qryWoListByProject_table.sql',
        'create_dict_zonghe_getTodo_table.sql',
        'create_dict_zonghe_queryProjectProdprcDict_table.sql',
        'create_dict_zonghe_batchLoadCodeList_table.sql',
        'create_dict_zonghe_loadCodeList_table.sql'
    ]
    
    success_count = 0
    for filename in files_to_fix:
        if os.path.exists(filename):
            if fix_sql_file(filename):
                success_count += 1
        else:
            print(f"[警告] 文件不存在: {filename}")
    
    print(f"\n[完成] 成功修复 {success_count} 个文件")

if __name__ == "__main__":
    main()
